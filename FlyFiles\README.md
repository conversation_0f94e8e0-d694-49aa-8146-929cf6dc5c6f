# FlyFiles - Dansk Fildelingsplatform

FlyFiles er en moderne, dansk fildelingsplatform inspireret af WeTransfer, bygget med Next.js 15, TypeScript, og MongoDB Atlas. Platformen understøtter både gæstebrugere og registrerede brugere med forskellige abonnementsplaner.

## 🎯 Funktioner

### ✅ Implementeret
- **🔐 Autentificering**: NextAuth.js med Google OAuth
- **📱 Responsivt UI**: Tailwind CSS med dark mode support
- **👤 Brugerroller**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Upgrade 1, Upgrade 2
- **📊 Dashboard**: Upload-forbrug tracking og fil-management
- **💰 Prissætning**: Komplet prisside med plan sammenligning
- **🗄️ Database**: MongoDB Atlas integration med TypeScript modeller
- **🌐 API Routes**: RESTful endpoints for alle operationer
- **🧹 Oprydning**: Cron endpoint til automatisk filsletning

### 🔄 Forberedt til integration
- **📁 Fil opbevaring**: Klar til <PERSON>j, Backblaze eller anden cloud storage
- **💳 Betalinger**: Database struktur klar til Stripe integration
- **📧 E-mail**: Grundlag for notifikationer og deling
- **🔗 Fil deling**: Link generering og adgangskontrol

## 🏗️ Teknisk Stack

- **Framework**: Next.js 15 (App Router)
- **Sprog**: TypeScript
- **Styling**: Tailwind CSS
- **Database**: MongoDB Atlas
- **Autentificering**: NextAuth.js
- **UI Komponenter**: Custom components med Lucide React ikoner

## 📁 Projekt Struktur

```
FlyFiles/
├── src/app/
│   ├── (auth)/
│   │   ├── login/                 # Login side
│   │   └── dashboard/             # Bruger dashboard
│   ├── api/
│   │   ├── auth/                  # NextAuth endpoints
│   │   ├── files/                 # Fil CRUD operationer
│   │   ├── upload/                # Upload håndtering
│   │   ├── download/              # Download tracking
│   │   ├── user/                  # Bruger statistikker
│   │   └── cron/                  # Scheduled tasks
│   ├── components/
│   │   ├── ui/                    # Genanvendelige UI komponenter
│   │   ├── auth/                  # Autentificering komponenter
│   │   └── dashboard/             # Dashboard specifikke komponenter
│   ├── lib/
│   │   ├── mongodb.ts             # Database forbindelse og konfiguration
│   │   ├── auth.ts                # NextAuth konfiguration
│   │   ├── types.ts               # TypeScript interfaces
│   │   └── utils.ts               # Utility funktioner
│   ├── pricing/                   # Prisside
│   ├── layout.tsx                 # Root layout med navigation
│   └── page.tsx                   # Homepage
├── public/                        # Statiske filer
└── .env.example                   # Environment variables eksempel
```

## 🚀 Opsætning

### 1. Clone repository og installer dependencies

```bash
git clone <repository-url>
cd FlyFiles
npm install
```

### 2. Environment Variables

Kopier `.env.example` til `.env.local` og udfyld:

```env
# MongoDB Atlas Connection
MONGODB_URI=mongodb+srv://username:<EMAIL>/flyfiles?retryWrites=true&w=majority

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here

# Google OAuth (hent fra Google Cloud Console)
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
```

### 3. Google OAuth Opsætning

1. Gå til [Google Cloud Console](https://console.cloud.google.com/)
2. Opret nyt projekt eller vælg eksisterende
3. Aktiver Google+ API
4. Opret OAuth 2.0 credentials
5. Tilføj `http://localhost:3000/api/auth/callback/google` som callback URL

### 4. MongoDB Atlas Opsætning

1. Opret konto på [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Opret nyt cluster
3. Opret database user
4. Whitelist din IP adresse
5. Hent connection string

### 5. Start udviklingsserver

```bash
npm run dev
```

Åbn [http://localhost:3000](http://localhost:3000) i din browser.

## 📊 Database Struktur

### Collections

#### `users`
```typescript
{
  _id: ObjectId,
  email: string,
  name: string,
  image?: string,
  plan: 'guest' | 'free' | 'upgrade1' | 'upgrade2',
  createdAt: Date,
  updatedAt: Date,
  usage: {
    monthly: number,  // bytes
    weekly: number,   // bytes
    session?: number  // for guests
  },
  subscription?: {
    id?: string,
    status: 'active' | 'inactive' | 'past_due',
    currentPeriodStart: Date,
    currentPeriodEnd: Date
  }
}
```

#### `files`
```typescript
{
  _id: ObjectId,
  filename: string,         // internal ID
  originalName: string,     // user's filename
  mimeType: string,
  size: number,            // bytes
  ownerId?: string,        // null for guests
  uploadDate: Date,
  expiryDate: Date,
  downloadCount: number,
  downloadLimit: number,   // -1 for unlimited
  isActive: boolean,
  storageProvider?: string,
  storageKey?: string,
  sessionId?: string       // for guest uploads
}
```

#### `sessions` (for guests)
```typescript
{
  _id: ObjectId,
  sessionId: string,
  totalUploaded: number,  // bytes
  createdAt: Date,
  expiresAt: Date,
  files: string[]         // file IDs
}
```

#### `downloadLogs`
```typescript
{
  _id: ObjectId,
  fileId: string,
  downloadedAt: Date,
  ip: string,
  userAgent: string
}
```

## 🎛️ API Endpoints

### Autentificering
- `GET/POST /api/auth/[...nextauth]` - NextAuth endpoints

### Filer
- `GET /api/files` - List brugers filer
- `POST /api/files` - Opret fil metadata
- `GET /api/download/[fileId]` - Download fil med tracking

### Bruger
- `GET /api/user/usage` - Hent forbrug statistikker

### Upload
- `POST /api/upload` - Upload endpoint (klar til storage integration)

### Vedligeholdelse
- `POST /api/cron/cleanup` - Ryd op i udløbne filer

## 💎 Abonnementsplaner

| Plan | Upload Grænse | Opbevaring | Downloads | Pris |
|------|---------------|------------|-----------|------|
| **Gæst** | 250MB/session | 7 dage | Ubegrænset | Gratis |
| **Gratis** | 15GB/måned | 10 dage | Konfigurerbare grænser | Gratis |
| **Upgrade 1** | 15GB/uge | 14 dage | Grænser + statistikker | 5 kr/måned |
| **Upgrade 2** | 50GB/uge | 30 dage | Ubegrænset + analytics | 25 kr/måned |

## 🔮 Næste Skridt

### Kritiske integrationer
1. **File Storage**: Integrer Storj, Backblaze, eller AWS S3
2. **Betalinger**: Implementer Stripe for abonnementer
3. **E-mail**: Notifikationer og fil-deling links

### Forbedringer
1. **Fil deling**: Generér og administrer deling links
2. **Batch operations**: Upload flere filer samtidig
3. **Advanced analytics**: Detaljerede download statistikker
4. **Mobile app**: React Native eller PWA
5. **API rate limiting**: Beskyt mod misbrug

## 🛡️ Sikkerhed

- HTTPS påkrævet i produktion
- File type validering
- Upload størrelse begrænsninger
- Rate limiting (skal implementeres)
- GDPR compliance (automatisk fil sletning)
- Input sanitization

## 🧪 Test

```bash
# Run tests (skal implementeres)
npm test

# Type checking
npm run type-check

# Linting
npm run lint
```

## 📦 Deployment

### Vercel (Anbefalet)
1. Push til GitHub
2. Connect repository til Vercel
3. Tilføj environment variables
4. Deploy!

### Andre platforme
- Netlify
- Railway
- Digital Ocean

## 🤝 Bidrag

1. Fork repository
2. Opret feature branch
3. Commit ændringer
4. Push til branch
5. Åbn Pull Request

## 📄 Licens

Dette projekt er licenseret under MIT License.

## 🔗 Links

- [Next.js Documentation](https://nextjs.org/docs)
- [NextAuth.js Documentation](https://next-auth.js.org/)
- [MongoDB Atlas](https://www.mongodb.com/atlas)
- [Tailwind CSS](https://tailwindcss.com/)
- [TypeScript](https://www.typescriptlang.org/)

---

**FlyFiles** - Dansk fildelingsplatform bygget med ❤️ til danske brugere
