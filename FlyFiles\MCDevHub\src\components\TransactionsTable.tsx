import React, { useState, useEffect } from 'react';
import { FaExchangeAlt, FaChevronLeft, FaChevronRight, FaSpinner } from 'react-icons/fa';

interface Transaction {
  _id: string;
  sellerId: string;
  buyerId: string;
  buyerName: string;
  productId: string;
  productName: string;
  amount: number;
  createdAt: string;
  stripeSessionId: string;
}

interface PaginationProps {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

function Pagination({ total, page, limit, totalPages, onPageChange }: PaginationProps) {
  return (
    <div className="flex items-center justify-between py-3 border-t border-gray-200 mt-4">
      <div className="flex-1 flex justify-between sm:hidden">
        <button
          onClick={() => onPageChange(page - 1)}
          disabled={page === 1}
          className={`${page === 1 ? 'opacity-50 cursor-not-allowed' : ''} relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white`}
        >
          Forrige
        </button>
        <button
          onClick={() => onPageChange(page + 1)}
          disabled={page === totalPages}
          className={`${page === totalPages ? 'opacity-50 cursor-not-allowed' : ''} ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white`}
        >
          Næste
        </button>
      </div>
      
      <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p className="text-sm text-gray-700">
            Viser <span className="font-medium">{((page - 1) * limit) + 1}</span> til <span className="font-medium">{Math.min(page * limit, total)}</span> af <span className="font-medium">{total}</span> transaktioner
          </p>
        </div>
        <div>
          <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <button
              onClick={() => onPageChange(page - 1)}
              disabled={page === 1}
              className={`${page === 1 ? 'opacity-50 cursor-not-allowed' : ''} relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500`}
            >
              <span className="sr-only">Forrige</span>
              <FaChevronLeft className="h-4 w-4" />
            </button>
            
            {/* Generate page numbers - show current, prev, next and first/last */}
            {Array.from({ length: totalPages }).map((_, i) => {
              const pageNum = i + 1;
              // Only show first, last, current, and pages within 1 of current
              if (
                pageNum === 1 || 
                pageNum === totalPages || 
                pageNum === page || 
                pageNum === page - 1 || 
                pageNum === page + 1
              ) {
                return (
                  <button
                    key={pageNum}
                    onClick={() => onPageChange(pageNum)}
                    className={`${pageNum === page 
                      ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    } relative inline-flex items-center px-4 py-2 border text-sm font-medium`}
                  >
                    {pageNum}
                  </button>
                );
              }
              
              // Show ellipsis for gaps
              if (pageNum === page - 2 || pageNum === page + 2) {
                return (
                  <span
                    key={`ellipsis-${pageNum}`}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                  >
                    ...
                  </span>
                );
              }
              
              return null;
            })}
            
            <button
              onClick={() => onPageChange(page + 1)}
              disabled={page === totalPages}
              className={`${page === totalPages ? 'opacity-50 cursor-not-allowed' : ''} relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500`}
            >
              <span className="sr-only">Næste</span>
              <FaChevronRight className="h-4 w-4" />
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
}

export default function TransactionsTable() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 1
  });

  const fetchTransactions = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/users/transactions?page=${page}&limit=${pagination.limit}`);
      
      if (!response.ok) {
        if (response.status === 403) {
          setTransactions([]);
          setLoading(false);
          return;
        }
        
        throw new Error('Failed to fetch transactions');
      }
      
      const data = await response.json();
      
      setTransactions(data.transactions);
      setPagination(data.pagination);
    } catch (err) {
      console.error('Error fetching transactions:', err);
      setError('Could not load transactions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, []);

  const handlePageChange = (newPage: number) => {
    fetchTransactions(newPage);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('da-DK');
    } catch {
      return dateString;
    }
  };

  if (loading && transactions.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow p-6 flex justify-center items-center min-h-[300px]">
        <div className="flex flex-col items-center">
          <FaSpinner className="animate-spin h-8 w-8 text-indigo-600 mb-4" />
          <p className="text-gray-600">Indlæser transaktioner...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow p-6">
        <div className="text-center text-red-500">
          <p>{error}</p>
          <button 
            onClick={() => fetchTransactions()} 
            className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
          >
            Prøv igen
          </button>
        </div>
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow p-8 text-center">
        <div className="mx-auto w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6">
          <FaExchangeAlt className="h-8 w-8 text-indigo-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Ingen transaktioner endnu</h3>
        <p className="text-gray-500 max-w-md mx-auto">
          Når dine produkter bliver købt, vil transaktionerne vises her.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow overflow-hidden">
      <div className="bg-gradient-to-r from-indigo-600 to-indigo-800 px-6 py-4">
        <h2 className="text-lg font-medium text-white flex items-center">
          <FaExchangeAlt className="mr-2" />
          Transaktioner
        </h2>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Dato
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Køber
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Produkt
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Beløb
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {transactions.map(transaction => (
              <tr key={transaction._id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDate(transaction.createdAt)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{transaction.buyerName}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{transaction.productName}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <span className="text-green-600 font-medium">
                    {(transaction.amount * 0.85).toLocaleString('da-DK')} DKK
                  </span>
                  <span className="text-xs text-gray-500 block">
                    (Efter 15% gebyr)
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {pagination.total > pagination.limit && (
        <div className="px-6">
          <Pagination 
            total={pagination.total}
            page={pagination.page}
            limit={pagination.limit}
            totalPages={pagination.totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
} 