'use client';

import React, { useState, useEffect, useLayoutEffect } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaCheck, FaCheckCircle, FaClock, FaArrowLeft, FaDiscord, FaGithub, FaEnvelope, FaPlug, FaCode, FaMap, FaCubes, FaQuestionCircle, FaPuzzlePiece, FaArrowRight, FaCodeBranch, FaShieldAlt, FaTimesCircle, FaBox, FaYoutube, FaTimes, FaUser, FaCog } from 'react-icons/fa';
import { useAuth } from '@/context/AuthContext';
import BadgeShowcase from '@/components/BadgeShowcase';
import BadgeDisplay from '@/components/BadgeDisplay';

// Hardcoded animation service product
const ANIMATION_SERVICE = {
  _id: 'animation-service',
  projectName: 'Animeret Billede Service',
  productType: 'service',
  price: 199,
  createdBy: 'MyckasP',
  description: 'Få lavet professionelle animerede billeder til din Minecraft server eller personligt.',
  projectDescription: 'Få lavet professionelle animerede billeder til din Minecraft server eller personligt. Jeg tilbyder animerede billeder til profilbilleder, server logoer og mere. Med en smule erfaring inden for grafik kan jeg hjælpe dig med at bringe dine ideer til live med højkvalitets animerede billeder, der fanger opmærksomheden.',
  screenshotUrls: [
    {
      fileId: 'handdrawn-image',
      filename: 'handdrawn.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745352236/tegnet_ytjlpi.png'
    },
    {
      fileId: 'handdrawn-image-2',
      filename: 'raw.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745428885/FlickMC_Tegnet-removebg-preview_vqq6zu.png'
    },
    {
      fileId: 'colored-image',
      filename: 'myckasp.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745352245/myckasp_whxdzt.png'
    },
    {
      fileId: 'colored-image-2',
      filename: 'FlickMC.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745428835/FlickMC__2_-removebg-preview_eexzik.png'
    }
  ],
  status: 'approved'
};

// Product interface
interface Product {
  _id: string;
  projectName: string;
  productType: string;
  price: number;
  createdBy: string;
  description: string;
  projectDescription?: string;
  screenshotUrls: Array<{
    fileId: string;
    filename: string;
    contentType: string;
    url?: string;
  }>;
  status: string;
}

// Freelancer interface
interface Freelancer {
  _id: string;
  username: string;
  discordUserId: string;
  avatar: string;
  bannerImage?: string;
  description?: string;
  isVerified?: boolean;
  verifiedAt?: string;
  email?: string;
  youtubeUrl?: string;
  githubUsername?: string;
  productCount: number;
  activeProductCount: number;
  avatarUrl?: string;
  createdAt: string;
  openForTasks?: boolean;
}

// Tab types for freelancer profile navigation
type TabType = 'products' | 'profile';

export default function FreelancerProfilePage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user: currentUser } = useAuth();

  const username = params?.username as string;
  const tabParam = searchParams.get('tab') as TabType;

  const [activeTab, setActiveTab] = useState<TabType>(tabParam || 'products');
  const [freelancer, setFreelancer] = useState<Freelancer | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [avatarFailed, setAvatarFailed] = useState(false);

  // Scroll to top when component mounts
  useLayoutEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Control scrolling based on loading state
  useLayoutEffect(() => {
    if (isLoading) {
      // Disable scrolling
      document.body.style.overflow = 'hidden';
      // Ensure we're at the top
      window.scrollTo(0, 0);
    } else {
      // Re-enable scrolling
      document.body.style.overflow = '';
    }
    
    // Cleanup function to re-enable scrolling when component unmounts
    return () => {
      document.body.style.overflow = '';
    };
  }, [isLoading]);

  // Handle avatar loading errors
  const handleAvatarError = () => {
    setAvatarFailed(true);
  };

  // Update active tab when URL parameter changes
  useEffect(() => {
    if (tabParam && ['products', 'profile'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Handle tab changes
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);

    // Update URL without reloading
    const newUrl = tab === 'products' ?
      `/developers/${username}` :
      `/developers/${username}?tab=${tab}`;
    router.replace(newUrl, { scroll: false });
  };

  // Check if current user is viewing their own profile
  const isOwnProfile = currentUser?.name?.toLowerCase() === username.toLowerCase();

  useEffect(() => {
    const fetchFreelancerData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Fetch freelancer data
        const freelancerResponse = await fetch(`/api/freelancers/${username}`);
        
        if (!freelancerResponse.ok) {
          throw new Error('Kunne ikke finde freelancer');
        }
        
        const freelancerData = await freelancerResponse.json();
        setFreelancer(freelancerData.freelancer);
        
        // Fetch freelancer's products - include all products regardless of status
        const productsResponse = await fetch(`/api/products?createdBy=${username}&includeAll=true`);
        
        if (productsResponse.ok) {
          const productsData = await productsResponse.json();
          let userProducts = productsData.products || [];
          
          // Add hardcoded animation service product for MyckasP
          if (username.toLowerCase() === 'myckasp') {
            userProducts = [ANIMATION_SERVICE, ...userProducts];
          }
          
          setProducts(userProducts);
        }
      } catch (err) {
        console.error('Error fetching freelancer data:', err);
        setError('Der opstod en fejl ved indlæsning af freelancerens profil');
      } finally {
        setIsLoading(false);
      }
    };
    
    if (username) {
      fetchFreelancerData();
    }
  }, [username]);

  // Set page title to "{developer name} | MCDevHub"
  useEffect(() => {
    if (freelancer?.username) {
      document.title = `${freelancer.username} | MCDevHub`;
    }
  }, [freelancer?.username]);

  const getProductTypeLabel = (type: string) => {
    const iconClass = "w-4 h-4 mr-2";
    const labelClass = "text-sm font-medium";
    
    switch (type) {
      case 'plugin':
        return <div className="flex items-center"><FaPlug className={iconClass} /><span className={labelClass}>Plugin</span></div>;
      case 'script':
      case 'skript':
        return <div className="flex items-center"><FaCode className={iconClass} /><span className={labelClass}>Skript</span></div>;
      case 'map':
        return <div className="flex items-center"><FaMap className={iconClass} /><span className={labelClass}>Map</span></div>;
      case 'build':
        return <div className="flex items-center"><FaCubes className={iconClass} /><span className={labelClass}>Build</span></div>;
      case 'service':
        return <div className="flex items-center"><FaCodeBranch className={iconClass} /><span className={labelClass}>Service</span></div>;
      default:
        return <div className="flex items-center"><FaQuestionCircle className={iconClass} /><span className={labelClass}>{type}</span></div>;
    }
  };

  const getProductTypeColor = (type: string) => {
    switch (type) {
      case 'plugin':
        return 'bg-pink-500/90 hover:bg-pink-600/90 shadow-pink-200';
      case 'script':
      case 'skript':
        return 'bg-green-500/90 hover:bg-green-600/90 shadow-green-200';
      case 'map':
        return 'bg-purple-500/90 hover:bg-purple-600/90 shadow-purple-200';
      case 'build':
        return 'bg-amber-500/90 hover:bg-amber-600/90 shadow-amber-200';
      case 'service':
        return 'bg-blue-500/90 hover:bg-blue-600/90 shadow-blue-200';
      default:
        return 'bg-gray-500/90 hover:bg-gray-600/90 shadow-gray-200';
    }
  };
  
  if (isLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90 backdrop-blur-sm">
        <div className="flex flex-col items-center max-w-sm mx-auto p-8 rounded-xl bg-white shadow-lg border border-gray-100">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mb-6" style={{ animationDuration: '0.4s' }}></div>
          <div className="text-blue-700 text-lg font-medium">Indlæser udvikler...</div>
          <p className="text-gray-500 text-sm mt-2 text-center">Vent venligst mens vi henter freelancerens profil</p>
        </div>
      </div>
    );
  }

  if (error || !freelancer) {
    return (
      <div className="min-h-screen pt-28 pb-20">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-10 h-10 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-4">Udvikler ikke fundet</h1>
            <p className="text-gray-600 mb-8">{error || 'Kunne ikke finde denne udvikler.'}</p>
            <Link href="/developers" className="inline-flex items-center text-blue-600 hover:text-blue-800">
              <FaArrowLeft className="mr-2" />
              Tilbage til freelancere
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20 sm:pt-28 pb-12 sm:pb-20">
      <div className="container mx-auto px-3 sm:px-4">
        {/* Back Link */}
        <div className="max-w-6xl mx-auto mb-4 sm:mb-6">
          <Link href="/developers" className="inline-flex items-center text-blue-600 hover:text-blue-800 group">
            <FaArrowLeft className="mr-2 transition-all duration-300 ease-in-out group-hover:-translate-x-1 group-hover:scale-110" />
            <span className="text-sm sm:text-base">Tilbage til alle freelancere</span>
          </Link>
        </div>
        
        {/* Freelancer Profile */}
        <div className="max-w-6xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden mb-8 border border-gray-100">
          {/* Banner Image */}
          <div className="h-36 sm:h-48 relative">
            {freelancer.bannerImage ? (
              <Image 
                src={freelancer.bannerImage} 
                alt={`${freelancer.username}'s banner`}
                layout="fill"
                objectFit="cover"
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="h-full w-full bg-gradient-to-r from-blue-600 to-indigo-700"></div>
            )}
            
            <div className="absolute -bottom-14 sm:-bottom-16 left-4 sm:left-8">
              <div className="relative w-28 h-28 sm:w-32 sm:h-32">
                <div className="w-full h-full rounded-full overflow-hidden border-4 border-white bg-white shadow-lg">
                  {freelancer.avatarUrl && !avatarFailed ? (
                    <Image
                      src={freelancer.avatarUrl}
                      alt={freelancer.username}
                      width={128}
                      height={128}
                      className="w-full h-full object-cover"
                      unoptimized
                      onError={handleAvatarError}
                    />
                  ) : freelancer.discordUserId && freelancer.avatar && !avatarFailed ? (
                    <Image
                      src={`https://cdn.discordapp.com/avatars/${freelancer.discordUserId}/${freelancer.avatar}.png?size=512`}
                      alt={freelancer.username}
                      width={128}
                      height={128}
                      className="w-full h-full object-cover"
                      unoptimized
                      onError={handleAvatarError}
                    />
                  ) : (
                    <div className="w-full h-full bg-blue-100 flex items-center justify-center">
                      <svg className="w-12 h-12 sm:w-16 sm:h-16 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  )}
                </div>
                {freelancer.username.toLowerCase() === 'myckasp' && (
                  <div className="absolute bottom-1 left-1 sm:bottom-2 sm:left-2 bg-red-500 rounded-full p-1.5 border-2 border-white shadow-sm">
                    <FaCheckCircle className="text-white w-3 h-3 sm:w-4 sm:h-4" />
                  </div>
                )}
                {!["myckasp"].includes(freelancer.username.toLowerCase()) && freelancer.isVerified && (
                  <div className="absolute bottom-1 left-1 sm:bottom-2 sm:left-2 bg-blue-500 rounded-full p-1.5 border-2 border-white shadow-sm">
                    <FaCheckCircle className="text-white w-3 h-3 sm:w-4 sm:h-4" />
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="pt-16 sm:pt-20 px-4 sm:px-8 pb-6 sm:pb-8">
            <div className="flex flex-col md:flex-row md:items-start md:justify-between">
              <div>
                <div className="flex flex-wrap items-center gap-1 sm:gap-2 mb-2">
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">{freelancer.username}</h1>
                  {["myckasp", "myckasp".toLowerCase(), "myckasp".toUpperCase()].includes(freelancer.username.toLowerCase()) && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 cursor-default">
                      Admin
                    </span>
                  )}
                  {freelancer.isVerified && (
                    <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-blue-100 text-blue-800 border border-blue-300 ml-1 sm:ml-2 cursor-default">
                      <FaCheck className="text-blue-500" /> Verificeret
                    </span>
                  )}
                  {freelancer.openForTasks ? (
                    <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-green-100 text-green-800 border border-green-300 ml-1 sm:ml-2 cursor-default">
                      <FaCheck className="text-green-500" /> Åben for opgaver
                    </span>
                  ) : (
                    <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-red-100 text-red-800 border border-red-300 ml-1 sm:ml-2 cursor-default">
                      <FaTimes className="text-red-500" /> Ikke åben for opgaver
                    </span>
                  )}
                </div>
                {/* Medlem siden */}
                {freelancer.createdAt && (
                  <div className="text-xs text-gray-500 mt-1 cursor-default">
                    Medlem siden {new Date(freelancer.createdAt).toLocaleDateString('da-DK', { year: 'numeric', month: 'long', day: 'numeric' })}
                  </div>
                )}
                <div className="flex items-center mt-2 text-gray-600">
                  <span className="font-medium">
                    {freelancer.username === 'MyckasP' ? 1 + freelancer.productCount : freelancer.productCount}
                  </span>
                  <span className="ml-1">
                    {freelancer.username === 'MyckasP' ? 
                      (1 + freelancer.productCount === 1 ? 'Produkt' : 'Produkter') : 
                      (freelancer.productCount === 1 ? 'Produkt' : 'Produkter')
                    }
                  </span>
                  {freelancer.activeProductCount !== freelancer.productCount && freelancer.activeProductCount > 0 && (
                    <span className="ml-3 text-green-600">
                      ({freelancer.activeProductCount} aktiv{freelancer.activeProductCount !== 1 ? 'e' : ''})
                    </span>
                  )}
                </div>

                {/* Badge Showcase */}
                <div className="mt-3">
                  <BadgeShowcase
                    discordUserId={freelancer.discordUserId}
                    maxDisplay={6}
                    size="medium"
                  />
                </div>
              </div>

              <div className="mt-4 md:mt-0 flex space-x-2 sm:space-x-3">
                {freelancer.discordUserId && (
                  <a 
                    href={`https://discord.com/users/${freelancer.discordUserId}`} 
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-[#5865F2] text-white transition-all duration-300 ease-in-out hover:scale-110"
                    title="Kontakt på Discord"
                  >
                    <FaDiscord className="w-4 h-4 sm:w-5 sm:h-5" />
                  </a>
                )}
                {freelancer.githubUsername && (
                  <a 
                    href={`https://github.com/${freelancer.githubUsername}`} 
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-gray-800 text-white transition-all duration-300 ease-in-out hover:scale-110"
                    title="GitHub profil"
                  >
                    <FaGithub className="w-4 h-4 sm:w-5 sm:h-5" />
                  </a>
                )}
                {freelancer.email && (
                  <a 
                    href={`mailto:${freelancer.email}`} 
                    className="inline-flex items-center justify-center w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-orange-500 text-white transition-all duration-300 ease-in-out hover:scale-110"
                    title="Send email"
                  >
                    <FaEnvelope className="w-4 h-4 sm:w-5 sm:h-5" />
                  </a>
                )}
                {freelancer.youtubeUrl && typeof freelancer.youtubeUrl === 'string' && (
                  <a 
                    href={freelancer.youtubeUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-red-600 text-white transition-all duration-300 ease-in-out hover:scale-110"
                    title="YouTube kanal"
                  >
                    <FaYoutube className="w-4 h-4 sm:w-5 sm:h-5" />
                  </a>
                )}
              </div>
            </div>
            
            {/* Description */}
            {freelancer.description && (
              <div className="mt-4 sm:mt-6 border-t border-gray-100 pt-4 sm:pt-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-2 sm:mb-4">Om Freelanceren</h2>
                <p className="text-sm sm:text-base text-gray-600 whitespace-pre-wrap">{freelancer.description}</p>
              </div>
            )}
            
            {/* Tab Navigation */}
            <div className="mt-6 sm:mt-8 border-t border-gray-100 pt-4 sm:pt-6">
              <div className="border-b border-gray-200 mb-6">
                <nav className="flex space-x-8" aria-label="Tabs">
                  <button
                    onClick={() => handleTabChange('products')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                      activeTab === 'products'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <FaCubes className="mr-2" />
                      Produkter ({freelancer.username === 'MyckasP' ? 1 + products.length : products.length})
                    </div>
                  </button>

                  <button
                    onClick={() => handleTabChange('profile')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                      activeTab === 'profile'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <FaUser className="mr-2" />
                      Udvikler Profil
                    </div>
                  </button>
                </nav>
              </div>

              {/* Tab Content */}
              {activeTab === 'products' && (
                <div>
                  <div className="flex flex-wrap items-center justify-between mb-4">
                    <h2 className="text-lg sm:text-xl font-bold text-gray-800 mb-1 sm:mb-0">Produkter</h2>
                    {products.some(p => p.status !== 'active') && (
                      <div className="text-xs sm:text-sm text-gray-500 w-full sm:w-auto">
                        Inkluderer produkter der afventer godkendelse
                      </div>
                    )}
                  </div>

                  {products.length === 0 ? (
                    <div className="text-center py-6 sm:py-8 bg-gray-50 rounded-lg">
                      <p className="text-gray-500">Denne udvikler har endnu ikke udgivet nogen produkter</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                      {products.map(product => (
                        <Link href={`/products/${product._id}`} key={product._id}>
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5 }}
                            className="group bg-white/60 backdrop-blur-md border border-blue-100 rounded-2xl sm:rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-200 hover:-translate-y-1"
                          >
                            <div className="relative aspect-[4/3] bg-gradient-to-br from-blue-100/40 to-indigo-50/50 flex items-center justify-center overflow-hidden">
                              {product.screenshotUrls && product.screenshotUrls[0]?.url ? (
                                <Image
                                  src={product.screenshotUrls[0].url}
                                  alt={product.projectName}
                                  width={400}
                                  height={160}
                                  className="w-full h-full object-cover"
                                  unoptimized
                                />
                              ) : (
                                <div className="flex flex-col items-center justify-center w-full h-full text-blue-300">
                                  <FaPuzzlePiece className="text-5xl mb-2" />
                                  <span className="text-base">Intet billede</span>
                                </div>
                              )}
                              <div className="absolute top-2 sm:top-4 left-2 sm:left-4">
                                <span className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-semibold shadow bg-blue-100 text-blue-800">
                                  {getProductTypeLabel(product.productType)}
                                </span>
                              </div>
                              <div className="absolute top-2 sm:top-4 right-2 sm:right-4">
                                {product.status === 'approved' || product.status === 'active' ? (
                                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-600 text-white">
                                    <FaCheckCircle className="mr-1 w-3 h-3" />
                                    Godkendt
                                  </span>
                                ) : product.status === 'pending' ? (
                                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-700 text-white">
                                    <FaClock className="mr-1 w-3 h-3" />
                                    Afventer
                                  </span>
                                ) : product.status === 'rejected' && (
                                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-600 text-white">
                                    <FaTimesCircle className="mr-1 w-3 h-3" />
                                    Afvist
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="p-4 sm:p-6 flex flex-col gap-1 sm:gap-2">
                              <h3 className="text-base sm:text-lg font-bold text-gray-900 group-hover:text-blue-700 transition-colors line-clamp-2 min-h-[2.5rem]">
                                {product.projectName}
                              </h3>
                              <p className="text-xs sm:text-sm text-gray-500 mb-1 sm:mb-2 line-clamp-2">
                                {product.projectDescription || product.description || 'Ingen beskrivelse'}
                              </p>
                              <div className="flex items-center justify-between mt-1">
                                <span className="font-bold text-blue-600 text-base sm:text-lg">
                                  {product.price === 0 ? (
                                    <span className="text-green-500">Gratis</span>
                                  ) : (
                                    <span>{product.price} DKK</span>
                                  )}
                                </span>
                                <FaArrowRight className="text-blue-400 group-hover:translate-x-1 transition-all duration-300 ease-in-out" />
                              </div>
                            </div>
                          </motion.div>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'profile' && (
                <div>
                  <h2 className="text-lg sm:text-xl font-bold text-gray-800 mb-4">Udvikler Profil</h2>
                  <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Tilgængelighed */}
                      <div>
                        <h3 className="font-semibold text-gray-800 mb-2">Tilgængelighed</h3>
                        <div className="flex items-center">
                          {freelancer.openForTasks ? (
                            <span className="inline-flex items-center gap-1 px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800 border border-green-300">
                              <FaCheck className="text-green-500" /> Åben for opgaver
                            </span>
                          ) : (
                            <span className="inline-flex items-center gap-1 px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800 border border-red-300">
                              <FaTimes className="text-red-500" /> Ikke åben for opgaver
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Badges */}
                      <div>
                        <h3 className="font-semibold text-gray-800 mb-2">Badges</h3>
                        <BadgeDisplay
                          discordUserId={freelancer.discordUserId}
                          maxDisplay={10}
                          showProgress={isOwnProfile}
                          showUnearned={isOwnProfile}
                        />
                      </div>

                      {/* Social Links */}
                      {(freelancer.githubUsername || freelancer.youtubeUrl || freelancer.email) && (
                        <div>
                          <h3 className="font-semibold text-gray-800 mb-2">Sociale Links</h3>
                          <div className="flex space-x-3">
                            {freelancer.githubUsername && (
                              <a
                                href={`https://github.com/${freelancer.githubUsername}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                              >
                                <FaGithub className="mr-2" />
                                GitHub
                              </a>
                            )}
                            {freelancer.youtubeUrl && (
                              <a
                                href={freelancer.youtubeUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-red-600 border border-red-600 rounded-md hover:bg-red-700"
                              >
                                <FaYoutube className="mr-2" />
                                YouTube
                              </a>
                            )}
                            {freelancer.email && (
                              <a
                                href={`mailto:${freelancer.email}`}
                                className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-orange-500 border border-orange-500 rounded-md hover:bg-orange-600"
                              >
                                <FaEnvelope className="mr-2" />
                                Email
                              </a>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Profile Owner Only Content */}
                      {isOwnProfile && (
                        <div className="md:col-span-2">
                          <h3 className="font-semibold text-gray-800 mb-2">Kun synlig for dig</h3>
                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <p className="text-sm text-blue-800">
                              Dette afsnit er kun synligt for dig som ejer af profilen. Andre brugere kan ikke se denne information.
                            </p>
                            <div className="mt-3">
                              <Link
                                href="/profile?tab=profile"
                                className="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded-md hover:bg-blue-200"
                              >
                                <FaCog className="mr-2" />
                                Rediger Profil
                              </Link>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      {/* Footer component */}
    </div>
  );
}