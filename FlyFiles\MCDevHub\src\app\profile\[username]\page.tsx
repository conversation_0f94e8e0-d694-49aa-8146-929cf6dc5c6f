'use client';

import React, { useState, useEffect, useLayoutEffect } from 'react';
import { usePara<PERSON>, useSearchParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  FaUser, 
  FaShoppingBag, 
  FaHeart, 
  FaArrowLeft, 
  FaDiscord, 
  FaGithub, 
  FaEnvelope, 
  FaYoutube,
  FaCheckCircle, 
  FaCheck,
  FaTimes,
  FaSpinner,
  FaExclamationTriangle,
  FaCalendar,
  FaShieldAlt
} from 'react-icons/fa';
import { useAuth } from '@/context/AuthContext';
import { formatDistanceToNow, format } from 'date-fns';
import { da } from 'date-fns/locale';
import BadgeDisplay from '@/components/BadgeDisplay';
import BadgeShowcase from '@/components/BadgeShowcase';

// Tab types for profile navigation
type TabType = 'purchases' | 'favorites' | 'profile';

// User profile interface
interface UserProfile {
  user: {
    username: string;
    discordUserId: string;
    avatar?: string;
    avatarUrl?: string;
    bannerImage?: string;
    description?: string;
    createdAt: string;
    badges: any[];
    isFreelancer: boolean;
    email?: string;
    youtubeUrl?: string;
    githubUsername?: string;
    openForTasks?: boolean;
  };
  purchases: any[];
  favorites: any[];
  verification: {
    isVerified: boolean;
    verifiedAt: string | null;
  };
  isOwnProfile: boolean;
}

export default function ProfileByUsernamePage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user: currentUser } = useAuth();
  
  const username = params?.username as string;
  const tabParam = searchParams.get('tab') as TabType;
  
  const [activeTab, setActiveTab] = useState<TabType>(tabParam || 'purchases');
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [avatarFailed, setAvatarFailed] = useState(false);

  // Scroll to top when component mounts
  useLayoutEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Control scrolling based on loading state
  useLayoutEffect(() => {
    if (isLoading) {
      document.body.style.overflow = 'hidden';
      window.scrollTo(0, 0);
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [isLoading]);

  // Handle avatar loading errors
  const handleAvatarError = () => {
    setAvatarFailed(true);
  };

  // Update active tab when URL parameter changes
  useEffect(() => {
    if (tabParam && ['purchases', 'favorites', 'profile'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Fetch user profile data by username
  useEffect(() => {
    const fetchProfile = async () => {
      if (!username) return;

      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/profile/username/${encodeURIComponent(username)}`);

        if (!response.ok) {
          const errorText = await response.text();

          if (response.status === 404) {
            throw new Error('Bruger ikke fundet');
          }

          // Try to parse error details
          try {
            const errorData = JSON.parse(errorText);
            throw new Error(errorData.error || 'Kunne ikke hente brugerprofil');
          } catch (parseError) {
            throw new Error('Kunne ikke hente brugerprofil');
          }
        }

        const profileData = await response.json();

        // Validate the profile data structure
        if (!profileData || typeof profileData !== 'object') {
          throw new Error('Ugyldig profildata modtaget');
        }

        // Check if this is a test response (from the simplified API)
        if (profileData.success && profileData.message) {
          throw new Error('API returnerer testdata - fuld implementering ikke aktiv endnu');
        }

        // Ensure user object exists with required fields
        if (!profileData.user) {
          throw new Error('Brugerdata mangler i API-svar');
        }

        // Provide default values for missing fields
        const normalizedProfile = {
          user: {
            username: profileData.user.username || username,
            discordUserId: profileData.user.discordUserId || '',
            avatar: profileData.user.avatar || null,
            avatarUrl: profileData.user.avatarUrl || null,
            bannerImage: profileData.user.bannerImage || null,
            description: profileData.user.description || null,
            createdAt: profileData.user.createdAt || new Date().toISOString(),
            badges: profileData.user.badges || [],
            isFreelancer: profileData.user.isFreelancer || false,
            email: profileData.user.email || null,
            youtubeUrl: profileData.user.youtubeUrl || null,
            githubUsername: profileData.user.githubUsername || null,
            openForTasks: profileData.user.openForTasks || false
          },
          purchases: profileData.purchases || [],
          favorites: profileData.favorites || [],
          verification: profileData.verification || {
            isVerified: false,
            verifiedAt: null
          },
          isOwnProfile: profileData.isOwnProfile || false
        };

        setProfile(normalizedProfile);

        // Set page title
        if (normalizedProfile.user.username) {
          document.title = `${normalizedProfile.user.username} | MCDevHub`;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Der opstod en fejl ved indlæsning af profilen');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, [username]);

  // Handle tab changes
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    
    // Update URL without reloading
    const newUrl = tab === 'purchases' ? 
      `/profile/${username}` : 
      `/profile/${username}?tab=${tab}`;
    router.replace(newUrl, { scroll: false });
  };

  // Get product type label
  const getProductTypeLabel = (type: string) => {
    switch (type) {
      case 'map': return 'Map';
      case 'plugin': return 'Plugin';
      case 'resourcepack': return 'Resourcepack';
      case 'skript': return 'Skript';
      case 'other': return 'Andet';
      default: return type;
    }
  };

  // Get product type color
  const getProductTypeColor = (type: string) => {
    switch (type) {
      case 'map': return 'bg-green-500';
      case 'plugin': return 'bg-blue-500';
      case 'resourcepack': return 'bg-yellow-500';
      case 'skript': return 'bg-purple-500';
      case 'other': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90 backdrop-blur-sm">
        <div className="flex flex-col items-center max-w-sm mx-auto p-8 rounded-xl bg-white shadow-lg border border-gray-100">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mb-6" style={{ animationDuration: '0.4s' }}></div>
          <div className="text-blue-700 text-lg font-medium">Indlæser profil...</div>
          <p className="text-gray-500 text-sm mt-2 text-center">Vent venligst mens vi henter brugerens profil</p>
        </div>
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className="min-h-screen pt-28 pb-20">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <FaExclamationTriangle className="text-red-600 text-3xl" />
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-4">Profil ikke fundet</h1>
            <p className="text-gray-600 mb-8">{error || 'Kunne ikke finde denne bruger.'}</p>
            <Link href="/" className="inline-flex items-center text-blue-600 hover:text-blue-800">
              <FaArrowLeft className="mr-2" />
              Tilbage til forsiden
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20 sm:pt-28 pb-12 sm:pb-20">
      <div className="container mx-auto px-3 sm:px-4">
        {/* Back Link */}
        <div className="max-w-6xl mx-auto mb-4 sm:mb-6">
          <Link href="/" className="inline-flex items-center text-blue-600 hover:text-blue-800 group">
            <FaArrowLeft className="mr-2 transition-all duration-300 ease-in-out group-hover:-translate-x-1 group-hover:scale-110" />
            <span className="text-sm sm:text-base">Tilbage til forsiden</span>
          </Link>
        </div>
        
        {/* User Profile Header */}
        <div className="max-w-6xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden mb-8 border border-gray-100">
          {/* Banner Image */}
          <div className="h-36 sm:h-48 relative">
            {profile.user.bannerImage ? (
              <Image 
                src={profile.user.bannerImage} 
                alt={`${profile.user.username}'s banner`}
                layout="fill"
                objectFit="cover"
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="h-full w-full bg-gradient-to-r from-blue-600 to-indigo-700"></div>
            )}
            
            <div className="absolute -bottom-14 sm:-bottom-16 left-4 sm:left-8">
              <div className="relative w-28 h-28 sm:w-32 sm:h-32">
                <div className="w-full h-full rounded-full overflow-hidden border-4 border-white bg-white shadow-lg">
                  {profile.user.avatarUrl && !avatarFailed ? (
                    <Image
                      src={profile.user.avatarUrl}
                      alt={profile.user.username}
                      width={128}
                      height={128}
                      className="w-full h-full object-cover"
                      unoptimized
                      onError={handleAvatarError}
                    />
                  ) : profile.user.discordUserId && profile.user.avatar && !avatarFailed ? (
                    <Image
                      src={`https://cdn.discordapp.com/avatars/${profile.user.discordUserId}/${profile.user.avatar}.png?size=512`}
                      alt={profile.user.username}
                      width={128}
                      height={128}
                      className="w-full h-full object-cover"
                      unoptimized
                      onError={handleAvatarError}
                    />
                  ) : (
                    <div className="w-full h-full bg-blue-100 flex items-center justify-center">
                      <FaUser className="w-12 h-12 sm:w-16 sm:h-16 text-blue-600" />
                    </div>
                  )}
                </div>
                
                {/* Verification badge */}
                {profile.verification.isVerified && (
                  <div className="absolute bottom-1 left-1 sm:bottom-2 sm:left-2 bg-blue-500 rounded-full p-1.5 border-2 border-white shadow-sm">
                    <FaCheckCircle className="text-white w-3 h-3 sm:w-4 sm:h-4" />
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="pt-16 sm:pt-20 px-4 sm:px-8 pb-6 sm:pb-8">
            <div className="flex flex-col md:flex-row md:items-start md:justify-between">
              <div>
                <div className="flex flex-wrap items-center gap-1 sm:gap-2 mb-2">
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                    {profile.user?.username || username || 'Ukendt bruger'}
                  </h1>

                  {profile.user?.isFreelancer && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 cursor-default">
                      Freelancer
                    </span>
                  )}

                  {profile.verification?.isVerified && (
                    <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-blue-100 text-blue-800 border border-blue-300 ml-1 sm:ml-2 cursor-default">
                      <FaCheck className="text-blue-500" /> Verificeret
                    </span>
                  )}

                  {profile.user?.isFreelancer && profile.user?.openForTasks !== undefined && (
                    profile.user.openForTasks ? (
                      <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-green-100 text-green-800 border border-green-300 ml-1 sm:ml-2 cursor-default">
                        <FaCheck className="text-green-500" /> Åben for opgaver
                      </span>
                    ) : (
                      <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-red-100 text-red-800 border border-red-300 ml-1 sm:ml-2 cursor-default">
                        <FaTimes className="text-red-500" /> Ikke åben for opgaver
                      </span>
                    )
                  )}
                </div>
                
                {/* Member since */}
                {profile.user?.createdAt && (
                  <div className="text-xs text-gray-500 mt-1 cursor-default flex items-center">
                    <FaCalendar className="mr-1" />
                    Medlem siden {new Date(profile.user.createdAt).toLocaleDateString('da-DK', { year: 'numeric', month: 'long', day: 'numeric' })}
                  </div>
                )}

                {/* Badge Display */}
                <div className="mt-3">
                  {profile.user?.discordUserId && (
                    <BadgeDisplay
                      discordUserId={profile.user.discordUserId}
                      maxDisplay={6}
                    />
                  )}
                </div>
              </div>

              {/* Social Links */}
              <div className="mt-4 md:mt-0 flex space-x-2 sm:space-x-3">
                {profile.user?.discordUserId && (
                  <a
                    href={`https://discord.com/users/${profile.user.discordUserId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-[#5865F2] text-white transition-all duration-300 ease-in-out hover:scale-110"
                    title="Kontakt på Discord"
                  >
                    <FaDiscord className="w-4 h-4 sm:w-5 sm:h-5" />
                  </a>
                )}
                {profile.user?.githubUsername && (
                  <a
                    href={`https://github.com/${profile.user.githubUsername}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-gray-800 text-white transition-all duration-300 ease-in-out hover:scale-110"
                    title="GitHub profil"
                  >
                    <FaGithub className="w-4 h-4 sm:w-5 sm:h-5" />
                  </a>
                )}
                {profile.user?.email && (
                  <a
                    href={`mailto:${profile.user.email}`}
                    className="inline-flex items-center justify-center w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-orange-500 text-white transition-all duration-300 ease-in-out hover:scale-110"
                    title="Send email"
                  >
                    <FaEnvelope className="w-4 h-4 sm:w-5 sm:h-5" />
                  </a>
                )}
                {profile.user?.youtubeUrl && (
                  <a
                    href={profile.user.youtubeUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-red-600 text-white transition-all duration-300 ease-in-out hover:scale-110"
                    title="YouTube kanal"
                  >
                    <FaYoutube className="w-4 h-4 sm:w-5 sm:h-5" />
                  </a>
                )}
              </div>
            </div>
            
            {/* Description */}
            {profile.user?.description && (
              <div className="mt-4 sm:mt-6 border-t border-gray-100 pt-4 sm:pt-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-2 sm:mb-4">
                  Om {profile.user?.username || username || 'denne bruger'}
                </h2>
                <p className="text-sm sm:text-base text-gray-600 whitespace-pre-wrap">{profile.user.description}</p>
              </div>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="max-w-6xl mx-auto mb-8">
          <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6" aria-label="Tabs">
                <button
                  onClick={() => handleTabChange('purchases')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                    activeTab === 'purchases'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center">
                    <FaShoppingBag className="mr-2" />
                    Køb ({profile.purchases?.length || 0})
                  </div>
                </button>

                <button
                  onClick={() => handleTabChange('favorites')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                    activeTab === 'favorites'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center">
                    <FaHeart className="mr-2" />
                    Favoritter ({profile.favorites?.length || 0})
                  </div>
                </button>

                {profile.user?.isFreelancer && (
                  <button
                    onClick={() => handleTabChange('profile')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                      activeTab === 'profile'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <FaUser className="mr-2" />
                      Udvikler Profil
                    </div>
                  </button>
                )}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {activeTab === 'purchases' && (
                <div>
                  <h2 className="text-xl font-bold text-gray-800 mb-4">Køb</h2>
                  {!profile.purchases || profile.purchases.length === 0 ? (
                    <div className="text-center py-8 bg-gray-50 rounded-lg">
                      <FaShoppingBag className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <p className="text-gray-500">Ingen køb at vise</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {profile.purchases.map((purchase, index) => (
                        <div key={purchase._id || index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              {purchase.thumbnailUrl && (
                                <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-200">
                                  <Image
                                    src={purchase.thumbnailUrl}
                                    alt={purchase.productName}
                                    width={48}
                                    height={48}
                                    className="w-full h-full object-cover"
                                    unoptimized
                                  />
                                </div>
                              )}
                              <div>
                                <h3 className="font-semibold text-gray-900">{purchase.productName}</h3>
                                <div className="flex items-center space-x-2 text-sm text-gray-500">
                                  <span className={`px-2 py-1 rounded text-xs font-medium text-white ${getProductTypeColor(purchase.productType)}`}>
                                    {getProductTypeLabel(purchase.productType)}
                                  </span>
                                  {purchase.seller && <span>af {purchase.seller}</span>}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-bold text-blue-600">{purchase.amount} DKK</div>
                              <div className="text-sm text-gray-500">
                                {format(new Date(purchase.purchaseDate), 'dd.MM.yyyy', { locale: da })}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'favorites' && (
                <div>
                  <h2 className="text-xl font-bold text-gray-800 mb-4">Favoritter</h2>
                  {!profile.favorites || profile.favorites.length === 0 ? (
                    <div className="text-center py-8 bg-gray-50 rounded-lg">
                      <FaHeart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <p className="text-gray-500">Ingen favoritter at vise</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {profile.favorites.map((favorite, index) => (
                        <Link href={`/products/${favorite.productId}`} key={favorite.productId || index}>
                          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow duration-200">
                            {favorite.thumbnailUrl && (
                              <div className="w-full h-32 rounded-lg overflow-hidden bg-gray-200 mb-3">
                                <Image
                                  src={favorite.thumbnailUrl}
                                  alt={favorite.productName}
                                  width={200}
                                  height={128}
                                  className="w-full h-full object-cover"
                                  unoptimized
                                />
                              </div>
                            )}
                            <h3 className="font-semibold text-gray-900 mb-2">{favorite.productName}</h3>
                            <span className={`inline-block px-2 py-1 rounded text-xs font-medium text-white ${getProductTypeColor(favorite.productType)}`}>
                              {getProductTypeLabel(favorite.productType)}
                            </span>
                            {favorite.addedAt && (
                              <div className="text-xs text-gray-500 mt-2">
                                Tilføjet {formatDistanceToNow(new Date(favorite.addedAt), { addSuffix: true, locale: da })}
                              </div>
                            )}
                          </div>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'profile' && profile.user?.isFreelancer && (
                <div>
                  <h2 className="text-xl font-bold text-gray-800 mb-4">Udvikler Profil</h2>
                  <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                    {/* Badges */}
                    <div>
                      <h3 className="font-semibold text-gray-800 mb-4">Badges</h3>
                      {profile.user?.discordUserId && (
                        <BadgeDisplay
                          discordUserId={profile.user.discordUserId}
                          maxDisplay={10}
                          showProgress={profile.isOwnProfile}
                        />
                      )}
                    </div>

                    {/* Social Links */}
                    {(profile.user?.githubUsername || profile.user?.youtubeUrl || profile.user?.email) && (
                      <div className="mt-6">
                        <h3 className="font-semibold text-gray-800 mb-4">Sociale Links</h3>
                        <div className="flex flex-wrap gap-3">
                          {profile.user?.githubUsername && (
                            <a
                              href={`https://github.com/${profile.user.githubUsername}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                            >
                              <FaGithub className="mr-2" />
                              GitHub
                            </a>
                          )}
                          {profile.user?.youtubeUrl && (
                            <a
                              href={profile.user.youtubeUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-red-600 border border-red-600 rounded-md hover:bg-red-700 transition-colors"
                            >
                              <FaYoutube className="mr-2" />
                              YouTube
                            </a>
                          )}
                          {profile.user?.email && (
                            <a
                              href={`mailto:${profile.user.email}`}
                              className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-orange-500 border border-orange-500 rounded-md hover:bg-orange-600 transition-colors"
                            >
                              <FaEnvelope className="mr-2" />
                              Email
                            </a>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
