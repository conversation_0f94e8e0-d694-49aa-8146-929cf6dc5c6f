import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

export async function GET(request) {
  try {
    // This endpoint should be protected in production
    // Check for admin authentication here
    
    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Get all freelancers that have both discordUserId and avatar but no avatarUrl
    const freelancers = await db.collection('adminusers')
      .find({ 
        admintype: 'Freelancer',
        discordUserId: { $exists: true, $ne: null },
        avatar: { $exists: true, $ne: null },
        $or: [
          { avatarUrl: { $exists: false } },
          { avatarUrl: null }
        ]
      })
      .toArray();
    
    if (freelancers.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No freelancers found that need avatar URL fixes',
        fixed: 0
      });
    }
    
    // For each freelancer, construct and store the avatar URL
    const updateResults = await Promise.all(
      freelancers.map(async (freelancer) => {
        try {
          // Construct the Discord CDN URL
          const avatarUrl = `https://cdn.discordapp.com/avatars/${freelancer.discordUserId}/${freelancer.avatar}.png?size=512`;
          
          // Update the user in the database
          const updateResult = await db.collection('adminusers').updateOne(
            { _id: freelancer._id },
            { $set: { avatarUrl: avatarUrl } }
          );
          
          return { 
            success: true, 
            username: freelancer.username,
            avatarUrl,
            updated: updateResult.modifiedCount > 0
          };
        } catch (error) {
          console.error(`Error updating avatar URL for ${freelancer.username}:`, error);
          return { success: false, username: freelancer.username, error: error.message };
        }
      })
    );
    
    const successfulUpdates = updateResults.filter(result => result.success);
    
    return NextResponse.json({
      success: true,
      message: `Fixed avatar URLs for ${successfulUpdates.length} of ${freelancers.length} freelancers`,
      results: updateResults
    });
    
  } catch (error) {
    console.error('Error fixing Discord avatar URLs:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      message: error.message 
    }, { status: 500 });
  }
} 