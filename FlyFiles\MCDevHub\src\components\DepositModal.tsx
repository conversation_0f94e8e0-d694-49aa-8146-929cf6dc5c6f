import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { FaTimes, FaExclamationTriangle, FaWallet, FaMoneyBillWave } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';

interface DepositModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function DepositModal({ isOpen, onClose }: DepositModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { user } = useAuth();
  const [amount, setAmount] = useState<number>(100); // Default amount
  
  // Predefined deposit amounts
  const depositAmounts = [50, 100, 200, 500, 1000];

  // Prevent scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      // Store original body style
      const originalStyle = window.getComputedStyle(document.body).overflow;
      // Disable scrolling
      document.body.style.overflow = 'hidden';
      // Add padding to prevent layout shift when scrollbar disappears
      document.documentElement.style.paddingRight = `${window.innerWidth - document.documentElement.clientWidth}px`;
      
      // Reset error when opening
      setErrorMessage(null);
      
      // Cleanup function to restore original scroll behavior
      return () => {
        document.body.style.overflow = originalStyle;
        document.documentElement.style.paddingRight = '0';
      };
    }
  }, [isOpen]);

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0) {
      setAmount(value);
    } else {
      setAmount(0);
    }
  };

  const handlePresetAmount = (value: number) => {
    setAmount(value);
  };

  const handleDepositSelection = async (method: 'stripe' | 'mobilepay') => {
    try {
      // Check if user is logged in
      if (!user) {
        // Redirect to Discord login using NextAuth
        const { signIn } = await import('next-auth/react');
        await signIn('discord', { callbackUrl: '/' });
        return;
      }
      
      // Validate amount
      if (amount <= 0) {
        setErrorMessage('Beløbet skal være større end 0 DKK');
        return;
      }
      
      setIsProcessing(true);
      setErrorMessage(null);
      
      console.log(`Initiating ${method} deposit of ${amount} DKK`);
      
      // Create a new deposit session
      const response = await fetch('/api/payments/create-deposit-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          paymentMethod: method,
        }),
      });

      console.log(`Deposit API response status: ${response.status}`);
      
      const data = await response.json();
      console.log('Deposit API response data:', data);
      
      if (!response.ok) {
        console.error('Deposit API error:', data);
        
        // Format the error message for display
        let errorMsg: string;
        if (data.details) {
          errorMsg = `${data.error}: ${data.details}`;
        } else {
          errorMsg = data.error || 'Der opstod en fejl ved oprettelse af indbetalingen';
        }
        
        throw new Error(errorMsg);
      }

      // Redirect to the payment URL
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('Ingen betalings-URL modtaget fra serveren');
      }
    } catch (error) {
      console.error('Deposit error:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Der opstod en fejl. Prøv igen senere.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop with blur effect */}
          <motion.div 
            className="fixed inset-0 bg-black/50 backdrop-blur-sm" 
            onClick={onClose}
            aria-hidden="true"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          />
          
          {/* Modal content */}
          <motion.div 
            className="relative bg-white w-full max-w-md mx-auto rounded-xl shadow-2xl p-6 z-10"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            {/* Close button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Luk"
              disabled={isProcessing}
            >
              <FaTimes size={20} />
            </button>
            
            {/* Header */}
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">Indbetal til din balance</h3>
              <p className="text-gray-600 mt-1">
                Vælg beløb og betalingsmetode
              </p>
            </div>
            
            {/* Error message */}
            {errorMessage && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-700 flex items-start">
                <FaExclamationTriangle className="flex-shrink-0 w-5 h-5 mr-2 mt-0.5 text-red-500" />
                <div>
                  <p className="font-medium">Der opstod en fejl</p>
                  <p>{errorMessage}</p>
                </div>
              </div>
            )}
            
            {/* Amount selection */}
            <div className="mb-6">
              <label htmlFor="deposit-amount" className="block text-sm font-medium text-gray-700 mb-2">
                Beløb (DKK)
              </label>
              <input
                type="number"
                id="deposit-amount"
                value={amount}
                onChange={handleAmountChange}
                min="1"
                className="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-lg"
                placeholder="Indtast beløb"
              />
              
              {/* Preset amounts */}
              <div className="grid grid-cols-3 gap-2 mt-3">
                {depositAmounts.map((presetAmount) => (
                  <button
                    key={presetAmount}
                    type="button"
                    onClick={() => handlePresetAmount(presetAmount)}
                    className={`px-3 py-2 border rounded-md text-sm font-medium transition-colors
                      ${amount === presetAmount 
                        ? 'bg-blue-50 border-blue-500 text-blue-700' 
                        : 'border-gray-300 text-gray-700 hover:bg-gray-50'}`}
                  >
                    {presetAmount} DKK
                  </button>
                ))}
              </div>
            </div>
            
            {/* Payment options */}
            <div className="grid grid-cols-2 gap-4">
              {/* Stripe option */}
              <button
                onClick={() => handleDepositSelection('stripe')}
                disabled={isProcessing || amount <= 0}
                className={`h-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:shadow-md transition-all group 
                  ${isProcessing ? 'opacity-50 cursor-default bg-gray-50' : ''}
                  ${amount <= 0 ? 'opacity-50 cursor-default bg-gray-50' : ''}`}
              >
                <span className="font-medium text-gray-800 group-hover:text-blue-600">
                  {isProcessing ? 'Behandler...' : 'Betal med kort'}
                </span>
                <div className="w-16 h-8 relative">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-full">
                    <path d="M21 4H3C1.89543 4 1 4.89543 1 6V18C1 19.1046 1.89543 20 3 20H21C22.1046 20 23 19.1046 23 18V6C23 4.89543 22.1046 4 21 4Z" stroke="#635BFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M1 10H23" stroke="#635BFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </button>
              
              {/* MobilePay option */}
              <button
                onClick={() => handleDepositSelection('mobilepay')}
                disabled={isProcessing || amount <= 0}
                className={`h-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:shadow-md transition-all group 
                  ${isProcessing ? 'opacity-50 cursor-default bg-gray-50' : ''}
                  ${amount <= 0 ? 'opacity-50 cursor-not-allowed bg-gray-50' : ''}`}
              >
                <span className="font-medium text-gray-800 group-hover:text-blue-600">
                  {isProcessing ? 'Behandler...' : 'Betal med MobilePay'}
                </span>
                <div className="w-24 h-8 relative">
                  <Image
                    src="https://developer.mobilepay.dk/img/logo-blue.svg"
                    alt="MobilePay"
                    fill
                    style={{ objectFit: 'contain' }}
                  />
                </div>
              </button>
            </div>
            
            {/* Info note */}
            <div className="mt-6 text-xs text-gray-500 bg-gray-50 p-3 rounded-lg border border-gray-100">
              <p className="flex items-start">
                <FaMoneyBillWave className="flex-shrink-0 mt-0.5 mr-2 text-gray-400" />
                Indbetalinger behandles øjeblikkeligt og vil fremgå af din balance med det samme
              </p>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
} 