'use client';

import Link from 'next/link';
import Image from 'next/image';
import { FaBolt, FaLock, FaHeadset, FaServer, FaTools, FaDiscord, FaYoutube, FaCheckCircle } from 'react-icons/fa';
import Script from 'next/script';

export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Schema.org JSON-LD structured data */}
      <Script
        id="schema-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'WebSite',
            'name': 'MCDevHub',
            'url': 'https://mcdevhub.dk/',
            'potentialAction': {
              '@type': 'SearchAction',
              'target': 'https://mcdevhub.dk/products?search={search_term_string}',
              'query-input': 'required name=search_term_string'
            },
            'description': 'MCDevHub - Danmarks førende markedsplads for Minecraft plugins, skripts, mods og maps fra danske udviklere.',
            'keywords': 'MCDevHub, Minecraft, Dansk, Plugins, Skripts, Builds, Resourcepacks, Mods, Maps',
            'inLanguage': 'da-DK',
            'publisher': {
              '@type': 'Organization',
              'name': 'MCDevHub',
              'logo': {
                '@type': 'ImageObject',
                'url': 'https://mcdevhub.dk/images/logo.png'
              }
            },
            'mainEntity': {
              '@type': 'ItemList',
              'itemListElement': [
                {
                  '@type': 'ListItem',
                  'position': 1,
                  'name': 'Minecraft Plugins',
                  'url': 'https://mcdevhub.dk/products?category=plugins'
                },
                {
                  '@type': 'ListItem',
                  'position': 2,
                  'name': 'Minecraft Skripts',
                  'url': 'https://mcdevhub.dk/products?category=skripts'
                },
                {
                  '@type': 'ListItem',
                  'position': 3,
                  'name': 'Minecraft Resourcepacks',
                  'url': 'https://mcdevhub.dk/products?category=resourcepacks'
                },
                {
                  '@type': 'ListItem',
                  'position': 4,
                  'name': 'Minecraft Builds',
                  'url': 'https://mcdevhub.dk/products?category=builds'
                }
              ]
            }
          })
        }}
      />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 text-white py-16 sm:py-20 md:py-28 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/home-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="flex flex-col lg:flex-row items-center">
            <div className="lg:w-1/2 lg:pr-10 text-center lg:text-left mb-10 lg:mb-0">
              <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/30 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium cursor-default">
                Danmarks førende Minecraft løsninger
              </div>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 sm:mb-6 leading-tight">
                Professionelle <span className="text-blue-300">Minecraft</span> plugins, skripts og builds til din server
              </h1>
              <p className="text-lg sm:text-xl text-blue-100 mb-6 sm:mb-8 max-w-xl mx-auto lg:mx-0">
              Vi udvikler løsninger til din Minecraft server med fokus på kvalitet, ydeevne og brugervenlighed.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link 
                  href="/custom-orders" 
                  className="group relative bg-white text-blue-800 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20 text-center sm:text-left"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Bestil specialudvikling
                    <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </Link>
                <Link 
                  href="/kontakt" 
                  className="group relative bg-transparent border-2 border-white text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-white/20 text-center sm:text-left"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Kontakt os
                    <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </Link>
              </div>
              
              <div className="mt-4 sm:mt-6 animate-pulse">
                <Link href="/partner-program" className="inline-flex items-center bg-indigo-600/80 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-indigo-700 transition-colors group">
                  <span className="flex items-center">
                    <FaYoutube className="mr-1.5 text-lg" />
                    <span className="hidden sm:inline">Er du content creator? Få gratis produkter!</span>
                    <span className="sm:hidden">Gratis produkter!</span> {/* Til Mobil */}
                    <svg className="w-4 h-4 ml-1.5 transform transition-all duration-300 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </span>
                </Link>
              </div>
              <div className="mt-3 flex flex-col items-center sm:items-start justify-center">
              </div>
            </div>
            <div className="lg:w-1/2 relative w-full max-w-md mx-auto lg:max-w-none">
              <div className="w-full h-[300px] sm:h-[350px] md:h-[400px] relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-indigo-600/30 rounded-2xl overflow-hidden shadow-2xl backdrop-blur-sm border border-white/10">
                  {/* Linux-style title bar */}
                  <div className="absolute top-0 left-0 right-0 h-8 bg-[#333333] flex items-center justify-between px-2">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-[#ec6a5f] rounded-full mr-1.5"></div>
                      <div className="w-3 h-3 bg-[#f4bf4f] rounded-full mr-1.5"></div>
                      <div className="w-3 h-3 bg-[#61c554] rounded-full"></div>
                    </div>
                    <div className="text-gray-200 text-xs font-mono flex-1 text-center">minecraft@server:~</div>
                    <div className="w-6"></div> {/* Empty space to balance the layout */}
                  </div>
                  {/* Linux terminal style content */}
                  <div className="absolute top-8 left-0 right-0 bottom-0 bg-[#1e1e1e] p-3 font-mono text-sm text-gray-300 overflow-hidden">
                    <div className="space-y-1.5">
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_0.1s_forwards]">
                        <span className="text-green-400">minecraft@server</span><span className="text-gray-400">:</span><span className="text-blue-400">~</span><span className="text-gray-400">$ </span><span className="text-white">sudo java -Xmx16G -Xms16G -jar minecraft_server.jar nogui</span>
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_0.6s_forwards]">
                        <span className="text-green-400">[INFO]:</span> Starting Minecraft server version 1.20.1
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_1.2s_forwards]">
                        <span className="text-green-400">[INFO]:</span> Allocated 16GB RAM for server
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_1.8s_forwards]">
                        <span className="text-green-400">[INFO]:</span> Loading properties from server.properties
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_2.4s_forwards]">
                        <span className="text-green-400">[INFO]:</span> Loading MCDevHub modules...
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_3.0s_forwards]">
                        <span className="text-yellow-400">[MCDevHub]:</span> Initializing custom framework
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_3.6s_forwards]">
                        <span className="text-yellow-400">[MCDevHub]:</span> Loading configuration files
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_4.2s_forwards]">
                        <span className="text-blue-400">[MCDevHub]:</span> Custom economy system 
                        <span className="inline-block animate-[pulse_1s_ease-in-out_4.2s_forwards]">.</span>
                        <span className="inline-block animate-[pulse_1s_ease-in-out_4.7s_forwards]">.</span>
                        <span className="inline-block animate-[pulse_1s_ease-in-out_5.2s_forwards]">.</span>
                        <span className="opacity-0 animate-[fadeIn_0.2s_ease-in-out_5.7s_forwards]"> loaded</span>
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_6.2s_forwards]">
                        <span className="text-blue-400">[McDevHub]:</span> Player management system 
                        <span className="inline-block animate-[pulse_1s_ease-in-out_6.7s_forwards]">.</span>
                        <span className="inline-block animate-[pulse_1s_ease-in-out_7.2s_forwards]">.</span>
                        <span className="inline-block animate-[pulse_1s_ease-in-out_7.7s_forwards]">.</span>
                        <span className="opacity-0 animate-[fadeIn_0.2s_ease-in-out_8.2s_forwards]"> loaded</span>
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_8.7s_forwards]">
                        <span className="text-blue-400">[McDevHub]:</span> Server optimization enabled
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_9.2s_forwards]">
                        <span className="text-green-400">[INFO]:</span> All systems operational
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_10.0s_forwards] font-bold">
                        <span className="text-green-500">[SUCCESS]:</span> Server ready! Plugins loaded: <span className="animate-[countUp_1s_ease-in-out_10.5s_forwards]">6</span>
                      </div>
                      <div className="server-line opacity-0 animate-[fadeIn_0.2s_ease-in-out_11.0s_forwards]">
                        <span className="text-green-400">minecraft@server</span><span className="text-gray-400">:</span><span className="text-blue-400">~</span><span className="text-gray-400">$ </span><span className="text-white opacity-100 inline-block w-2 h-4 ml-1 bg-white animate-[blink_1s_infinite]"></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-gray-50 to-transparent"></div>
      </section>

      {/* Partner Program Ad Banner */}
      <div className="relative py-3 bg-gradient-to-r from-indigo-600 to-purple-700 shadow-lg">
        <div className="container mx-auto">
          <div className="flex flex-col sm:flex-row items-center justify-between py-2 px-4">
            <div className="flex flex-col sm:flex-row items-center mb-3 sm:mb-0 text-center sm:text-left">
              <div className="h-12 w-12 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto sm:mx-0 sm:mr-4 mb-2 sm:mb-0">
                <FaYoutube className="h-6 w-6 text-white" />
              </div>
              <div>
                <p className="text-white font-bold text-lg">Content Creator eller Server Ejer?</p>
                <p className="text-indigo-100 text-sm">Få adgang til produkter - helt gratis!</p>
              </div>
            </div>
            <Link 
              href="/partner-program"
              className="group relative bg-white text-indigo-700 px-5 py-3 rounded-lg font-bold overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-indigo-500/20 w-full sm:w-auto text-center"
            >
              <span className="relative z-10 flex items-center justify-center">
                Bliv Partner Nu
                <svg className="w-5 h-5 ml-2 transform transition-all duration-500 group-hover:translate-x-2 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>
            </Link>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <section className="py-16 sm:py-20 md:py-24 bg-white">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-10 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Hvorfor vælge MCDevHub
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-base sm:text-lg">
              Vi leverer gennemtestede og optimerede løsninger, der giver dig en problemfri oplevelse
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 relative overflow-hidden group hover:border-blue-200">
              <div className="absolute top-0 right-0 w-40 h-40 bg-blue-100 rounded-full -mr-20 -mt-20 transition-all duration-700 group-hover:scale-150 group-hover:bg-blue-200"></div>
              <div className="relative">
                <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6 text-blue-600 group-hover:bg-blue-600 group-hover:text-white transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-3">
                  <FaBolt className="w-7 h-7" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Optimeret Performance</h3>
                <p className="text-gray-600 mb-4">
                  Vores plugins er optimeret til at køre effektivt selv på servere med mange spillere og plugins.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Minimal belastning på serveren</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Hurtig indlæsningstid</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Optimeret kode</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 relative overflow-hidden group hover:border-green-200">
              <div className="absolute top-0 right-0 w-40 h-40 bg-green-100 rounded-full -mr-20 -mt-20 transition-all duration-700 group-hover:scale-150 group-hover:bg-green-200"></div>
              <div className="relative">
                <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mb-6 text-green-600 group-hover:bg-green-600 group-hover:text-white transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-3">
                  <FaLock className="w-7 h-7" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Sikker Betaling</h3>
                <p className="text-gray-600 mb-4">
                  Vi tilbyder sikre betalingsmuligheder med MobilePay, så dine data altid er beskyttet.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-green-100 text-green-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Sikker transaktion</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-green-100 text-green-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Ingen skjulte gebyrer</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-green-100 text-green-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Hurtig behandling</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 relative overflow-hidden group hover:border-indigo-200">
              <div className="absolute top-0 right-0 w-40 h-40 bg-indigo-100 rounded-full -mr-20 -mt-20 transition-all duration-700 group-hover:scale-150 group-hover:bg-indigo-200"></div>
              <div className="relative">
                <div className="w-16 h-16 bg-indigo-100 rounded-xl flex items-center justify-center mb-6 text-indigo-600 group-hover:bg-indigo-600 group-hover:text-white transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-3">
                  <FaHeadset className="w-7 h-7" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Dedikeret Support</h3>
                <p className="text-gray-600 mb-4">
                  Vi er her for at hjælpe dig med enhver udfordring du måtte støde på med vores produkter.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Discord support</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Hurtig svartid</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Detaljerede guides</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 sm:py-20 md:py-24 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-10 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Vores ydelser
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-base sm:text-lg">
              Specialiseret udvikling til at forbedre din Minecraft server
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
            <div className="bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 flex flex-col h-full hover:shadow-2xl transition-shadow duration-500">
              <div className="h-3 bg-blue-600"></div>
              <div className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 text-blue-600">
                    <FaServer className="w-6 h-6" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Minecraft Plugins</h3>
                </div>
                <p className="text-gray-600 mb-6">
                Få udviklet plugins, skripts og builds til din Minecraft server, der præcist matcher dine behov – uanset om det er et simpelt adminpanel, et avanceret Stats-system eller en unik verden
                </p>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span className="text-gray-600">Administration plugins med brugervenlige interfaces</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span className="text-gray-600">Cellesystemer til at købe celler</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span className="text-gray-600">Nemt ban system med avanceret logging</span>
                  </li>
                </ul>
                <div className="mt-auto">
                  <Link 
                    href="/custom-orders" 
                    className="inline-flex items-center font-medium text-blue-600 hover:text-blue-800 transition-colors duration-300 group"
                  >
                    Læs mere om plugins
                    <div className="arrow-wrapper">
                      <svg className="w-5 h-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 flex flex-col h-full hover:shadow-2xl transition-shadow duration-500">
              <div className="h-3 bg-indigo-600"></div>
              <div className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mr-4 text-indigo-600">
                    <FaTools className="w-6 h-6" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Løsninger Lavet Til Dig</h3>
                </div>
                <p className="text-gray-600 mb-6">
                Vi bygger alt-i-ét serverpakker eller designer præcis det, din server mangler.
                Ingen kode-kaos – kun features, der gør din server forskellig fra alle andre.
                </p>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span className="text-gray-600">Komplet serverpakker med alle nødvendige features</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span className="text-gray-600">Automatiserede systemer til effektiv drift</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span className="text-gray-600">Unikke spilleoplevelser der skiller din server ud</span>
                  </li>
                </ul>
                <div className="mt-auto">
                  <Link 
                    href="/custom-orders" 
                    className="inline-flex items-center font-medium text-indigo-600 hover:text-indigo-800 transition-colors duration-300 group"
                  >
                    Bestil specialudvikling
                    <div className="arrow-wrapper">
                      <svg className="w-5 h-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Reviews & Feedback */}
      <section className="py-16 sm:py-20 md:py-24 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
              <div className="text-center lg:text-left">
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4 sm:mb-6">
                  Din mening betyder meget
                </h2>
                <p className="text-gray-600 text-base sm:text-lg mb-6 sm:mb-8">
                  Har du købt et af vores produkter eller brugt vores services? Din feedback hjælper os med at blive bedre.
                </p>
                <p className="text-gray-600 mb-6 sm:mb-8">
                  Del din oplevelse og hjælp andre serverejere med at træffe det rette valg. Hver anmeldelse giver os mulighed for at forbedre vores produkter og services.
                </p>
                
                {/* 3D Interactive Element */}
                <div className="mt-8 sm:mt-10 scene relative h-48 sm:h-64 w-48 sm:w-64 mx-auto lg:ml-0 perspective-1000 cursor-pointer">
                  <div className="cube relative preserve-3d w-full h-full transition-transform duration-1000 cube-rotate">
                    {/* Front face */}
                    <div className="cube-face cube-face-front absolute w-full h-full flex items-center justify-center rounded-xl bg-gradient-to-br from-blue-600 to-blue-800 text-white">
                      <div className="text-center p-5">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-blue-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                        </svg>
                        <h4 className="text-xl font-bold mb-2">Din feedback</h4>
                        <p className="text-sm opacity-80">Hjælper os med at forbedre</p>
                      </div>
                    </div>
                    
                    {/* Back face */}
                    <div className="cube-face cube-face-back absolute w-full h-full flex items-center justify-center rounded-xl bg-gradient-to-br from-indigo-600 to-indigo-800 text-white">
                      <div className="text-center p-5">
                        <FaDiscord className="h-16 w-16 mx-auto mb-4 text-indigo-100" />
                        <h4 className="text-xl font-bold mb-2">Join Discord</h4>
                        <p className="text-sm opacity-80">Bliv en del af fællesskabet</p>
                      </div>
                    </div>
                    
                    {/* Right face */}
                    <div className="cube-face cube-face-right absolute w-full h-full flex items-center justify-center rounded-xl bg-gradient-to-br from-green-600 to-green-800 text-white">
                      <div className="text-center p-5">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-green-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h4 className="text-xl font-bold mb-2">Trustpilot</h4>
                        <p className="text-sm opacity-80">Del din anmeldelse</p>
                      </div>
                    </div>
                    
                    {/* Left face */}
                    <div className="cube-face cube-face-left absolute w-full h-full flex items-center justify-center rounded-xl bg-gradient-to-br from-purple-600 to-purple-800 text-white">
                      <div className="text-center p-5">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-purple-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <h4 className="text-xl font-bold mb-2">Kommentarer</h4>
                        <p className="text-sm opacity-80">Fortæl os hvad du synes</p>
                      </div>
                    </div>
                    
                    {/* Top face */}
                    <div className="cube-face cube-face-top absolute w-full h-full flex items-center justify-center rounded-xl bg-gradient-to-br from-red-600 to-red-800 text-white">
                      <div className="text-center p-5">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-red-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <h4 className="text-xl font-bold mb-2">Tak for støtten</h4>
                        <p className="text-sm opacity-80">Vi sætter pris på dig</p>
                      </div>
                    </div>
                    
                    {/* Bottom face */}
                    <div className="cube-face cube-face-bottom absolute w-full h-full flex items-center justify-center rounded-xl bg-gradient-to-br from-yellow-500 to-yellow-700 text-white">
                      <div className="text-center p-5">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-yellow-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <h4 className="text-xl font-bold mb-2">Hurtig support</h4>
                        <p className="text-sm opacity-80">Altid klar til at hjælpe</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-center lg:justify-start space-x-2 mt-6 sm:mt-8">
                  <div className="h-2 w-2 bg-blue-600 rounded-full"></div>
                  <div className="h-2 w-2 bg-blue-400 rounded-full"></div>
                  <div className="h-2 w-2 bg-blue-200 rounded-full"></div>
                </div>
              </div>

              <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 border border-gray-100">
                <div className="space-y-6">
                  <div className="flex items-center justify-center h-16 w-16 bg-blue-50 rounded-full mx-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                    </svg>
                  </div>
                  
                  <div className="text-center">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Fortæl om din oplevelse</h3>
                    <p className="text-gray-600 mb-6">
                      Vælg din foretrukne platform og del din oplevelse med vores produkter
                    </p>
                  </div>

                  <div className="space-y-4">
                    <Link
                      href="https://discord.mcdevhub.dk"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full bg-blue-600 text-white px-6 py-4 rounded-xl font-medium flex items-center justify-center group transition-all duration-300 hover:bg-blue-700 hover:shadow-lg hover:-translate-y-0.5"
                    >
                      <FaDiscord className="mr-2 transition-transform duration-300 group-hover:scale-105" />
                      Feedback via Discord
                    </Link>
                    
                    <Link
                      href="https://www.trustpilot.com"
                      className="w-full bg-green-600 text-white px-6 py-4 rounded-xl font-medium flex items-center justify-center transition-all duration-300 hover:bg-green-700 hover:shadow-lg hover:-translate-y-0.5"
                    >
                      Skriv anmeldelse på Trustpilot
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Partner Program Promotion */}
      <section className="py-16 sm:py-20 bg-gradient-to-r from-teal-600 to-emerald-700 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/pattern-dots.svg')] bg-repeat"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-10">
            <div className="lg:w-1/2 text-center lg:text-left">
              <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-teal-500/30 rounded-full backdrop-blur-sm text-teal-100 text-sm font-medium cursor-default">
                Eksklusivt for content creators og server ejere
              </div>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 leading-tight">
                Bliv Partner og få <span className="text-teal-200">gratis produkter</span> til din platform
              </h2>
              <p className="text-lg sm:text-xl text-teal-100 mb-6 sm:mb-8">
                Viser du Minecraft indhold på YouTube eller Twitch? Eller driver du en populær Minecraft server? Så kan du få adgang til vores produkter helt gratis.
              </p>
              <div className="flex flex-col sm:flex-row flex-wrap gap-4 justify-center lg:justify-start">
                <Link 
                  href="/partner-program" 
                  className="group relative bg-teal-500 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:bg-teal-600 hover:shadow-2xl hover:shadow-teal-500/20 w-full sm:w-auto text-center"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Se mere om partnerskab
                    <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5 group-hover:scale-110" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-teal-400 to-teal-500 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </Link>
                <Link 
                  href="https://discord.mcdevhub.dk" 
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group relative bg-teal-600/20 border-2 border-teal-400 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:bg-teal-600/40 hover:shadow-2xl hover:shadow-teal-500/20 w-full sm:w-auto text-center"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    <FaDiscord className="mr-2 text-xl" />
                    Join Discord
                  </span>
                  <div className="absolute inset-0 bg-teal-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </Link>
              </div>
            </div>
            <div className="lg:w-1/2 relative w-full mt-8 lg:mt-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="bg-teal-800/50 backdrop-blur-sm rounded-xl p-5 border border-teal-500/30 transform transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] hover:scale-[1.02] hover:shadow-lg hover:shadow-teal-500/20 hover:border-teal-400/50">
                  <FaYoutube className="text-4xl mb-3 text-white transition-transform duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110" />
                  <h3 className="text-xl font-bold mb-2">Content Creators</h3>
                  <p className="text-teal-100">Showcase vores produkter i dine videoer og streams, og brug dem helt gratis!</p>
                </div>
                <div className="bg-teal-800/50 backdrop-blur-sm rounded-xl p-5 border border-teal-500/30 transform transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] hover:scale-[1.02] hover:shadow-lg hover:shadow-teal-500/20 hover:border-teal-400/50">
                  <FaServer className="text-4xl mb-3 text-white transition-transform duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110" />
                  <h3 className="text-xl font-bold mb-2">Server Ejere</h3>
                  <p className="text-teal-100">Giv dine spillere en bedre oplevelse med vores plugins uden omkostninger.</p>
                </div>
                <div className="bg-teal-800/50 backdrop-blur-sm rounded-xl p-5 border border-teal-500/30 transform transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] hover:scale-[1.02] hover:shadow-lg hover:shadow-teal-500/20 hover:border-teal-400/50">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-3 text-white transition-transform duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <h3 className="text-xl font-bold mb-2">Prioriteret Support</h3>
                  <p className="text-teal-100">Få direkte adgang til vores udviklingsteam og hurtig hjælp når du har brug for det.</p>
                </div>
                <div className="bg-teal-800/50 backdrop-blur-sm rounded-xl p-5 border border-teal-500/30 transform transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] hover:scale-[1.02] hover:shadow-lg hover:shadow-teal-500/20 hover:border-teal-400/50">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-3 text-white transition-transform duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                  <h3 className="text-xl font-bold mb-2">Officiel Partner Status</h3>
                  <p className="text-teal-100">Bliv fremhævet på vores hjemmeside og få større eksponering for din platform.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 sm:py-20 md:py-24 bg-gradient-to-r from-blue-700 to-blue-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/cta-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 text-center relative z-10">
          <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/40 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium">
            Start i dag
          </div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6">
            Klar til at forbedre din Minecraft server?
          </h2>
          <p className="text-lg sm:text-xl text-blue-100 mb-8 sm:mb-10 max-w-2xl mx-auto">
            Lad os hjælpe dig med at skabe den perfekte oplevelse for dine spillere. Vores eksperter er klar til at udvikle præcis den løsning, du har brug for.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/custom-orders" 
              className="bg-white text-blue-900 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 hover:shadow-lg w-full sm:w-auto"
            >
              Bestil specialudvikling
            </Link>
            <Link 
              href="https://discord.mcdevhub.dk" 
              target="_blank"
              rel="noopener noreferrer"
              className="bg-indigo-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold hover:bg-indigo-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg flex items-center justify-center w-full sm:w-auto"
            >
              <FaDiscord className="mr-2 text-xl" />
              Join vores Discord
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
