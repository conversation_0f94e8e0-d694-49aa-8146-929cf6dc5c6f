'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect, useRef } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { FaChevronDown, FaHome, FaGavel, FaInfoCircle, FaHeadset, FaShoppingCart, FaHandshake, FaCode, FaDiscord, FaEnvelope, FaSignOutAlt, FaUser, FaChartLine, FaUserCircle, FaShieldAlt } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { signIn, signOut } from 'next-auth/react';
import { useAuth } from '@/context/AuthContext';
import NotificationsDropdown from './NotificationsDropdown';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [activeIndicator, setActiveIndicator] = useState({ left: 0, width: 0, top: 0 });
  const [previousActiveIndicator, setPreviousActiveIndicator] = useState({ left: 0, width: 0, top: 0 });
  const [avatarFailed, setAvatarFailed] = useState(false);
  const dropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const navItemRefs = useRef<{ [key: string]: HTMLElement | null }>({});
  const pathname = usePathname();
  const router = useRouter();
  const { session, user, refreshAuth, isAdmin } = useAuth();
  
  // Check if current page is privacy page, 404 page, products page, or developers page
  const isPrivacyPage = pathname === '/privatlivspolitik';
  const isProductsPage = pathname === '/products';
  const isDevelopersPage = pathname === '/developers' || pathname.startsWith('/developers/');
  const isNotFoundPage = pathname !== '/' && 
                        pathname !== '/om-os' && 
                        pathname !== '/kontakt' && 
                        pathname !== '/custom-orders' && 
                        pathname !== '/partner-program' && 
                        pathname !== '/tos' && 
                        pathname !== '/products' && 
                        !pathname.startsWith('/product/') && 
                        !pathname.startsWith('/category/') &&
                        !pathname.startsWith('/developers/');
  
  const isLightHeaderPage = isPrivacyPage || isNotFoundPage || isProductsPage || isDevelopersPage;
  const isTextDark = isScrolled || isLightHeaderPage;

  // Handle avatar loading errors
  const handleAvatarError = () => {
    setAvatarFailed(true);
  };

  // Track scrolling for navbar appearance change with smoother transition
  useEffect(() => {
    // Set initial state based on current scroll position
    if (window.scrollY > 20) {
      setIsScrolled(true);
    }
    
    const handleScroll = () => {
      // Add debounce to make transition smoother
      if (window.scrollY > 20) {
        if (!isScrolled) setIsScrolled(true);
      } else {
        if (isScrolled) setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isScrolled]);

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
    setActiveDropdown(null);
  }, [pathname]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!activeDropdown) return;
      
      const dropdownRef = dropdownRefs.current[activeDropdown];
      
      // Check if the click was outside the dropdown content
      if (dropdownRef && !dropdownRef.contains(event.target as Node)) {
        // Also ensure we're not clicking on the toggle button that would handle its own state
        const isClickOnToggle = event.target instanceof Element && 
          event.target.closest(`button[data-dropdown="${activeDropdown}"]`);
          
        if (!isClickOnToggle) {
          setActiveDropdown(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeDropdown]);

  // Lock body scroll when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isMenuOpen]);

  // Update active indicator position
  useEffect(() => {
    // Only run in browser
    if (typeof window === 'undefined') return;
    
    // Function to update the indicator with debounce
    let updateTimeout: NodeJS.Timeout | null = null;
    const debouncedUpdate = () => {
      if (updateTimeout) clearTimeout(updateTimeout);
      updateTimeout = setTimeout(() => {
        updateActiveIndicator();
      }, 100);
    };
    
    // Initial update
    updateActiveIndicator();
    
    // Only create ONE resize observer for the entire app
    const resizeObserver = new ResizeObserver(() => {
      debouncedUpdate();
    });
    
    // Only observe the body element - more efficient
    resizeObserver.observe(document.body);
    
    return () => {
      if (updateTimeout) clearTimeout(updateTimeout);
      resizeObserver.disconnect();
    };
  }, [pathname, isScrolled]);

  // Add a useEffect to refresh auth on initial load and when path changes
  useEffect(() => {
    // Only run if not already checked in the last minute
    const lastAuthCheck = sessionStorage.getItem('lastAuthCheck');
    const currentTime = Date.now();
    const needsCheck = !lastAuthCheck || (currentTime - parseInt(lastAuthCheck, 10)) > 300000; // Increase to 5 minutes
    
    const checkAuth = async () => {
      await refreshAuth();
      sessionStorage.setItem('lastAuthCheck', Date.now().toString());
    };
    
    // Check auth immediately when component mounts if needed
    if (needsCheck) {
      checkAuth();
    }
    
    // Set up an interval to periodically check auth status - with a much longer interval
    // Only one instance of this interval should exist
    const intervalId = setInterval(checkAuth, 10 * 60000); // Check every 10 minutes instead of 5 minutes
    
    // Clean up interval when component unmounts
    return () => clearInterval(intervalId);
  }, [refreshAuth]);

  // Add effect for admin status check
  useEffect(() => {
    // If the user is logged in, check their admin status
    if (user) {
      refreshAdminCheckForDropdown();
    }
  }, [user]); // This will run when the user changes (login/logout)

  // Listener for user state changes (removed debug logs)
  useEffect(() => {
    // Clean version without debug logs
  }, [user]);

  // Determines if a link is active
  const isActive = (path: string) => {
    // Special case for homepage to ensure exact match only
    if (path === '/') {
      return pathname === '/';
    }
    return pathname === path;
  };
  
  // Check if any paths in an array are active
  const isGroupActive = (paths: string[]) => paths.some(path => 
    path === pathname || (path !== '/' && pathname.startsWith(path))
  );

  // Get the active item key based on the current path
  const getActiveItemKey = () => {
    // Special case: if we're on partner-program or homepage, don't activate any navbar items
    if (pathname === '/partner-program' || pathname === '/') {
      return ''; // Return empty to avoid highlighting anything
    }
    
    // First check for exact matches
    for (const [key, item] of Object.entries(navLinks)) {
      if (item.type === 'link' && isActive(item.path)) {
        return key;
      }
      if (item.type === 'dropdown' && item.items) {
        for (const subItem of item.items) {
          if (isActive(subItem.path)) {
            return key;
          }
        }
      }
    }
    
    // Then check for partial matches for dropdown items
    for (const [key, item] of Object.entries(navLinks)) {
      if (item.type === 'dropdown' && item.items) {
        const pathsInGroup = item.items.map(subItem => subItem.path);
        if (isGroupActive(pathsInGroup)) {
          return key;
        }
      }
    }
    
    // Return empty for no highlight
    return '';
  };

  // Update the active indicator position based on the current pathname
  const updateActiveIndicator = () => {
    const activeKey = getActiveItemKey();
    
    // If no active key or we're on the partner page, hide the indicator
    if (!activeKey) {
      setActiveIndicator({ left: 0, width: 0, top: 0 });
      return;
    }
    
    const activeElement = navItemRefs.current[activeKey];
    
    if (activeElement) {
      const rect = activeElement.getBoundingClientRect();
      setPreviousActiveIndicator({ ...activeIndicator });
      setActiveIndicator({
        left: rect.left,
        width: rect.width,
        top: rect.top + rect.height - 2 // Position just below the element
      });
    }
  };

  // Toggle dropdown menu
  const toggleDropdown = (name: string, event?: React.MouseEvent) => {
    // If event is provided, stop it from propagating to prevent click outside handler from triggering
    if (event) {
      event.stopPropagation();
    }
    
    // Check if this is the user menu and we're opening it
    if (name === 'userMenu' && activeDropdown !== 'userMenu' && user) {
      // Refresh admin status when opening the user dropdown
      refreshAdminCheckForDropdown();
    }
    
    setActiveDropdown(activeDropdown === name ? null : name);
  };

  // New function to refresh admin status when opening user dropdown
  const refreshAdminCheckForDropdown = async () => {
    try {
      // Call our refresh endpoint
      const response = await fetch('/api/auth/refresh-admin-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      console.log('Admin status refresh:', data);
      
      // Admin status is now handled by NextAuth session
      console.log('Admin status check result:', data.isAdmin);
    } catch (error) {
      console.error('Error refreshing admin status:', error);
    }
  };

  const handleSignOut = async () => {
    try {
      console.log("Starting NextAuth sign out process");
      setActiveDropdown(null);
      await signOut({ callbackUrl: '/' });
    } catch (error) {
      console.error('Error during sign out:', error);
      // Fallback to redirect
      window.location.href = '/';
    }
  };

  // Structure for navbar links and dropdowns
  const navLinks = [
    {
      name: 'Forside',
      path: '/',
      icon: <FaHome className="mr-2" />,
      type: 'link'
    },
    {
      name: 'Produkter',
      path: '/products',
      icon: <FaShoppingCart className="mr-2" />,
      type: 'dropdown',
      items: [
        { name: 'Alle Produkter', path: '/products' },
        { name: 'Plugins', path: '/products?type=plugin' },
        { name: 'Skripts', path: '/products?type=script' },
        { name: 'Maps', path: '/products?type=map' }
      ]
    },
    {
      name: 'Om Os',
      path: '/om-o',
      icon: <FaInfoCircle className="mr-2" />,
      type: 'dropdown',
      items: [
        { name: 'Freelancers', path: '/developers' },
        { name: 'Om MCDevHub', path: '/om-os' }
      ]
    },
    {
      name: 'Kontakt',
      path: '/kontakt',
      icon: <FaEnvelope className="mr-2" />,
      type: 'dropdown',
      items: [
        { name: 'Support', path: '/kontakt' },
        { name: 'Specialbestillinger', path: '/custom-orders' },
        { name: 'Bliv Partner', path: '/partner-program' }
      ]
    },
    {
      name: 'Juridisk',
      path: '/tos',
      icon: <FaGavel className="mr-2" />,
      type: 'dropdown',
      items: [
        { name: 'Handelsbetingelser', path: '/tos' },
        { name: 'Privatlivspolitik', path: '/privatlivspolitik' }
      ]
    }
  ];

  return (
    <>
      {/* Fixed navbar */}
      <nav className={`fixed w-full z-50 transition-all duration-350 ease-in-out ${
        isScrolled || isLightHeaderPage
          ? 'bg-white/95 backdrop-blur-sm text-gray-800 shadow-lg py-2 sm:py-2' 
          : 'bg-transparent text-white py-3 sm:py-4'
      }`}>
        <div className="container mx-auto px-4 sm:px-6">
          <div className="flex justify-between items-center">
            {/* Logo */}
            <Link href="/" className="flex items-center group z-10">
              <Image 
                src="/images/McDevHub.png"
                alt="MCDevHub Logo"
                width={40}
                height={40}
                className="mr-2 sm:mr-3 brightness-100"
              />
              <span className={`text-lg sm:text-xl font-bold transition-all duration-500 ease-in-out group-hover:translate-x-1 ${
                isTextDark ? 'text-gray-800' : 'text-white'
              }`}>
                MCDevHub
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-1 lg:space-x-3 z-10">
              <div className="flex space-x-1 lg:space-x-3 relative">
                {/* Animated active indicator that moves between items */}
                <motion.div 
                  className={`absolute h-0.5 ${isTextDark ? 'bg-blue-600' : 'bg-white'} rounded-full z-0`}
                  initial={false}
                  animate={{
                    left: activeIndicator.left,
                    width: activeIndicator.width,
                    top: activeIndicator.top,
                  }}
                  transition={{
                    type: "spring",
                    stiffness: 350,
                    damping: 30
                  }}
                />

                {Object.entries(navLinks).map(([key, item], index) => {
                  // For regular links
                  if (item.type === 'link') {
                    return (
                      <Link 
                        key={index}
                        href={item.path} 
                        ref={(el) => { navItemRefs.current[key] = el; }}
                        className={`font-medium relative px-2 lg:px-3 py-2 rounded-md transition-all duration-300 ease-in-out text-sm lg:text-base ${
                          isActive(item.path) 
                            ? (isTextDark ? 'text-blue-600' : 'text-white font-bold') 
                            : (isTextDark ? 'text-gray-800 hover:text-blue-600 hover:bg-blue-50' : 'text-white hover:text-blue-200 hover:bg-white/10')
                        } group`}
                      >
                        <span className="flex items-center">
                          <span className="md:hidden lg:inline-block">{item.icon}</span>
                          {item.name}
                        </span>
                        <span className={`absolute bottom-0 left-0 h-0.5 ${isTextDark ? 'bg-blue-600' : 'bg-white'} transition-all duration-300 ease-in-out ${
                          isActive(item.path) ? 'w-full' : 'w-0 group-hover:w-full'
                        }`}></span>
                      </Link>
                    );
                  }
                  
                  // For dropdown menus
                  const dropdownPaths = item.items?.map(subItem => subItem.path) || [];
                  const isDropdownActive = isGroupActive(dropdownPaths);
                  
                  return (
                    <div 
                      key={index} 
                      className="relative"
                      ref={(el) => { dropdownRefs.current[item.name] = el; }}
                    >
                      <button
                        ref={(el) => { navItemRefs.current[key] = el; }}
                        onClick={(e) => toggleDropdown(item.name, e)}
                        data-dropdown={item.name}
                        className={`font-medium relative px-2 lg:px-3 py-2 rounded-md transition-all duration-300 ease-in-out text-sm lg:text-base ${
                          isDropdownActive
                            ? (isTextDark ? 'text-blue-600' : 'text-white font-bold') 
                            : (isTextDark ? 'text-gray-800 hover:text-blue-600 hover:bg-blue-50' : 'text-white hover:text-blue-200 hover:bg-white/10')
                        } flex items-center group`}
                      >
                        <span className="flex items-center">
                          <span className="md:hidden lg:inline-block">{item.icon}</span>
                          {item.name}
                        </span>
                        <motion.div
                          animate={{ rotate: activeDropdown === item.name ? 180 : 0 }}
                          transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
                          className="ml-1"
                        >
                          <FaChevronDown className="h-2.5 w-2.5 lg:h-3 lg:w-3" />
                        </motion.div>
                        <span className={`absolute bottom-0 left-0 h-0.5 ${isTextDark ? 'bg-blue-600' : 'bg-white'} transition-all duration-300 ease-in-out ${
                          isDropdownActive ? 'w-full' : 'w-0 group-hover:w-full'
                        }`}></span>
                      </button>
                      
                      {/* Dropdown Menu */}
                      <AnimatePresence>
                        {activeDropdown === item.name && (
                          <motion.div 
                            initial={{ opacity: 0, y: -10, scale: 0.95 }}
                            animate={{ opacity: 1, y: 0, scale: 1 }}
                            exit={{ opacity: 0, y: -10, scale: 0.95 }}
                            transition={{ duration: 0.2, ease: "easeInOut" }}
                            className="absolute top-full right-0 md:left-0 md:right-auto mt-1 bg-white rounded-md shadow-lg overflow-hidden"
                            style={{ minWidth: '200px', maxWidth: '90vw' }}
                          >
                            <div className="py-1">
                              {item.items?.map((subItem, subIndex) => (
                                <Link
                                  key={subIndex}
                                  href={subItem.path}
                                  className={`block px-4 py-2 text-sm ${
                                    isActive(subItem.path)
                                      ? 'bg-blue-50 text-blue-600 font-medium'
                                      : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                                  } transition-colors duration-200`}
                                >
                                  {subItem.name}
                                </Link>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  );
                })}
              </div>
              
              {/* Discord Login/User Profile */}
              {!user ? (
                <button
                  onClick={() => signIn('discord', { callbackUrl: '/' })}
                  className="group relative ml-1 sm:ml-2"
                >
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#5865F2] to-[#404EED] rounded-full blur-sm opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative bg-gradient-to-b from-[#5865F2] to-[#404EED] text-white px-4 sm:px-5 py-2 rounded-full flex items-center justify-center border-none transition-all duration-300 hover:scale-105 hover:shadow-lg">
                      <div className="w-8 h-8 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center mr-2">
                        <FaDiscord 
                          className="w-5 h-5"
                        />
                      </div>
                      <span className="text-sm font-medium">Log ind</span>
                    </div>
                  </div>
                </button>
              ) : (
                <>
                  {/* Notifications Dropdown - Only show when logged in */}
                  <div className="mr-2">
                    <NotificationsDropdown isTextDark={isTextDark} />
                  </div>
                
                  {/* User Profile Dropdown */}
                  <div className="relative group ml-1 sm:ml-2 z-50">
                    <button 
                      onClick={(e) => toggleDropdown('userMenu', e)}
                      data-dropdown="userMenu"
                      className="flex items-center space-x-2 py-1.5 px-3 rounded-full transition-all duration-300 hover:scale-105 hover:shadow-md"
                      style={{
                        background: activeDropdown === 'userMenu' 
                          ? 'linear-gradient(to bottom right, #4f47e6, #3182ce)' 
                          : 'transparent',
                        boxShadow: activeDropdown === 'userMenu' 
                          ? '0 4px 12px rgba(66, 153, 225, 0.2)' 
                          : 'none',
                      }}
                      onMouseEnter={(e) => {
                        if (activeDropdown !== 'userMenu') {
                          e.currentTarget.style.background = isTextDark 
                            ? 'rgba(59, 130, 246, 0.08)' 
                            : 'rgba(255, 255, 255, 0.15)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (activeDropdown !== 'userMenu') {
                          e.currentTarget.style.background = 'transparent';
                        }
                      }}
                    >
                      <div className="h-8 w-8 rounded-full overflow-hidden border-2 border-blue-400 transition-all duration-300 group-hover:border-blue-500">
                        {user?.image && !avatarFailed ? (
                          <Image
                            src={user.image}
                            alt={user.name || 'User'}
                            width={32}
                            height={32}
                            className="h-full w-full object-cover"
                            unoptimized // Add this to bypass image optimization for Discord CDN URLs
                            onError={handleAvatarError}
                          />
                        ) : (
                          <div className="h-full w-full bg-blue-400 flex items-center justify-center text-white group-hover:bg-blue-500 transition-colors duration-300">
                            <FaUser />
                          </div>
                        )}
                      </div>
                      <span className={`hidden md:block text-sm font-medium transition-all duration-300 ${
                        activeDropdown === 'userMenu' 
                          ? 'text-white' 
                          : isTextDark ? 'text-gray-800 group-hover:text-blue-600' : 'text-white'
                      }`}>
                        {user?.name || 'Bruger'}
                      </span>
                      <motion.div
                        animate={{ rotate: activeDropdown === 'userMenu' ? 180 : 0 }}
                        transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
                        className="ml-1"
                      >
                        <FaChevronDown className={`h-3 w-3 ${
                          activeDropdown === 'userMenu' 
                            ? 'text-white' 
                            : isTextDark ? 'text-gray-600 group-hover:text-blue-600' : 'text-white'
                        }`} />
                      </motion.div>
                    </button>
                    
                    {/* User Dropdown Menu */}
                    <AnimatePresence>
                      {activeDropdown === 'userMenu' && (
                        <motion.div 
                          initial={{ opacity: 0, y: 10, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: 10, scale: 0.95 }}
                          transition={{ duration: 0.2 }}
                          className="absolute right-0 mt-2 w-52 bg-white rounded-xl shadow-lg overflow-hidden z-50 border border-blue-100"
                          ref={(el) => { dropdownRefs.current['userMenu'] = el; }}
                        >
                          <div className="py-2">
                            <div className="px-4 py-2 border-b border-gray-100">
                              <p className="text-sm font-medium text-gray-600">Logget ind som</p>
                              <p className="text-sm font-semibold text-gray-800 truncate">{user?.email || user?.name}</p>
                            </div>
                            <Link 
                              href="/profile" 
                              className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150"
                            >
                              <FaUser className="mr-2 text-blue-500" />
                              Min Profil
                            </Link>
                            {isAdmin && (
                            <Link
                              href="/admin/login"
                              className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2z" />
                              </svg>
                              Dashboard
                            </Link>
                            )}
                            <div className="border-t border-gray-100 my-1"></div>
                            <button
                              onClick={handleSignOut}
                              className="flex items-center w-full text-left px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 transition-colors duration-150"
                            >
                              <FaSignOutAlt className="mr-2" />
                              Log ud
                            </button>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button 
              className={`md:hidden relative z-60 focus:outline-none transition-all duration-300 ease-in-out p-2 -mr-2`}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
            >
              <div className={`w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${
                isMenuOpen ? 'transform rotate-45 translate-y-2 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'
              }`}></div>
              <div className={`w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${
                isMenuOpen ? 'opacity-0 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'
              }`}></div>
              <div className={`w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${
                isMenuOpen ? 'transform -rotate-45 -translate-y-2 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'
              }`}></div>
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay - Separate from navbar */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div 
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            transition={{ duration: 0.25, ease: "easeInOut" }}
            className="fixed inset-0 bg-white z-40 overflow-y-auto"
          >
            <div className="container mx-auto px-4 pt-20 sm:pt-24 pb-8 h-full flex flex-col">
              <div className="flex flex-col space-y-1 sm:space-y-2 text-center text-base sm:text-lg">
                {Object.entries(navLinks).map(([key, item], index) => {
                  if (item.type === 'link') {
                    return (
                      <Link 
                        key={index}
                        href={item.path} 
                        className={`py-2.5 sm:py-3 px-4 transition-all duration-300 ease-in-out ${isActive(item.path) ? 'text-blue-600 font-bold' : 'text-gray-800 hover:text-blue-600'}`}
                      >
                        <span className="relative group flex justify-center items-center">
                          {item.icon}{item.name}
                          <span className={`absolute -bottom-1 left-0 h-0.5 bg-blue-600 transition-all duration-300 ease-in-out ${isActive(item.path) ? 'w-full' : 'w-0 sm:group-hover:w-full'}`}></span>
                        </span>
                      </Link>
                    );
                  }
                  
                  // For accordion-style mobile dropdown
                  const isDropdownActive = item.items?.some(subItem => isActive(subItem.path));
                  return (
                    <div key={index} className="border-b border-gray-100 pb-2 mb-2 last:border-0">
                      <button
                        onClick={(e) => toggleDropdown(item.name, e)}
                        data-dropdown={item.name}
                        className={`w-full py-2.5 sm:py-3 px-4 transition-all duration-300 ease-in-out ${
                          isDropdownActive || activeDropdown === item.name ? 'text-blue-600 font-bold' : 'text-gray-800'
                        } flex items-center justify-center`}
                      >
                        <span className="flex items-center">{item.icon}{item.name}</span>
                        <motion.div
                          animate={{ rotate: activeDropdown === item.name ? 180 : 0 }}
                          transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
                          className="ml-2"
                        >
                          <FaChevronDown className="h-3 w-3" />
                        </motion.div>
                      </button>
                      
                      {/* Mobile Dropdown Items */}
                      <motion.div 
                        animate={{ 
                          height: activeDropdown === item.name ? "auto" : 0,
                          opacity: activeDropdown === item.name ? 1 : 0 
                        }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="overflow-hidden"
                      >
                        <div className="py-2 flex flex-col space-y-1">
                          {item.items?.map((subItem, subIndex) => (
                            <Link 
                              key={subIndex}
                              href={subItem.path}
                              className={`block py-2 px-4 sm:px-8 text-sm ${
                                isActive(subItem.path)
                                  ? 'bg-blue-50 text-blue-600 font-medium'
                                  : 'text-gray-600 hover:bg-gray-50 hover:text-blue-600'
                              } transition-colors duration-200 rounded-md mx-4 sm:mx-8`}
                            >
                              {subItem.name}
                            </Link>
                          ))}
                        </div>
                      </motion.div>
                    </div>
                  );
                })}
              </div>
              
              {/* Mobile CTA */}
              <div className="mt-auto pb-4 sm:pb-8">
                {/* User profile section for mobile - Only shown when logged in */}
                {user && (
                  <div className="mb-6 border-t border-gray-100 pt-6">
                    <div className="flex items-center justify-center mb-4">
                      <div className="h-16 w-16 rounded-full overflow-hidden border-2 border-blue-400 mr-3">
                        {user?.image ? (
                          <Image
                            src={user.image}
                            alt={user.name || 'User'}
                            width={64}
                            height={64}
                            className="h-full w-full object-cover"
                            unoptimized
                          />
                        ) : (
                          <div className="h-full w-full bg-blue-400 flex items-center justify-center text-white">
                            <FaUser size={24} />
                          </div>
                        )}
                      </div>
                      <div className="text-left">
                        <h3 className="font-bold text-lg text-gray-800">
                          {user?.name || 'Bruger'}
                        </h3>
                        <p className="text-sm text-gray-500">{user?.email}</p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3 mb-4">
                      <Link 
                        href="/profile" 
                        className="py-2 px-4 bg-gray-100 text-gray-800 rounded-lg text-center font-medium hover:bg-gray-200 transition-colors"
                      >
                        Min Profil
                      </Link>
                      {isAdmin && (
                      <Link
                        href="/admin/login"
                        className="py-2 px-4 bg-gray-100 text-gray-800 rounded-lg text-center font-medium hover:bg-gray-200 transition-colors"
                      >
                        Dashboard
                      </Link>
                      )}
                    </div>
                    
                    <button 
                      onClick={handleSignOut}
                      className="w-full py-3 px-4 bg-red-50 text-red-600 rounded-lg font-bold flex items-center justify-center hover:bg-red-100 transition-colors duration-300 hover:shadow-md"
                    >
                      <FaSignOutAlt className="mr-2" />
                      Log ud
                    </button>
                  </div>
                )}
                
                {/* Login with Discord button - Only shown when not logged in */}
                {!user && (
                  <button
                    onClick={() => signIn('discord', { callbackUrl: '/' })}
                    className="block w-full py-2.5 sm:py-3 px-4 bg-[#5865F2] text-white text-center rounded-lg font-bold text-base sm:text-lg transition-all duration-300 ease-in-out hover:bg-[#4752c4] shadow-md hover:shadow-lg transform hover:scale-[1.02] mb-6 flex items-center justify-center"
                  >
                    <FaDiscord className="mr-2 text-lg" />
                    Log ind med Discord
                  </button>
                )}
                
                {/* Note: We keep these options in the Contact dropdown menu, these should not be standalone buttons on mobile */}
                {/* Social Links */}
                <div className="flex justify-center mt-6 sm:mt-8 space-x-6">
                  <a href="https://discord.mcdevhub.dk" target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-blue-600 transition-colors duration-300">
                    <FaDiscord className="w-8 h-8 sm:w-10 sm:h-10" />
                  </a>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Navbar; 