'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Fa<PERSON><PERSON>ch, <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaCheckCircle } from 'react-icons/fa';
import { useAuth } from '@/context/AuthContext';

interface SearchResult {
  username: string;
  discordUserId: string;
  avatar?: string;
  avatarUrl?: string;
  isFreelancer: boolean;
  isAdmin: boolean;
  isVerified: boolean;
  createdAt: string;
  type: 'admin' | 'user';
}

interface UserSearchProps {
  placeholder?: string;
  className?: string;
  onUserSelect?: (user: SearchResult) => void;
  showResults?: boolean;
}

const UserSearch: React.FC<UserSearchProps> = ({
  placeholder = 'Søg efter brugere...',
  className = '',
  onUserSelect,
  showResults = true
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { user } = useAuth();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Search users with debounce
  useEffect(() => {
    const searchUsers = async () => {
      if (!query.trim() || query.length < 2) {
        setResults([]);
        setShowDropdown(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/users/search?q=${encodeURIComponent(query)}&limit=8`);
        
        if (!response.ok) {
          throw new Error('Søgning fejlede');
        }

        const data = await response.json();
        setResults(data.users || []);
        setShowDropdown(true);
      } catch (err) {
        console.error('Search error:', err);
        setError('Der opstod en fejl ved søgning');
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchUsers, 300);
    return () => clearTimeout(debounceTimer);
  }, [query]);

  const handleUserSelect = (selectedUser: SearchResult) => {
    setQuery('');
    setShowDropdown(false);
    setResults([]);
    
    if (onUserSelect) {
      onUserSelect(selectedUser);
    }
  };

  const getUserProfileUrl = (user: SearchResult) => {
    // Prefer the new username-based route for Discord users
    if (user.username) {
      return `/profile/${user.username}`;
    }
    // Fallback to the old routes
    if (user.isFreelancer) {
      return `/developers/${user.username}`;
    }
    return `/users/${user.discordUserId}`;
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          {isLoading ? (
            <FaSpinner className="h-4 w-4 text-gray-400 animate-spin" />
          ) : (
            <FaSearch className="h-4 w-4 text-gray-400" />
          )}
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => {
            if (results.length > 0) {
              setShowDropdown(true);
            }
          }}
          placeholder={placeholder}
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      {/* Search Results Dropdown */}
      {showDropdown && showResults && (
        <div className="absolute z-50 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
          {error && (
            <div className="px-4 py-2 text-sm text-red-600">
              {error}
            </div>
          )}
          
          {!error && results.length === 0 && !isLoading && query.length >= 2 && (
            <div className="px-4 py-2 text-sm text-gray-500">
              Ingen brugere fundet
            </div>
          )}
          
          {results.map((result) => (
            <Link
              key={result.discordUserId}
              href={getUserProfileUrl(result)}
              onClick={() => handleUserSelect(result)}
              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
            >
              <div className="flex items-center space-x-3">
                {/* Avatar */}
                <div className="flex-shrink-0 w-8 h-8">
                  {result.avatarUrl ? (
                    <Image
                      src={result.avatarUrl}
                      alt={result.username}
                      width={32}
                      height={32}
                      className="w-8 h-8 rounded-full object-cover"
                      unoptimized
                    />
                  ) : result.discordUserId && result.avatar ? (
                    <Image
                      src={`https://cdn.discordapp.com/avatars/${result.discordUserId}/${result.avatar}.png?size=64`}
                      alt={result.username}
                      width={32}
                      height={32}
                      className="w-8 h-8 rounded-full object-cover"
                      unoptimized
                    />
                  ) : (
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <FaUser className="w-4 h-4 text-gray-500" />
                    </div>
                  )}
                </div>
                
                {/* User Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {result.username}
                    </p>
                    
                    {/* Badges */}
                    {result.isAdmin && (
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Admin
                      </span>
                    )}
                    
                    {result.isFreelancer && (
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Freelancer
                      </span>
                    )}
                    
                    {result.isVerified && (
                      <FaCheckCircle className="w-3 h-3 text-blue-500" title="Verificeret" />
                    )}
                  </div>
                  
                  <p className="text-xs text-gray-500">
                    Medlem siden {new Date(result.createdAt).toLocaleDateString('da-DK', { 
                      year: 'numeric', 
                      month: 'short' 
                    })}
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

export default UserSearch;
