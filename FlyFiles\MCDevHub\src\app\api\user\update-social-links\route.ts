import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { MongoClient } from 'mongodb';
import { triggerBadgeCheck } from '@/lib/utils/badgeUtils';

export async function POST(request: NextRequest) {
  try {
    // Get the data from the request
    const data = await request.json();
    const { type, value } = data;
    
    // Validate input
    if (!type || (type !== 'github' && type !== 'youtube' && type !== 'email')) {
      return NextResponse.json({ error: 'Invalid type' }, { status: 400 });
    }
    
    // Get the user session from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }
    
    // Connect to MongoDB
    const uri = process.env.MONGODB_URI;
    if (!uri) {
      throw new Error('MongoDB URI is not defined');
    }
    
    const client = new MongoClient(uri);
    await client.connect();
    
    const db = client.db('mcdevhub');
    const adminUsers = db.collection('adminusers');
    
    // Get Discord user information from session
    const discordId = session.user.id;
    const discordUsername = session.user.name;
    const discordEmail = session.user.email;
    const discordAvatar = session.user.image;
    
    // Prepare the update object based on the type
    const updateObject = {};
    if (type === 'github') {
      updateObject['githubUsername'] = value;
    } else if (type === 'youtube') {
      updateObject['youtubeUrl'] = value;
    } else if (type === 'email') {
      updateObject['email'] = value;
    }
    
    // Check if user exists first
    const existingUser = await adminUsers.findOne({ discordUserId: discordId });
    
    if (!existingUser) {
      // User doesn't exist, create a new user document with basic information
      const newUser = {
        discordUserId: discordId,
        username: discordUsername,
        email: type === 'email' ? value : discordEmail, // Use the new email if that's what we're updating
        avatar: discordAvatar,
        createdAt: new Date(),
        ...updateObject
      };
      
      const insertResult = await adminUsers.insertOne(newUser);
      
      await client.close();

      // Trigger badge check for new user
      triggerBadgeCheck(discordId, 'social_link_update').catch(error => {
        console.error('Error checking badges for new user:', error);
      });

      return NextResponse.json({
        success: true,
        message: 'New user created with social link',
        isNewUser: true
      });
    } else {
      // User exists, update their social links
      const result = await adminUsers.updateOne(
        { discordUserId: discordId },
        { $set: updateObject }
      );
      
      await client.close();

      // Trigger badge check after social link update
      triggerBadgeCheck(discordId, 'social_link_update').catch(error => {
        console.error('Error checking badges after social link update:', error);
      });

      return NextResponse.json({
        success: true,
        message: 'Social link updated',
        isNewUser: false
      });
    }
  } catch (error) {
    console.error('Error updating social link:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
} 