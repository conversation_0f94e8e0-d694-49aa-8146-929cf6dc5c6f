import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import * as notificationUtils from '../../../../lib/notifications';

// Timeout for the request in milliseconds (10 seconds)
const REQUEST_TIMEOUT = 10000;

// Helper function to add timeout to a promise
const timeoutPromise = (promise, ms) => {
  return Promise.race([
    promise,
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error(`Request timed out after ${ms}ms`)), ms)
    )
  ]);
};

// GET /api/user/notifications - Get user notifications
export async function GET(request: NextRequest) {
  // Add a timeout to the entire request
  return timeoutPromise(handleGetRequest(request), REQUEST_TIMEOUT)
    .catch(error => {
      console.error('Notifications API request timed out:', error);
      return NextResponse.json({ 
        success: false, 
        error: 'Request timed out. Please try again later.' 
      }, { status: 504 });
    });
}

// Separate the actual handler logic for cleaner error handling
async function handleGetRequest(request: NextRequest) {
  try {
    console.log('Notifications API: Request received');
    
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      console.log('Notifications API: Unauthorized request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      console.log('Notifications API: No Discord ID found');
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    console.log(`Notifications API: Fetching notifications for user ${discordId.slice(0, 6)}...`);
    
    // Get notifications using the utility function with a limit to prevent excessive data fetching
    const result = await notificationUtils.getNotifications(discordId, { limit: 50 });
    
    if (!result.success) {
      console.error('Notifications API: Failed to fetch notifications:', result.error);
      return NextResponse.json({ 
        error: result.error || 'Failed to get notifications' 
      }, { status: 500 });
    }
    
    console.log(`Notifications API: Successfully fetched ${result.notifications?.length || 0} notifications`);
    
    return NextResponse.json({
      success: true,
      notifications: result.notifications || []
    });
  } catch (error) {
    console.error('Error in notifications API:', error);
    return NextResponse.json({ 
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}

// POST /api/user/notifications/mark-read - Mark notifications as read
export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const data = await request.json();
    const { notificationId } = data;
    
    if (!notificationId) {
      return NextResponse.json({ error: 'Notification ID is required' }, { status: 400 });
    }
    
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Mark notification as read using the utility function
    const result = await notificationUtils.markNotificationAsRead(discordId, notificationId);
    
    if (!result.success) {
      return NextResponse.json({ error: result.error || 'Notification not found' }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Notification marked as read',
      modified: result.modified
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 