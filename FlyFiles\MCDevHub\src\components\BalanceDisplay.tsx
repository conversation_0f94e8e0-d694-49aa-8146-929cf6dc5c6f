import React, { useState, useEffect } from 'react';
import { FaWallet, FaSync, FaExclamationCircle } from 'react-icons/fa';

interface BalanceDisplayProps {
  className?: string;
}

export default function BalanceDisplay({ className }: BalanceDisplayProps) {
  const [balance, setBalance] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Check if we should use white text (for the credit card display)
  const isWhiteText = className?.includes('text-white');

  const fetchBalance = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/users/balance');
      
      if (!response.ok) {
        // If the response is 403, it means the user is not an admin
        if (response.status === 403) {
          setBalance(null);
          setLoading(false);
          return;
        }
        
        throw new Error('Failed to fetch balance');
      }
      
      const data = await response.json();
      setBalance(data.balance);
      setLastUpdated(new Date(data.lastUpdated));
    } catch (err) {
      console.error('Error fetching balance:', err);
      setError('Could not load balance');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBalance();
  }, []);

  // If there's an error or balance is null, don't display anything
  if (error || balance === null) return null;

  // If we're in credit card mode (has text-white class), render a simplified version
  if (className?.includes('bg-transparent')) {
    return (
      <div className={className}>
        {loading ? (
          <div className="animate-pulse h-7 w-24 bg-white bg-opacity-20 rounded"></div>
        ) : (
          <div className={`text-xl font-bold ${isWhiteText ? 'text-white' : 'text-gray-800'}`}>
            {balance.toLocaleString('da-DK')} <span className={`${isWhiteText ? 'text-white opacity-80' : 'text-gray-500'} text-lg`}>DKK</span>
          </div>
        )}
      </div>
    );
  }

  // Default display for normal mode (not in credit card)
  return (
    <div className={`bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden ${className || ''}`}>
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-5 py-4">
        <h3 className="font-semibold text-white flex items-center">
          <FaWallet className="mr-2" /> 
          Din Balance
        </h3>
      </div>
      
      <div className="p-5">
        {loading ? (
          <div className="flex items-center justify-center py-6">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-700"></div>
            <span className="ml-3 text-gray-600">Indlæser balance...</span>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <div className="text-3xl font-bold text-gray-800 mb-1">
              {balance.toLocaleString('da-DK')} <span className="text-gray-500 text-xl">DKK</span>
            </div>
            <div className="text-sm text-gray-500 mb-4">
              {lastUpdated && (
                <span>Senest opdateret: {lastUpdated.toLocaleString('da-DK')}</span>
              )}
            </div>
            <button 
              onClick={fetchBalance}
              className="flex items-center text-sm text-indigo-600 hover:text-indigo-800 transition-colors"
            >
              <FaSync className="mr-1 h-3 w-3" /> Opdater balance
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 