/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configuration for redirects
  async redirects() {
    return [
      {
        source: '/admin',
        destination: '/admin/dashboard',
        permanent: true,
      },
      {
        source: '/freelance',
        destination: '/admin/dashboard',
        permanent: true,
      },
    ]
  },
  // Allow images from Discord's CDN
  images: {
    domains: [
      'cdn.discordapp.com',
      'media.discordapp.net',
      'i.imgur.com'
    ]
  },
}

module.exports = nextConfig 