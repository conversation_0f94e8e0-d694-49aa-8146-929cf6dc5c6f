import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { verifyAdminToken } from '@/lib/auth';
import { ObjectId, GridFSBucket } from 'mongodb';
import { Readable } from 'stream';
import { uploadImage } from '@/lib/imageUpload';
import { triggerBadgeCheck } from '@/lib/utils/badgeUtils';

// Set higher body size limit for file uploads (50MB)
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
  },
};

export async function POST(request) {
  try {
    // Verify the admin user
    const admin = await verifyAdminToken();
    if (!admin) {
      return NextResponse.json(
        { message: 'Ikke autoriseret' },
        { status: 401 }
      );
    }

    // Only freelancers can upload products
    if (admin.admintype !== 'Freelancer') {
      return NextResponse.json(
        { message: 'Kun freelancere kan uploade produkter' },
        { status: 403 }
      );
    }

    // Parse the multipart form data
    const formData = await request.formData();
    
    // Extract form fields
    const projectName = formData.get('projectName');
    const projectDescription = formData.get('projectDescription');
    const version = formData.get('version');
    const productType = formData.get('productType');
    const price = formData.get('price') || '0';
    const isPremium = formData.get('isPremium') === 'true';
    // Get addons data from the form
    let addons = [];
    try {
      const addonsString = formData.get('addons');
      if (addonsString) {
        addons = JSON.parse(addonsString);
        // Validate addons structure
        if (!Array.isArray(addons)) {
          addons = [];
        }
      }
    } catch (error) {
      console.error('Error parsing addons data:', error);
      // Continue with empty addons array
    }

    // Validate required fields
    if (!projectName || !projectDescription || !version || !productType) {
      return NextResponse.json(
        { message: 'Manglende påkrævede felter' },
        { status: 400 }
      );
    }

    // Get screenshots and product files
    const screenshots = formData.getAll('screenshots');
    const files = formData.getAll('files');

    // Validate that we have at least one screenshot and one file
    if (screenshots.length === 0 || files.length === 0) {
      return NextResponse.json(
        { message: 'Mindst ét screenshot og én fil er påkrævet' },
        { status: 400 }
      );
    }

    // Connect to database
    const { db } = await connectToDatabase();

    // Create a new product entry with a temporary ID to use in file paths
    const tempId = new ObjectId();
    
    // Create arrays to store file references
    const screenshotUrls = [];
    const fileUrls = [];

    // Upload screenshots
    for (const screenshot of screenshots) {
      try {
        // Get the file data
        const buffer = await screenshot.arrayBuffer();
        const filename = `${tempId.toString()}_screenshot_${screenshot.name}`;
        
        // Upload to our custom API
        const result = await uploadImage(
          Buffer.from(buffer),
          'screenshots',
          filename,
          screenshot.type
        );
        
        if ('error' in result) {
          console.error('API upload error:', result.error);
          
          // Create a placeholder URL - using a more realistic pattern
          // Note: These URLs won't actually work, but they will be stored in the database
          // so the UI can show something. In production, you'll need to implement a proper solution.
          const staticPath = `/static/placeholders/screenshot-${Date.now()}.jpg`;
          console.log('Creating placeholder static URL:', staticPath);
          
          screenshotUrls.push({
            fileId: `placeholder_${tempId.toString()}_${screenshot.name}`,
            filename: screenshot.name,
            contentType: screenshot.type,
            url: staticPath,
            isPlaceholder: true, // Flag to indicate this is a placeholder
            uploadError: result.error // Store the original error for reference
          });
          
          // Log information about the failed upload
          console.warn(`Using placeholder for screenshot due to upload error: ${result.error}`);
        } else {
          // Upload succeeded, use the real URL
          screenshotUrls.push({
            fileId: result.path,
            filename: screenshot.name,
            contentType: screenshot.type,
            url: result.url
          });
        }
      } catch (uploadError) {
        console.error('Error uploading screenshot:', uploadError);
        
        // Create a placeholder for failed uploads
        const staticPath = `/static/placeholders/screenshot-${Date.now()}.jpg`;
        
        screenshotUrls.push({
          fileId: `placeholder_${tempId.toString()}_${screenshot.name}`,
          filename: screenshot.name,
          contentType: screenshot.type,
          url: staticPath,
          isPlaceholder: true,
          uploadError: uploadError.message
        });
      }
    }
    
    // If all screenshot uploads failed with the same error, return that error
    if (screenshotUrls.length === 0) {
      return NextResponse.json(
        { message: 'Alle screenshots fejlede ved upload. Kontroller venligst din server konfiguration.' },
        { status: 500 }
      );
    }
    
    // Upload product files to MongoDB
    for (const file of files) {
      try {
        // Get the file data
        const buffer = await file.arrayBuffer();
        const fileId = new ObjectId();
        const filename = file.name;
        
        // Store file in MongoDB GridFS
        const { db } = await connectToDatabase();
        const bucket = new GridFSBucket(db, { bucketName: 'product_files' });
        
        // Create upload stream
        const uploadStream = bucket.openUploadStream(filename, {
          contentType: file.type,
          metadata: {
            originalName: filename,
            uploadedBy: admin._id,
            productId: tempId,
            uploadDate: new Date()
          }
        });
        
        // Handle upload completion
        await new Promise((resolve, reject) => {
          uploadStream.on('error', reject);
          uploadStream.on('finish', resolve);
          
          // Write the buffer to the upload stream
          const readable = new Readable();
          readable.push(Buffer.from(buffer));
          readable.push(null);
          readable.pipe(uploadStream);
        });
        
        // Add file reference to the array
        fileUrls.push({
          fileId: uploadStream.id.toString(),
          filename: filename,
          contentType: file.type,
          size: buffer.byteLength,
          uploadDate: new Date()
        });
        
      } catch (uploadError) {
        console.error('Error uploading file to MongoDB:', uploadError);
        
        // Create a placeholder for failed uploads
        const staticPath = `/static/placeholders/file-${Date.now()}.zip`;
        
        // Add error information to the response
        fileUrls.push({
          fileId: `placeholder_${tempId.toString()}_${file.name}`,
          filename: file.name,
          contentType: file.type,
          url: staticPath,
          isPlaceholder: true,
          error: 'Kunne ikke uploade filen',
          errorDetails: uploadError.message
        });
      }
    }
    
    // If all file uploads failed with the same error, return that error
    if (fileUrls.length === 0) {
      return NextResponse.json(
        { message: 'Alle filer fejlede ved upload. Kontroller venligst din server konfiguration.' },
        { status: 500 }
      );
    }
    
    // Create the product document with file references
    const product = {
      projectName,
      projectDescription,
      version,
      productType,
      price: parseInt(price, 10),
      isPremium,
      createdBy: admin.username,
      discordUserId: admin.discordUserId || '',
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'pending', // pending, approved, rejected
      screenshotUrls,
      fileUrls,
      addons: addons // Add the addons array to the product document
    };
    
    // Insert the product into MongoDB
    const result = await db.collection('products').insertOne(product);
    const productId = result.insertedId;

    // Trigger badge check for freelancer after product upload
    if (admin.discordUserId) {
      triggerBadgeCheck(admin.discordUserId, 'product_upload').catch(error => {
        console.error('Error checking badges after product upload:', error);
      });
    }

    // Return success response with the product ID
    return NextResponse.json({ 
      success: true,
      message: 'Produkt uploadet succesfuldt',
      productId: productId.toString()
    });
    
  } catch (error) {
    console.error('Error uploading product:', error);
    return NextResponse.json(
      { 
        success: false,
        message: 'Der opstod en fejl under upload af produkt: ' + error.message 
      },
      { status: 500 }
    );
  }
}