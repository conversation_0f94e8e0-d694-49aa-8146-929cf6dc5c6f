'use client';

import { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON><PERSON>, Fa<PERSON><PERSON><PERSON>, FaTimes, FaExclamationTriangle } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { format, formatDistanceToNow } from 'date-fns';
import { da } from 'date-fns/locale';

interface Notification {
  _id: string;
  title: string;
  message: string;
  date: string;
  read?: boolean;
  readAt?: string;
  type?: string;
  link?: string;
}

interface NotificationsDropdownProps {
  isTextDark: boolean;
}

const NotificationsDropdown: React.FC<NotificationsDropdownProps> = ({ isTextDark }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get unread notifications count
  const unreadCount = notifications.filter(notification => !notification.read).length;

  // Fetch notifications
  const fetchNotifications = async (retryCount = 0) => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('Dropdown: Fetching notifications...');
      const response = await fetch('/api/user/notifications', {
        // Adding cache: 'no-store' to prevent caching issues
        cache: 'no-store',
        // Setting a longer timeout
        signal: AbortSignal.timeout(15000) // 15 seconds timeout
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch notifications: ${response.status} ${errorText}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log(`Dropdown: Successfully fetched ${data.notifications.length} notifications`);
        setNotifications(data.notifications);
      } else {
        throw new Error(data.error || 'Failed to fetch notifications');
      }
    } catch (error) {
      console.error('Error fetching notifications in dropdown:', error);
      
      // If it's a timeout or network error and we haven't retried too many times, retry
      if ((error instanceof Error && 
          (error.name === 'AbortError' || 
           error.message.includes('network') || 
           error.message.includes('timeout'))) && 
          retryCount < 2) {
        
        console.log(`Dropdown: Retrying notification fetch (attempt ${retryCount + 1})...`);
        
        // Wait a bit before retrying (exponential backoff)
        const backoffTime = Math.pow(2, retryCount) * 1000;
        setTimeout(() => {
          fetchNotifications(retryCount + 1);
        }, backoffTime);
        
        return;
      }
      
      setError(error instanceof Error ? error.message : 'Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  // Mark notification as read
  const markAsRead = async (notificationId: string) => {
    console.log('Marking notification as read from dropdown:', notificationId);
    
    try {
      const response = await fetch('/api/user/notifications/mark-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notificationId }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        console.log('Successfully marked notification as read:', data);
        
        // Update local state
        setNotifications(prevNotifications => 
          prevNotifications.map(notification => 
            notification._id === notificationId 
              ? { ...notification, read: true, readAt: new Date().toISOString() } 
              : notification
          )
        );
        
        // Dispatch a custom event to notify other components
        const event = new CustomEvent('notificationRead', {
          detail: { notificationId }
        });
        window.dispatchEvent(event);
      } else {
        console.error('Server responded with error:', data);
        throw new Error(data.error || 'Failed to mark notification as read');
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      
      // If date is less than 24 hours ago, show relative time
      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
      
      if (diffInHours < 24) {
        return formatDistanceToNow(date, { addSuffix: true, locale: da });
      }
      
      // Otherwise show date in format "10. jan 2023 13:45"
      return format(date, "d. MMM yyyy HH:mm", { locale: da });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen && dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Listen for the allNotificationsRead event
  useEffect(() => {
    const handleAllNotificationsRead = () => {
      console.log('Dropdown: Received allNotificationsRead event');
      setNotifications(prevNotifications => 
        prevNotifications.map(notification => ({
          ...notification,
          read: true,
          readAt: new Date().toISOString()
        }))
      );
    };
    
    window.addEventListener('allNotificationsRead', handleAllNotificationsRead);
    
    return () => {
      window.removeEventListener('allNotificationsRead', handleAllNotificationsRead);
    };
  }, []);

  // Fetch notifications when component mounts
  useEffect(() => {
    // Fetch notifications immediately when component mounts
    fetchNotifications();
    
    // Optional: Set up auto-refresh every 60 seconds (1 minute)
    const intervalId = setInterval(() => {
      fetchNotifications();
    }, 60000);
    
    // Listen for the notificationRead event from the profile page
    const handleNotificationRead = (event: CustomEvent) => {
      const { notificationId } = event.detail;
      console.log('NotificationsDropdown received notificationRead event:', notificationId);
      
      // Update local state to reflect the change
      setNotifications(prevNotifications => 
        prevNotifications.map(notification => 
          notification._id === notificationId 
            ? { ...notification, read: true, readAt: new Date().toISOString() } 
            : notification
        )
      );
    };
    
    // Add event listener
    window.addEventListener('notificationRead', handleNotificationRead as EventListener);
    
    return () => {
      clearInterval(intervalId);
      // Remove event listener
      window.removeEventListener('notificationRead', handleNotificationRead as EventListener);
    };
  }, []);

  // Refetch notifications when dropdown is opened
  useEffect(() => {
    if (isOpen) {
      fetchNotifications();
    }
  }, [isOpen]);

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Notification Button */}
      <button
        onClick={toggleDropdown}
        className={`relative p-2 flex items-center justify-center rounded-full transition-all duration-300 hover:scale-105 ${
          isOpen
            ? 'bg-blue-500 text-white'
            : isTextDark
            ? 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'
            : 'text-white hover:bg-white/10'
        }`}
        aria-label="Notifications"
      >
        {unreadCount > 0 ? (
          <FaBell className="h-5 w-5" />
        ) : (
          <FaRegBell className="h-5 w-5" />
        )}
        
        {/* Notification Badge */}
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-white text-xs font-bold transform translate-x-1/2 -translate-y-1/3">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notifications Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-80 bg-white rounded-xl shadow-lg overflow-hidden z-50 border border-blue-100"
          >
            <div className="py-2">
              {/* Header */}
              <div className="px-4 py-2 border-b border-gray-100 flex justify-between items-center">
                <h3 className="font-medium text-gray-800">Notifikationer</h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
                >
                  <FaTimes className="h-4 w-4" />
                </button>
              </div>

              {/* Notifications List */}
              <div className="max-h-80 overflow-y-auto">
                {loading ? (
                  <div className="py-8 text-center">
                    <FaSpinner className="animate-spin mx-auto h-8 w-8 text-blue-500 mb-2" />
                    <p className="text-gray-500">Indlæser notifikationer...</p>
                  </div>
                ) : error ? (
                  <div className="py-8 text-center">
                    <FaExclamationTriangle className="mx-auto h-8 w-8 text-orange-500 mb-2" />
                    <p className="text-gray-700">Kunne ikke indlæse notifikationer</p>
                    <div className="mt-2 text-center">
                      <p className="text-red-500 text-sm">{error}</p>
                      <button 
                        className="mt-2 text-blue-500 hover:text-blue-600 text-sm font-medium"
                        onClick={() => fetchNotifications(0)}
                      >
                        Prøv igen
                      </button>
                    </div>
                  </div>
                ) : notifications.length === 0 ? (
                  <div className="py-8 text-center">
                    <div className="mx-auto h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mb-3">
                      <FaBell className="h-6 w-6 text-blue-500" />
                    </div>
                    <p className="text-gray-700">Ingen notifikationer</p>
                    <p className="text-gray-500 text-sm mt-1">Du har ingen notifikationer lige nu</p>
                  </div>
                ) : (
                  notifications.map((notification) => (
                    <div
                      key={notification._id}
                      className={`px-4 py-3 border-b border-gray-100 last:border-b-0 ${
                        !notification.read ? 'bg-blue-50' : ''
                      }`}
                      onClick={() => !notification.read && markAsRead(notification._id)}
                    >
                      <div className="flex justify-between items-start mb-1">
                        <h4 className={`text-sm font-medium ${!notification.read ? 'text-blue-800' : 'text-gray-800'}`}>
                          {notification.title}
                        </h4>
                        <span className="text-xs text-gray-500">
                          {formatDate(notification.date)}
                        </span>
                      </div>
                      <p className={`text-sm ${!notification.read ? 'text-blue-700' : 'text-gray-600'}`}>
                        {notification.message}
                      </p>
                      {notification.link && (
                        <a 
                          href={notification.link} 
                          className="text-xs text-blue-600 hover:text-blue-800 font-medium mt-1 inline-block"
                          onClick={(e) => e.stopPropagation()}
                        >
                          Se mere
                        </a>
                      )}
                    </div>
                  ))
                )}
              </div>

              {/* Footer - Only show if there are notifications */}
              {notifications.length > 0 && (
                <div className="px-4 py-2 border-t border-gray-100">
                  <a 
                    href="/profile?tab=notifications" 
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium block text-center"
                  >
                    Se alle notifikationer
                  </a>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default NotificationsDropdown; 