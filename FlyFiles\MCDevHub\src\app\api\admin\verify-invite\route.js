import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function GET(request) {
  try {
    // Extract token from query parameters
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { message: 'Token er påkrævet' },
        { status: 400 }
      );
    }

    // Connect to the database
    const { db } = await connectToDatabase();

    // Find the invitation
    const invitation = await db.collection('invitations').findOne({
      token,
      used: { $ne: true },
      expiresAt: { $gt: new Date() }
    });

    if (!invitation) {
      return NextResponse.json(
        { message: 'Ugyldigt eller udløbet invitationslink' },
        { status: 404 }
      );
    }

    // Format the invitation data to return
    const formattedInvitation = {
      admintype: invitation.admintype,
      allowedcases: invitation.allowedcases,
      discordUserId: invitation.discordUserId,
      description: invitation.description || '',
      createdBy: invitation.createdBy,
      expiresAt: invitation.expiresAt
    };

    return NextResponse.json({
      message: 'Invitationslink verificeret',
      invitation: formattedInvitation
    });
  } catch (error) {
    console.error('Error verifying invitation token:', error);
    return NextResponse.json(
      { message: 'Der opstod en fejl ved verificering af invitationslink' },
      { status: 500 }
    );
  }
} 