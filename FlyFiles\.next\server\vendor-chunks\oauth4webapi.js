"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth4webapi";
exports.ids = ["vendor-chunks/oauth4webapi"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth4webapi/build/index.js":
/*!**************************************************!*\
  !*** ./node_modules/oauth4webapi/build/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTHORIZATION_RESPONSE_ERROR: () => (/* binding */ AUTHORIZATION_RESPONSE_ERROR),\n/* harmony export */   AuthorizationResponseError: () => (/* binding */ AuthorizationResponseError),\n/* harmony export */   ClientSecretBasic: () => (/* binding */ ClientSecretBasic),\n/* harmony export */   ClientSecretJwt: () => (/* binding */ ClientSecretJwt),\n/* harmony export */   ClientSecretPost: () => (/* binding */ ClientSecretPost),\n/* harmony export */   DPoP: () => (/* binding */ DPoP),\n/* harmony export */   HTTP_REQUEST_FORBIDDEN: () => (/* binding */ HTTP_REQUEST_FORBIDDEN),\n/* harmony export */   INVALID_REQUEST: () => (/* binding */ INVALID_REQUEST),\n/* harmony export */   INVALID_RESPONSE: () => (/* binding */ INVALID_RESPONSE),\n/* harmony export */   INVALID_SERVER_METADATA: () => (/* binding */ INVALID_SERVER_METADATA),\n/* harmony export */   JSON_ATTRIBUTE_COMPARISON: () => (/* binding */ JSON_ATTRIBUTE_COMPARISON),\n/* harmony export */   JWT_CLAIM_COMPARISON: () => (/* binding */ JWT_CLAIM_COMPARISON),\n/* harmony export */   JWT_TIMESTAMP_CHECK: () => (/* binding */ JWT_TIMESTAMP_CHECK),\n/* harmony export */   JWT_USERINFO_EXPECTED: () => (/* binding */ JWT_USERINFO_EXPECTED),\n/* harmony export */   KEY_SELECTION: () => (/* binding */ KEY_SELECTION),\n/* harmony export */   MISSING_SERVER_METADATA: () => (/* binding */ MISSING_SERVER_METADATA),\n/* harmony export */   None: () => (/* binding */ None),\n/* harmony export */   OperationProcessingError: () => (/* binding */ OperationProcessingError),\n/* harmony export */   PARSE_ERROR: () => (/* binding */ PARSE_ERROR),\n/* harmony export */   PrivateKeyJwt: () => (/* binding */ PrivateKeyJwt),\n/* harmony export */   REQUEST_PROTOCOL_FORBIDDEN: () => (/* binding */ REQUEST_PROTOCOL_FORBIDDEN),\n/* harmony export */   RESPONSE_BODY_ERROR: () => (/* binding */ RESPONSE_BODY_ERROR),\n/* harmony export */   RESPONSE_IS_NOT_CONFORM: () => (/* binding */ RESPONSE_IS_NOT_CONFORM),\n/* harmony export */   RESPONSE_IS_NOT_JSON: () => (/* binding */ RESPONSE_IS_NOT_JSON),\n/* harmony export */   ResponseBodyError: () => (/* binding */ ResponseBodyError),\n/* harmony export */   TlsClientAuth: () => (/* binding */ TlsClientAuth),\n/* harmony export */   UNSUPPORTED_OPERATION: () => (/* binding */ UNSUPPORTED_OPERATION),\n/* harmony export */   UnsupportedOperationError: () => (/* binding */ UnsupportedOperationError),\n/* harmony export */   WWWAuthenticateChallengeError: () => (/* binding */ WWWAuthenticateChallengeError),\n/* harmony export */   WWW_AUTHENTICATE_CHALLENGE: () => (/* binding */ WWW_AUTHENTICATE_CHALLENGE),\n/* harmony export */   _expectedIssuer: () => (/* binding */ _expectedIssuer),\n/* harmony export */   _nodiscoverycheck: () => (/* binding */ _nodiscoverycheck),\n/* harmony export */   _nopkce: () => (/* binding */ _nopkce),\n/* harmony export */   allowInsecureRequests: () => (/* binding */ allowInsecureRequests),\n/* harmony export */   authorizationCodeGrantRequest: () => (/* binding */ authorizationCodeGrantRequest),\n/* harmony export */   backchannelAuthenticationGrantRequest: () => (/* binding */ backchannelAuthenticationGrantRequest),\n/* harmony export */   backchannelAuthenticationRequest: () => (/* binding */ backchannelAuthenticationRequest),\n/* harmony export */   calculatePKCECodeChallenge: () => (/* binding */ calculatePKCECodeChallenge),\n/* harmony export */   checkProtocol: () => (/* binding */ checkProtocol),\n/* harmony export */   clientCredentialsGrantRequest: () => (/* binding */ clientCredentialsGrantRequest),\n/* harmony export */   clockSkew: () => (/* binding */ clockSkew),\n/* harmony export */   clockTolerance: () => (/* binding */ clockTolerance),\n/* harmony export */   customFetch: () => (/* binding */ customFetch),\n/* harmony export */   deviceAuthorizationRequest: () => (/* binding */ deviceAuthorizationRequest),\n/* harmony export */   deviceCodeGrantRequest: () => (/* binding */ deviceCodeGrantRequest),\n/* harmony export */   discoveryRequest: () => (/* binding */ discoveryRequest),\n/* harmony export */   dynamicClientRegistrationRequest: () => (/* binding */ dynamicClientRegistrationRequest),\n/* harmony export */   expectNoNonce: () => (/* binding */ expectNoNonce),\n/* harmony export */   expectNoState: () => (/* binding */ expectNoState),\n/* harmony export */   formPostResponse: () => (/* binding */ formPostResponse),\n/* harmony export */   generateKeyPair: () => (/* binding */ generateKeyPair),\n/* harmony export */   generateRandomCodeVerifier: () => (/* binding */ generateRandomCodeVerifier),\n/* harmony export */   generateRandomNonce: () => (/* binding */ generateRandomNonce),\n/* harmony export */   generateRandomState: () => (/* binding */ generateRandomState),\n/* harmony export */   genericTokenEndpointRequest: () => (/* binding */ genericTokenEndpointRequest),\n/* harmony export */   getContentType: () => (/* binding */ getContentType),\n/* harmony export */   getValidatedIdTokenClaims: () => (/* binding */ getValidatedIdTokenClaims),\n/* harmony export */   introspectionRequest: () => (/* binding */ introspectionRequest),\n/* harmony export */   isDPoPNonceError: () => (/* binding */ isDPoPNonceError),\n/* harmony export */   issueRequestObject: () => (/* binding */ issueRequestObject),\n/* harmony export */   jweDecrypt: () => (/* binding */ jweDecrypt),\n/* harmony export */   jwksCache: () => (/* binding */ jwksCache),\n/* harmony export */   modifyAssertion: () => (/* binding */ modifyAssertion),\n/* harmony export */   nopkce: () => (/* binding */ nopkce),\n/* harmony export */   processAuthorizationCodeResponse: () => (/* binding */ processAuthorizationCodeResponse),\n/* harmony export */   processBackchannelAuthenticationGrantResponse: () => (/* binding */ processBackchannelAuthenticationGrantResponse),\n/* harmony export */   processBackchannelAuthenticationResponse: () => (/* binding */ processBackchannelAuthenticationResponse),\n/* harmony export */   processClientCredentialsResponse: () => (/* binding */ processClientCredentialsResponse),\n/* harmony export */   processDeviceAuthorizationResponse: () => (/* binding */ processDeviceAuthorizationResponse),\n/* harmony export */   processDeviceCodeResponse: () => (/* binding */ processDeviceCodeResponse),\n/* harmony export */   processDiscoveryResponse: () => (/* binding */ processDiscoveryResponse),\n/* harmony export */   processDynamicClientRegistrationResponse: () => (/* binding */ processDynamicClientRegistrationResponse),\n/* harmony export */   processGenericTokenEndpointResponse: () => (/* binding */ processGenericTokenEndpointResponse),\n/* harmony export */   processIntrospectionResponse: () => (/* binding */ processIntrospectionResponse),\n/* harmony export */   processPushedAuthorizationResponse: () => (/* binding */ processPushedAuthorizationResponse),\n/* harmony export */   processRefreshTokenResponse: () => (/* binding */ processRefreshTokenResponse),\n/* harmony export */   processResourceDiscoveryResponse: () => (/* binding */ processResourceDiscoveryResponse),\n/* harmony export */   processRevocationResponse: () => (/* binding */ processRevocationResponse),\n/* harmony export */   processUserInfoResponse: () => (/* binding */ processUserInfoResponse),\n/* harmony export */   protectedResourceRequest: () => (/* binding */ protectedResourceRequest),\n/* harmony export */   pushedAuthorizationRequest: () => (/* binding */ pushedAuthorizationRequest),\n/* harmony export */   refreshTokenGrantRequest: () => (/* binding */ refreshTokenGrantRequest),\n/* harmony export */   resolveEndpoint: () => (/* binding */ resolveEndpoint),\n/* harmony export */   resourceDiscoveryRequest: () => (/* binding */ resourceDiscoveryRequest),\n/* harmony export */   revocationRequest: () => (/* binding */ revocationRequest),\n/* harmony export */   skipAuthTimeCheck: () => (/* binding */ skipAuthTimeCheck),\n/* harmony export */   skipStateCheck: () => (/* binding */ skipStateCheck),\n/* harmony export */   skipSubjectCheck: () => (/* binding */ skipSubjectCheck),\n/* harmony export */   userInfoRequest: () => (/* binding */ userInfoRequest),\n/* harmony export */   validateApplicationLevelSignature: () => (/* binding */ validateApplicationLevelSignature),\n/* harmony export */   validateAuthResponse: () => (/* binding */ validateAuthResponse),\n/* harmony export */   validateCodeIdTokenResponse: () => (/* binding */ validateCodeIdTokenResponse),\n/* harmony export */   validateDetachedSignatureResponse: () => (/* binding */ validateDetachedSignatureResponse),\n/* harmony export */   validateJwtAccessToken: () => (/* binding */ validateJwtAccessToken),\n/* harmony export */   validateJwtAuthResponse: () => (/* binding */ validateJwtAuthResponse)\n/* harmony export */ });\nlet USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'oauth4webapi';\n    const VERSION = 'v3.6.1';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nfunction looseInstanceOf(input, expected) {\n    if (input == null) {\n        return false;\n    }\n    try {\n        return (input instanceof expected ||\n            Object.getPrototypeOf(input)[Symbol.toStringTag] === expected.prototype[Symbol.toStringTag]);\n    }\n    catch {\n        return false;\n    }\n}\nconst ERR_INVALID_ARG_VALUE = 'ERR_INVALID_ARG_VALUE';\nconst ERR_INVALID_ARG_TYPE = 'ERR_INVALID_ARG_TYPE';\nfunction CodedTypeError(message, code, cause) {\n    const err = new TypeError(message, { cause });\n    Object.assign(err, { code });\n    return err;\n}\nconst allowInsecureRequests = Symbol();\nconst clockSkew = Symbol();\nconst clockTolerance = Symbol();\nconst customFetch = Symbol();\nconst modifyAssertion = Symbol();\nconst jweDecrypt = Symbol();\nconst jwksCache = Symbol();\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nfunction buf(input) {\n    if (typeof input === 'string') {\n        return encoder.encode(input);\n    }\n    return decoder.decode(input);\n}\nlet encodeBase64Url;\nif (Uint8Array.prototype.toBase64) {\n    encodeBase64Url = (input) => {\n        if (input instanceof ArrayBuffer) {\n            input = new Uint8Array(input);\n        }\n        return input.toBase64({ alphabet: 'base64url', omitPadding: true });\n    };\n}\nelse {\n    const CHUNK_SIZE = 0x8000;\n    encodeBase64Url = (input) => {\n        if (input instanceof ArrayBuffer) {\n            input = new Uint8Array(input);\n        }\n        const arr = [];\n        for (let i = 0; i < input.byteLength; i += CHUNK_SIZE) {\n            arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n        }\n        return btoa(arr.join('')).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n    };\n}\nlet decodeBase64Url;\nif (Uint8Array.fromBase64) {\n    decodeBase64Url = (input) => {\n        try {\n            return Uint8Array.fromBase64(input, { alphabet: 'base64url' });\n        }\n        catch (cause) {\n            throw CodedTypeError('The input to be decoded is not correctly encoded.', ERR_INVALID_ARG_VALUE, cause);\n        }\n    };\n}\nelse {\n    decodeBase64Url = (input) => {\n        try {\n            const binary = atob(input.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, ''));\n            const bytes = new Uint8Array(binary.length);\n            for (let i = 0; i < binary.length; i++) {\n                bytes[i] = binary.charCodeAt(i);\n            }\n            return bytes;\n        }\n        catch (cause) {\n            throw CodedTypeError('The input to be decoded is not correctly encoded.', ERR_INVALID_ARG_VALUE, cause);\n        }\n    };\n}\nfunction b64u(input) {\n    if (typeof input === 'string') {\n        return decodeBase64Url(input);\n    }\n    return encodeBase64Url(input);\n}\nclass UnsupportedOperationError extends Error {\n    code;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = UNSUPPORTED_OPERATION;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass OperationProcessingError extends Error {\n    code;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        if (options?.code) {\n            this.code = options?.code;\n        }\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nfunction OPE(message, code, cause) {\n    return new OperationProcessingError(message, { code, cause });\n}\nfunction assertCryptoKey(key, it) {\n    if (!(key instanceof CryptoKey)) {\n        throw CodedTypeError(`${it} must be a CryptoKey`, ERR_INVALID_ARG_TYPE);\n    }\n}\nfunction assertPrivateKey(key, it) {\n    assertCryptoKey(key, it);\n    if (key.type !== 'private') {\n        throw CodedTypeError(`${it} must be a private CryptoKey`, ERR_INVALID_ARG_VALUE);\n    }\n}\nfunction assertPublicKey(key, it) {\n    assertCryptoKey(key, it);\n    if (key.type !== 'public') {\n        throw CodedTypeError(`${it} must be a public CryptoKey`, ERR_INVALID_ARG_VALUE);\n    }\n}\nfunction normalizeTyp(value) {\n    return value.toLowerCase().replace(/^application\\//, '');\n}\nfunction isJsonObject(input) {\n    if (input === null || typeof input !== 'object' || Array.isArray(input)) {\n        return false;\n    }\n    return true;\n}\nfunction prepareHeaders(input) {\n    if (looseInstanceOf(input, Headers)) {\n        input = Object.fromEntries(input.entries());\n    }\n    const headers = new Headers(input ?? {});\n    if (USER_AGENT && !headers.has('user-agent')) {\n        headers.set('user-agent', USER_AGENT);\n    }\n    if (headers.has('authorization')) {\n        throw CodedTypeError('\"options.headers\" must not include the \"authorization\" header name', ERR_INVALID_ARG_VALUE);\n    }\n    return headers;\n}\nfunction signal(url, value) {\n    if (value !== undefined) {\n        if (typeof value === 'function') {\n            value = value(url.href);\n        }\n        if (!(value instanceof AbortSignal)) {\n            throw CodedTypeError('\"options.signal\" must return or be an instance of AbortSignal', ERR_INVALID_ARG_TYPE);\n        }\n        return value;\n    }\n    return undefined;\n}\nfunction replaceDoubleSlash(pathname) {\n    if (pathname.includes('//')) {\n        return pathname.replace('//', '/');\n    }\n    return pathname;\n}\nfunction prependWellKnown(url, wellKnown, allowTerminatingSlash = false) {\n    if (url.pathname === '/') {\n        url.pathname = wellKnown;\n    }\n    else {\n        url.pathname = replaceDoubleSlash(`${wellKnown}/${allowTerminatingSlash ? url.pathname : url.pathname.replace(/(\\/)$/, '')}`);\n    }\n    return url;\n}\nfunction appendWellKnown(url, wellKnown) {\n    url.pathname = replaceDoubleSlash(`${url.pathname}/${wellKnown}`);\n    return url;\n}\nasync function performDiscovery(input, urlName, transform, options) {\n    if (!(input instanceof URL)) {\n        throw CodedTypeError(`\"${urlName}\" must be an instance of URL`, ERR_INVALID_ARG_TYPE);\n    }\n    checkProtocol(input, options?.[allowInsecureRequests] !== true);\n    const url = transform(new URL(input.href));\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body: undefined,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: signal(url, options?.signal),\n    });\n}\nasync function discoveryRequest(issuerIdentifier, options) {\n    return performDiscovery(issuerIdentifier, 'issuerIdentifier', (url) => {\n        switch (options?.algorithm) {\n            case undefined:\n            case 'oidc':\n                appendWellKnown(url, '.well-known/openid-configuration');\n                break;\n            case 'oauth2':\n                prependWellKnown(url, '.well-known/oauth-authorization-server');\n                break;\n            default:\n                throw CodedTypeError('\"options.algorithm\" must be \"oidc\" (default), or \"oauth2\"', ERR_INVALID_ARG_VALUE);\n        }\n        return url;\n    }, options);\n}\nfunction assertNumber(input, allow0, it, code, cause) {\n    try {\n        if (typeof input !== 'number' || !Number.isFinite(input)) {\n            throw CodedTypeError(`${it} must be a number`, ERR_INVALID_ARG_TYPE, cause);\n        }\n        if (input > 0)\n            return;\n        if (allow0) {\n            if (input !== 0) {\n                throw CodedTypeError(`${it} must be a non-negative number`, ERR_INVALID_ARG_VALUE, cause);\n            }\n            return;\n        }\n        throw CodedTypeError(`${it} must be a positive number`, ERR_INVALID_ARG_VALUE, cause);\n    }\n    catch (err) {\n        if (code) {\n            throw OPE(err.message, code, cause);\n        }\n        throw err;\n    }\n}\nfunction assertString(input, it, code, cause) {\n    try {\n        if (typeof input !== 'string') {\n            throw CodedTypeError(`${it} must be a string`, ERR_INVALID_ARG_TYPE, cause);\n        }\n        if (input.length === 0) {\n            throw CodedTypeError(`${it} must not be empty`, ERR_INVALID_ARG_VALUE, cause);\n        }\n    }\n    catch (err) {\n        if (code) {\n            throw OPE(err.message, code, cause);\n        }\n        throw err;\n    }\n}\nasync function processDiscoveryResponse(expectedIssuerIdentifier, response) {\n    const expected = expectedIssuerIdentifier;\n    if (!(expected instanceof URL) && expected !== _nodiscoverycheck) {\n        throw CodedTypeError('\"expectedIssuerIdentifier\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform Authorization Server Metadata response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.issuer, '\"response\" body \"issuer\" property', INVALID_RESPONSE, { body: json });\n    if (expected !== _nodiscoverycheck && new URL(json.issuer).href !== expected.href) {\n        throw OPE('\"response\" body \"issuer\" property does not match the expected value', JSON_ATTRIBUTE_COMPARISON, { expected: expected.href, body: json, attribute: 'issuer' });\n    }\n    return json;\n}\nfunction assertApplicationJson(response) {\n    assertContentType(response, 'application/json');\n}\nfunction notJson(response, ...types) {\n    let msg = '\"response\" content-type must be ';\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `${types.join(', ')}, or ${last}`;\n    }\n    else if (types.length === 2) {\n        msg += `${types[0]} or ${types[1]}`;\n    }\n    else {\n        msg += types[0];\n    }\n    return OPE(msg, RESPONSE_IS_NOT_JSON, response);\n}\nfunction assertContentTypes(response, ...types) {\n    if (!types.includes(getContentType(response))) {\n        throw notJson(response, ...types);\n    }\n}\nfunction assertContentType(response, contentType) {\n    if (getContentType(response) !== contentType) {\n        throw notJson(response, contentType);\n    }\n}\nfunction randomBytes() {\n    return b64u(crypto.getRandomValues(new Uint8Array(32)));\n}\nfunction generateRandomCodeVerifier() {\n    return randomBytes();\n}\nfunction generateRandomState() {\n    return randomBytes();\n}\nfunction generateRandomNonce() {\n    return randomBytes();\n}\nasync function calculatePKCECodeChallenge(codeVerifier) {\n    assertString(codeVerifier, 'codeVerifier');\n    return b64u(await crypto.subtle.digest('SHA-256', buf(codeVerifier)));\n}\nfunction getKeyAndKid(input) {\n    if (input instanceof CryptoKey) {\n        return { key: input };\n    }\n    if (!(input?.key instanceof CryptoKey)) {\n        return {};\n    }\n    if (input.kid !== undefined) {\n        assertString(input.kid, '\"kid\"');\n    }\n    return {\n        key: input.key,\n        kid: input.kid,\n    };\n}\nfunction psAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'PS256';\n        case 'SHA-384':\n            return 'PS384';\n        case 'SHA-512':\n            return 'PS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name', {\n                cause: key,\n            });\n    }\n}\nfunction rsAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'RS256';\n        case 'SHA-384':\n            return 'RS384';\n        case 'SHA-512':\n            return 'RS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name', {\n                cause: key,\n            });\n    }\n}\nfunction esAlg(key) {\n    switch (key.algorithm.namedCurve) {\n        case 'P-256':\n            return 'ES256';\n        case 'P-384':\n            return 'ES384';\n        case 'P-521':\n            return 'ES512';\n        default:\n            throw new UnsupportedOperationError('unsupported EcKeyAlgorithm namedCurve', { cause: key });\n    }\n}\nfunction keyToJws(key) {\n    switch (key.algorithm.name) {\n        case 'RSA-PSS':\n            return psAlg(key);\n        case 'RSASSA-PKCS1-v1_5':\n            return rsAlg(key);\n        case 'ECDSA':\n            return esAlg(key);\n        case 'Ed25519':\n        case 'EdDSA':\n            return 'Ed25519';\n        default:\n            throw new UnsupportedOperationError('unsupported CryptoKey algorithm name', { cause: key });\n    }\n}\nfunction getClockSkew(client) {\n    const skew = client?.[clockSkew];\n    return typeof skew === 'number' && Number.isFinite(skew) ? skew : 0;\n}\nfunction getClockTolerance(client) {\n    const tolerance = client?.[clockTolerance];\n    return typeof tolerance === 'number' && Number.isFinite(tolerance) && Math.sign(tolerance) !== -1\n        ? tolerance\n        : 30;\n}\nfunction epochTime() {\n    return Math.floor(Date.now() / 1000);\n}\nfunction assertAs(as) {\n    if (typeof as !== 'object' || as === null) {\n        throw CodedTypeError('\"as\" must be an object', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(as.issuer, '\"as.issuer\"');\n}\nfunction assertClient(client) {\n    if (typeof client !== 'object' || client === null) {\n        throw CodedTypeError('\"client\" must be an object', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(client.client_id, '\"client.client_id\"');\n}\nfunction formUrlEncode(token) {\n    return encodeURIComponent(token).replace(/(?:[-_.!~*'()]|%20)/g, (substring) => {\n        switch (substring) {\n            case '-':\n            case '_':\n            case '.':\n            case '!':\n            case '~':\n            case '*':\n            case \"'\":\n            case '(':\n            case ')':\n                return `%${substring.charCodeAt(0).toString(16).toUpperCase()}`;\n            case '%20':\n                return '+';\n            default:\n                throw new Error();\n        }\n    });\n}\nfunction ClientSecretPost(clientSecret) {\n    assertString(clientSecret, '\"clientSecret\"');\n    return (_as, client, body, _headers) => {\n        body.set('client_id', client.client_id);\n        body.set('client_secret', clientSecret);\n    };\n}\nfunction ClientSecretBasic(clientSecret) {\n    assertString(clientSecret, '\"clientSecret\"');\n    return (_as, client, _body, headers) => {\n        const username = formUrlEncode(client.client_id);\n        const password = formUrlEncode(clientSecret);\n        const credentials = btoa(`${username}:${password}`);\n        headers.set('authorization', `Basic ${credentials}`);\n    };\n}\nfunction clientAssertionPayload(as, client) {\n    const now = epochTime() + getClockSkew(client);\n    return {\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n        sub: client.client_id,\n    };\n}\nfunction PrivateKeyJwt(clientPrivateKey, options) {\n    const { key, kid } = getKeyAndKid(clientPrivateKey);\n    assertPrivateKey(key, '\"clientPrivateKey.key\"');\n    return async (as, client, body, _headers) => {\n        const header = { alg: keyToJws(key), kid };\n        const payload = clientAssertionPayload(as, client);\n        options?.[modifyAssertion]?.(header, payload);\n        body.set('client_id', client.client_id);\n        body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n        body.set('client_assertion', await signJwt(header, payload, key));\n    };\n}\nfunction ClientSecretJwt(clientSecret, options) {\n    assertString(clientSecret, '\"clientSecret\"');\n    const modify = options?.[modifyAssertion];\n    let key;\n    return async (as, client, body, _headers) => {\n        key ||= await crypto.subtle.importKey('raw', buf(clientSecret), { hash: 'SHA-256', name: 'HMAC' }, false, ['sign']);\n        const header = { alg: 'HS256' };\n        const payload = clientAssertionPayload(as, client);\n        modify?.(header, payload);\n        const data = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(payload)))}`;\n        const hmac = await crypto.subtle.sign(key.algorithm, key, buf(data));\n        body.set('client_id', client.client_id);\n        body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n        body.set('client_assertion', `${data}.${b64u(new Uint8Array(hmac))}`);\n    };\n}\nfunction None() {\n    return (_as, client, body, _headers) => {\n        body.set('client_id', client.client_id);\n    };\n}\nfunction TlsClientAuth() {\n    return None();\n}\nasync function signJwt(header, payload, key) {\n    if (!key.usages.includes('sign')) {\n        throw CodedTypeError('CryptoKey instances used for signing assertions must include \"sign\" in their \"usages\"', ERR_INVALID_ARG_VALUE);\n    }\n    const input = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(payload)))}`;\n    const signature = b64u(await crypto.subtle.sign(keyToSubtle(key), key, buf(input)));\n    return `${input}.${signature}`;\n}\nasync function issueRequestObject(as, client, parameters, privateKey, options) {\n    assertAs(as);\n    assertClient(client);\n    parameters = new URLSearchParams(parameters);\n    const { key, kid } = getKeyAndKid(privateKey);\n    assertPrivateKey(key, '\"privateKey.key\"');\n    parameters.set('client_id', client.client_id);\n    const now = epochTime() + getClockSkew(client);\n    const claims = {\n        ...Object.fromEntries(parameters.entries()),\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n    };\n    let resource;\n    if (parameters.has('resource') &&\n        (resource = parameters.getAll('resource')) &&\n        resource.length > 1) {\n        claims.resource = resource;\n    }\n    {\n        let value = parameters.get('max_age');\n        if (value !== null) {\n            claims.max_age = parseInt(value, 10);\n            assertNumber(claims.max_age, true, '\"max_age\" parameter');\n        }\n    }\n    {\n        let value = parameters.get('claims');\n        if (value !== null) {\n            try {\n                claims.claims = JSON.parse(value);\n            }\n            catch (cause) {\n                throw OPE('failed to parse the \"claims\" parameter as JSON', PARSE_ERROR, cause);\n            }\n            if (!isJsonObject(claims.claims)) {\n                throw CodedTypeError('\"claims\" parameter must be a JSON with a top level object', ERR_INVALID_ARG_VALUE);\n            }\n        }\n    }\n    {\n        let value = parameters.get('authorization_details');\n        if (value !== null) {\n            try {\n                claims.authorization_details = JSON.parse(value);\n            }\n            catch (cause) {\n                throw OPE('failed to parse the \"authorization_details\" parameter as JSON', PARSE_ERROR, cause);\n            }\n            if (!Array.isArray(claims.authorization_details)) {\n                throw CodedTypeError('\"authorization_details\" parameter must be a JSON with a top level array', ERR_INVALID_ARG_VALUE);\n            }\n        }\n    }\n    const header = {\n        alg: keyToJws(key),\n        typ: 'oauth-authz-req+jwt',\n        kid,\n    };\n    options?.[modifyAssertion]?.(header, claims);\n    return signJwt(header, claims, key);\n}\nlet jwkCache;\nasync function getSetPublicJwkCache(key) {\n    const { kty, e, n, x, y, crv } = await crypto.subtle.exportKey('jwk', key);\n    const jwk = { kty, e, n, x, y, crv };\n    jwkCache.set(key, jwk);\n    return jwk;\n}\nasync function publicJwk(key) {\n    jwkCache ||= new WeakMap();\n    return jwkCache.get(key) || getSetPublicJwkCache(key);\n}\nconst URLParse = URL.parse\n    ?\n        (url, base) => URL.parse(url, base)\n    : (url, base) => {\n        try {\n            return new URL(url, base);\n        }\n        catch {\n            return null;\n        }\n    };\nfunction checkProtocol(url, enforceHttps) {\n    if (enforceHttps && url.protocol !== 'https:') {\n        throw OPE('only requests to HTTPS are allowed', HTTP_REQUEST_FORBIDDEN, url);\n    }\n    if (url.protocol !== 'https:' && url.protocol !== 'http:') {\n        throw OPE('only HTTP and HTTPS requests are allowed', REQUEST_PROTOCOL_FORBIDDEN, url);\n    }\n}\nfunction validateEndpoint(value, endpoint, useMtlsAlias, enforceHttps) {\n    let url;\n    if (typeof value !== 'string' || !(url = URLParse(value))) {\n        throw OPE(`authorization server metadata does not contain a valid ${useMtlsAlias ? `\"as.mtls_endpoint_aliases.${endpoint}\"` : `\"as.${endpoint}\"`}`, value === undefined ? MISSING_SERVER_METADATA : INVALID_SERVER_METADATA, { attribute: useMtlsAlias ? `mtls_endpoint_aliases.${endpoint}` : endpoint });\n    }\n    checkProtocol(url, enforceHttps);\n    return url;\n}\nfunction resolveEndpoint(as, endpoint, useMtlsAlias, enforceHttps) {\n    if (useMtlsAlias && as.mtls_endpoint_aliases && endpoint in as.mtls_endpoint_aliases) {\n        return validateEndpoint(as.mtls_endpoint_aliases[endpoint], endpoint, useMtlsAlias, enforceHttps);\n    }\n    return validateEndpoint(as[endpoint], endpoint, useMtlsAlias, enforceHttps);\n}\nasync function pushedAuthorizationRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'pushed_authorization_request_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, 'POST');\n    }\n    const response = await authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nclass DPoPHandler {\n    #header;\n    #privateKey;\n    #publicKey;\n    #clockSkew;\n    #modifyAssertion;\n    #map;\n    #jkt;\n    constructor(client, keyPair, options) {\n        assertPrivateKey(keyPair?.privateKey, '\"DPoP.privateKey\"');\n        assertPublicKey(keyPair?.publicKey, '\"DPoP.publicKey\"');\n        if (!keyPair.publicKey.extractable) {\n            throw CodedTypeError('\"DPoP.publicKey.extractable\" must be true', ERR_INVALID_ARG_VALUE);\n        }\n        this.#modifyAssertion = options?.[modifyAssertion];\n        this.#clockSkew = getClockSkew(client);\n        this.#privateKey = keyPair.privateKey;\n        this.#publicKey = keyPair.publicKey;\n        branded.add(this);\n    }\n    #get(key) {\n        this.#map ||= new Map();\n        let item = this.#map.get(key);\n        if (item) {\n            this.#map.delete(key);\n            this.#map.set(key, item);\n        }\n        return item;\n    }\n    #set(key, val) {\n        this.#map ||= new Map();\n        this.#map.delete(key);\n        if (this.#map.size === 100) {\n            this.#map.delete(this.#map.keys().next().value);\n        }\n        this.#map.set(key, val);\n    }\n    async calculateThumbprint() {\n        if (!this.#jkt) {\n            const jwk = await crypto.subtle.exportKey('jwk', this.#publicKey);\n            let components;\n            switch (jwk.kty) {\n                case 'EC':\n                    components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x, y: jwk.y };\n                    break;\n                case 'OKP':\n                    components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x };\n                    break;\n                case 'RSA':\n                    components = { e: jwk.e, kty: jwk.kty, n: jwk.n };\n                    break;\n                default:\n                    throw new UnsupportedOperationError('unsupported JWK', { cause: { jwk } });\n            }\n            this.#jkt ||= b64u(await crypto.subtle.digest({ name: 'SHA-256' }, buf(JSON.stringify(components))));\n        }\n        return this.#jkt;\n    }\n    async addProof(url, headers, htm, accessToken) {\n        this.#header ||= {\n            alg: keyToJws(this.#privateKey),\n            typ: 'dpop+jwt',\n            jwk: await publicJwk(this.#publicKey),\n        };\n        const nonce = this.#get(url.origin);\n        const now = epochTime() + this.#clockSkew;\n        const payload = {\n            iat: now,\n            jti: randomBytes(),\n            htm,\n            nonce,\n            htu: `${url.origin}${url.pathname}`,\n            ath: accessToken ? b64u(await crypto.subtle.digest('SHA-256', buf(accessToken))) : undefined,\n        };\n        this.#modifyAssertion?.(this.#header, payload);\n        headers.set('dpop', await signJwt(this.#header, payload, this.#privateKey));\n    }\n    cacheNonce(response) {\n        try {\n            const nonce = response.headers.get('dpop-nonce');\n            if (nonce) {\n                this.#set(new URL(response.url).origin, nonce);\n            }\n        }\n        catch { }\n    }\n}\nfunction isDPoPNonceError(err) {\n    if (err instanceof WWWAuthenticateChallengeError) {\n        const { 0: challenge, length } = err.cause;\n        return (length === 1 && challenge.scheme === 'dpop' && challenge.parameters.error === 'use_dpop_nonce');\n    }\n    if (err instanceof ResponseBodyError) {\n        return err.error === 'use_dpop_nonce';\n    }\n    return false;\n}\nfunction DPoP(client, keyPair, options) {\n    return new DPoPHandler(client, keyPair, options);\n}\nclass ResponseBodyError extends Error {\n    cause;\n    code;\n    error;\n    status;\n    error_description;\n    response;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = RESPONSE_BODY_ERROR;\n        this.cause = options.cause;\n        this.error = options.cause.error;\n        this.status = options.response.status;\n        this.error_description = options.cause.error_description;\n        Object.defineProperty(this, 'response', { enumerable: false, value: options.response });\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass AuthorizationResponseError extends Error {\n    cause;\n    code;\n    error;\n    error_description;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = AUTHORIZATION_RESPONSE_ERROR;\n        this.cause = options.cause;\n        this.error = options.cause.get('error');\n        this.error_description = options.cause.get('error_description') ?? undefined;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass WWWAuthenticateChallengeError extends Error {\n    cause;\n    code;\n    response;\n    status;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = WWW_AUTHENTICATE_CHALLENGE;\n        this.cause = options.cause;\n        this.status = options.response.status;\n        this.response = options.response;\n        Object.defineProperty(this, 'response', { enumerable: false });\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nconst tokenMatch = \"[a-zA-Z0-9!#$%&\\\\'\\\\*\\\\+\\\\-\\\\.\\\\^_`\\\\|~]+\";\nconst token68Match = '[a-zA-Z0-9\\\\-\\\\._\\\\~\\\\+\\\\/]+[=]{0,2}';\nconst quotedMatch = '\"((?:[^\"\\\\\\\\]|\\\\\\\\.)*)\"';\nconst quotedParamMatcher = '(' + tokenMatch + ')\\\\s*=\\\\s*' + quotedMatch;\nconst paramMatcher = '(' + tokenMatch + ')\\\\s*=\\\\s*(' + tokenMatch + ')';\nconst schemeRE = new RegExp('^[,\\\\s]*(' + tokenMatch + ')\\\\s(.*)');\nconst quotedParamRE = new RegExp('^[,\\\\s]*' + quotedParamMatcher + '[,\\\\s]*(.*)');\nconst unquotedParamRE = new RegExp('^[,\\\\s]*' + paramMatcher + '[,\\\\s]*(.*)');\nconst token68ParamRE = new RegExp('^(' + token68Match + ')(?:$|[,\\\\s])(.*)');\nfunction parseWwwAuthenticateChallenges(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    const header = response.headers.get('www-authenticate');\n    if (header === null) {\n        return undefined;\n    }\n    const challenges = [];\n    let rest = header;\n    while (rest) {\n        let match = rest.match(schemeRE);\n        const scheme = match?.['1'].toLowerCase();\n        rest = match?.['2'];\n        if (!scheme) {\n            return undefined;\n        }\n        const parameters = {};\n        let token68;\n        while (rest) {\n            let key;\n            let value;\n            if ((match = rest.match(quotedParamRE))) {\n                ;\n                [, key, value, rest] = match;\n                if (value.includes('\\\\')) {\n                    try {\n                        value = JSON.parse(`\"${value}\"`);\n                    }\n                    catch { }\n                }\n                parameters[key.toLowerCase()] = value;\n                continue;\n            }\n            if ((match = rest.match(unquotedParamRE))) {\n                ;\n                [, key, value, rest] = match;\n                parameters[key.toLowerCase()] = value;\n                continue;\n            }\n            if ((match = rest.match(token68ParamRE))) {\n                if (Object.keys(parameters).length) {\n                    break;\n                }\n                ;\n                [, token68, rest] = match;\n                break;\n            }\n            return undefined;\n        }\n        const challenge = { scheme, parameters };\n        if (token68) {\n            challenge.token68 = token68;\n        }\n        challenges.push(challenge);\n    }\n    if (!challenges.length) {\n        return undefined;\n    }\n    return challenges;\n}\nasync function processPushedAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 201, 'Pushed Authorization Request Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.request_uri, '\"response\" body \"request_uri\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n    assertNumber(expiresIn, true, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.expires_in = expiresIn;\n    return json;\n}\nasync function parseOAuthResponseErrorBody(response) {\n    if (response.status > 399 && response.status < 500) {\n        assertReadableResponse(response);\n        assertApplicationJson(response);\n        try {\n            const json = await response.clone().json();\n            if (isJsonObject(json) && typeof json.error === 'string' && json.error.length) {\n                return json;\n            }\n        }\n        catch { }\n    }\n    return undefined;\n}\nasync function checkOAuthBodyError(response, expected, label) {\n    if (response.status !== expected) {\n        let err;\n        if ((err = await parseOAuthResponseErrorBody(response))) {\n            await response.body?.cancel();\n            throw new ResponseBodyError('server responded with an error in the response body', {\n                cause: err,\n                response,\n            });\n        }\n        throw OPE(`\"response\" is not a conform ${label} response (unexpected HTTP status code)`, RESPONSE_IS_NOT_CONFORM, response);\n    }\n}\nfunction assertDPoP(option) {\n    if (!branded.has(option)) {\n        throw CodedTypeError('\"options.DPoP\" is not a valid DPoPHandle', ERR_INVALID_ARG_VALUE);\n    }\n}\nasync function resourceRequest(accessToken, method, url, headers, body, options) {\n    assertString(accessToken, '\"accessToken\"');\n    if (!(url instanceof URL)) {\n        throw CodedTypeError('\"url\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    checkProtocol(url, options?.[allowInsecureRequests] !== true);\n    headers = prepareHeaders(headers);\n    if (options?.DPoP) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, method.toUpperCase(), accessToken);\n    }\n    headers.set('authorization', `${headers.has('dpop') ? 'DPoP' : 'Bearer'} ${accessToken}`);\n    const response = await (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: signal(url, options?.signal),\n    });\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nasync function protectedResourceRequest(accessToken, method, url, headers, body, options) {\n    const response = await resourceRequest(accessToken, method, url, headers, body, options);\n    checkAuthenticationChallenges(response);\n    return response;\n}\nasync function userInfoRequest(as, client, accessToken, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'userinfo_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const headers = prepareHeaders(options?.headers);\n    if (client.userinfo_signed_response_alg) {\n        headers.set('accept', 'application/jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n        headers.append('accept', 'application/jwt');\n    }\n    return resourceRequest(accessToken, 'GET', url, headers, null, {\n        ...options,\n        [clockSkew]: getClockSkew(client),\n    });\n}\nlet jwksMap;\nfunction setJwksCache(as, jwks, uat, cache) {\n    jwksMap ||= new WeakMap();\n    jwksMap.set(as, {\n        jwks,\n        uat,\n        get age() {\n            return epochTime() - this.uat;\n        },\n    });\n    if (cache) {\n        Object.assign(cache, { jwks: structuredClone(jwks), uat });\n    }\n}\nfunction isFreshJwksCache(input) {\n    if (typeof input !== 'object' || input === null) {\n        return false;\n    }\n    if (!('uat' in input) || typeof input.uat !== 'number' || epochTime() - input.uat >= 300) {\n        return false;\n    }\n    if (!('jwks' in input) ||\n        !isJsonObject(input.jwks) ||\n        !Array.isArray(input.jwks.keys) ||\n        !Array.prototype.every.call(input.jwks.keys, isJsonObject)) {\n        return false;\n    }\n    return true;\n}\nfunction clearJwksCache(as, cache) {\n    jwksMap?.delete(as);\n    delete cache?.jwks;\n    delete cache?.uat;\n}\nasync function getPublicSigKeyFromIssuerJwksUri(as, options, header) {\n    const { alg, kid } = header;\n    checkSupportedJwsAlg(header);\n    if (!jwksMap?.has(as) && isFreshJwksCache(options?.[jwksCache])) {\n        setJwksCache(as, options?.[jwksCache].jwks, options?.[jwksCache].uat);\n    }\n    let jwks;\n    let age;\n    if (jwksMap?.has(as)) {\n        ;\n        ({ jwks, age } = jwksMap.get(as));\n        if (age >= 300) {\n            clearJwksCache(as, options?.[jwksCache]);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n    }\n    else {\n        jwks = await jwksRequest(as, options).then(processJwksResponse);\n        age = 0;\n        setJwksCache(as, jwks, epochTime(), options?.[jwksCache]);\n    }\n    let kty;\n    switch (alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            kty = 'RSA';\n            break;\n        case 'ES':\n            kty = 'EC';\n            break;\n        case 'Ed':\n            kty = 'OKP';\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg } });\n    }\n    const candidates = jwks.keys.filter((jwk) => {\n        if (jwk.kty !== kty) {\n            return false;\n        }\n        if (kid !== undefined && kid !== jwk.kid) {\n            return false;\n        }\n        if (jwk.alg !== undefined && alg !== jwk.alg) {\n            return false;\n        }\n        if (jwk.use !== undefined && jwk.use !== 'sig') {\n            return false;\n        }\n        if (jwk.key_ops?.includes('verify') === false) {\n            return false;\n        }\n        switch (true) {\n            case alg === 'ES256' && jwk.crv !== 'P-256':\n            case alg === 'ES384' && jwk.crv !== 'P-384':\n            case alg === 'ES512' && jwk.crv !== 'P-521':\n            case alg === 'Ed25519' && jwk.crv !== 'Ed25519':\n            case alg === 'EdDSA' && jwk.crv !== 'Ed25519':\n                return false;\n        }\n        return true;\n    });\n    const { 0: jwk, length } = candidates;\n    if (!length) {\n        if (age >= 60) {\n            clearJwksCache(as, options?.[jwksCache]);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n        throw OPE('error when selecting a JWT verification key, no applicable keys found', KEY_SELECTION, { header, candidates, jwks_uri: new URL(as.jwks_uri) });\n    }\n    if (length !== 1) {\n        throw OPE('error when selecting a JWT verification key, multiple applicable keys found, a \"kid\" JWT Header Parameter is required', KEY_SELECTION, { header, candidates, jwks_uri: new URL(as.jwks_uri) });\n    }\n    return importJwk(alg, jwk);\n}\nconst skipSubjectCheck = Symbol();\nfunction getContentType(input) {\n    return input.headers.get('content-type')?.split(';')[0];\n}\nasync function processUserInfoResponse(as, client, expectedSubject, response, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform UserInfo Endpoint response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    let json;\n    if (getContentType(response) === 'application/jwt') {\n        const { claims, jwt } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.userinfo_signed_response_alg, as.userinfo_signing_alg_values_supported, undefined), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(validateOptionalAudience.bind(undefined, client.client_id))\n            .then(validateOptionalIssuer.bind(undefined, as));\n        jwtRefs.set(response, jwt);\n        json = claims;\n    }\n    else {\n        if (client.userinfo_signed_response_alg) {\n            throw OPE('JWT UserInfo Response expected', JWT_USERINFO_EXPECTED, response);\n        }\n        json = await getResponseJsonBody(response);\n    }\n    assertString(json.sub, '\"response\" body \"sub\" property', INVALID_RESPONSE, { body: json });\n    switch (expectedSubject) {\n        case skipSubjectCheck:\n            break;\n        default:\n            assertString(expectedSubject, '\"expectedSubject\"');\n            if (json.sub !== expectedSubject) {\n                throw OPE('unexpected \"response\" body \"sub\" property value', JSON_ATTRIBUTE_COMPARISON, {\n                    expected: expectedSubject,\n                    body: json,\n                    attribute: 'sub',\n                });\n            }\n    }\n    return json;\n}\nasync function authenticatedRequest(as, client, clientAuthentication, url, body, headers, options) {\n    await clientAuthentication(as, client, body, headers);\n    headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'POST',\n        redirect: 'manual',\n        signal: signal(url, options?.signal),\n    });\n}\nasync function tokenEndpointRequest(as, client, clientAuthentication, grantType, parameters, options) {\n    const url = resolveEndpoint(as, 'token_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    parameters.set('grant_type', grantType);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, 'POST');\n    }\n    const response = await authenticatedRequest(as, client, clientAuthentication, url, parameters, headers, options);\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nasync function refreshTokenGrantRequest(as, client, clientAuthentication, refreshToken, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(refreshToken, '\"refreshToken\"');\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('refresh_token', refreshToken);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'refresh_token', parameters, options);\n}\nconst idTokenClaims = new WeakMap();\nconst jwtRefs = new WeakMap();\nfunction getValidatedIdTokenClaims(ref) {\n    if (!ref.id_token) {\n        return undefined;\n    }\n    const claims = idTokenClaims.get(ref);\n    if (!claims) {\n        throw CodedTypeError('\"ref\" was already garbage collected or did not resolve from the proper sources', ERR_INVALID_ARG_VALUE);\n    }\n    return claims;\n}\nasync function validateApplicationLevelSignature(as, ref, options) {\n    assertAs(as);\n    if (!jwtRefs.has(ref)) {\n        throw CodedTypeError('\"ref\" does not contain a processed JWT Response to verify the signature of', ERR_INVALID_ARG_VALUE);\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwtRefs.get(ref).split('.');\n    const header = JSON.parse(buf(b64u(protectedHeader)));\n    if (header.alg.startsWith('HS')) {\n        throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg: header.alg } });\n    }\n    let key;\n    key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, b64u(encodedSignature));\n}\nasync function processGenericAccessTokenResponse(as, client, response, additionalRequiredIdTokenClaims, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Token Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.access_token, '\"response\" body \"access_token\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.token_type, '\"response\" body \"token_type\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.token_type = json.token_type.toLowerCase();\n    if (json.token_type !== 'dpop' && json.token_type !== 'bearer') {\n        throw new UnsupportedOperationError('unsupported `token_type` value', { cause: { body: json } });\n    }\n    if (json.expires_in !== undefined) {\n        let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n        assertNumber(expiresIn, true, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n        json.expires_in = expiresIn;\n    }\n    if (json.refresh_token !== undefined) {\n        assertString(json.refresh_token, '\"response\" body \"refresh_token\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    if (json.scope !== undefined && typeof json.scope !== 'string') {\n        throw OPE('\"response\" body \"scope\" property must be a string', INVALID_RESPONSE, { body: json });\n    }\n    if (json.id_token !== undefined) {\n        assertString(json.id_token, '\"response\" body \"id_token\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n        const requiredClaims = ['aud', 'exp', 'iat', 'iss', 'sub'];\n        if (client.require_auth_time === true) {\n            requiredClaims.push('auth_time');\n        }\n        if (client.default_max_age !== undefined) {\n            assertNumber(client.default_max_age, true, '\"client.default_max_age\"');\n            requiredClaims.push('auth_time');\n        }\n        if (additionalRequiredIdTokenClaims?.length) {\n            requiredClaims.push(...additionalRequiredIdTokenClaims);\n        }\n        const { claims, jwt } = await validateJwt(json.id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(validatePresence.bind(undefined, requiredClaims))\n            .then(validateIssuer.bind(undefined, as))\n            .then(validateAudience.bind(undefined, client.client_id));\n        if (Array.isArray(claims.aud) && claims.aud.length !== 1) {\n            if (claims.azp === undefined) {\n                throw OPE('ID Token \"aud\" (audience) claim includes additional untrusted audiences', JWT_CLAIM_COMPARISON, { claims, claim: 'aud' });\n            }\n            if (claims.azp !== client.client_id) {\n                throw OPE('unexpected ID Token \"azp\" (authorized party) claim value', JWT_CLAIM_COMPARISON, { expected: client.client_id, claims, claim: 'azp' });\n            }\n        }\n        if (claims.auth_time !== undefined) {\n            assertNumber(claims.auth_time, true, 'ID Token \"auth_time\" (authentication time)', INVALID_RESPONSE, { claims });\n        }\n        jwtRefs.set(response, jwt);\n        idTokenClaims.set(json, claims);\n    }\n    return json;\n}\nfunction checkAuthenticationChallenges(response) {\n    let challenges;\n    if ((challenges = parseWwwAuthenticateChallenges(response))) {\n        throw new WWWAuthenticateChallengeError('server responded with a challenge in the WWW-Authenticate HTTP Header', { cause: challenges, response });\n    }\n}\nasync function processRefreshTokenResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nfunction validateOptionalAudience(expected, result) {\n    if (result.claims.aud !== undefined) {\n        return validateAudience(expected, result);\n    }\n    return result;\n}\nfunction validateAudience(expected, result) {\n    if (Array.isArray(result.claims.aud)) {\n        if (!result.claims.aud.includes(expected)) {\n            throw OPE('unexpected JWT \"aud\" (audience) claim value', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: result.claims,\n                claim: 'aud',\n            });\n        }\n    }\n    else if (result.claims.aud !== expected) {\n        throw OPE('unexpected JWT \"aud\" (audience) claim value', JWT_CLAIM_COMPARISON, {\n            expected,\n            claims: result.claims,\n            claim: 'aud',\n        });\n    }\n    return result;\n}\nfunction validateOptionalIssuer(as, result) {\n    if (result.claims.iss !== undefined) {\n        return validateIssuer(as, result);\n    }\n    return result;\n}\nfunction validateIssuer(as, result) {\n    const expected = as[_expectedIssuer]?.(result) ?? as.issuer;\n    if (result.claims.iss !== expected) {\n        throw OPE('unexpected JWT \"iss\" (issuer) claim value', JWT_CLAIM_COMPARISON, {\n            expected,\n            claims: result.claims,\n            claim: 'iss',\n        });\n    }\n    return result;\n}\nconst branded = new WeakSet();\nfunction brand(searchParams) {\n    branded.add(searchParams);\n    return searchParams;\n}\nconst nopkce = Symbol();\nasync function authorizationCodeGrantRequest(as, client, clientAuthentication, callbackParameters, redirectUri, codeVerifier, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!branded.has(callbackParameters)) {\n        throw CodedTypeError('\"callbackParameters\" must be an instance of URLSearchParams obtained from \"validateAuthResponse()\", or \"validateJwtAuthResponse()', ERR_INVALID_ARG_VALUE);\n    }\n    assertString(redirectUri, '\"redirectUri\"');\n    const code = getURLSearchParameter(callbackParameters, 'code');\n    if (!code) {\n        throw OPE('no authorization code in \"callbackParameters\"', INVALID_RESPONSE);\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('redirect_uri', redirectUri);\n    parameters.set('code', code);\n    if (codeVerifier !== nopkce) {\n        assertString(codeVerifier, '\"codeVerifier\"');\n        parameters.set('code_verifier', codeVerifier);\n    }\n    return tokenEndpointRequest(as, client, clientAuthentication, 'authorization_code', parameters, options);\n}\nconst jwtClaimNames = {\n    aud: 'audience',\n    c_hash: 'code hash',\n    client_id: 'client id',\n    exp: 'expiration time',\n    iat: 'issued at',\n    iss: 'issuer',\n    jti: 'jwt id',\n    nonce: 'nonce',\n    s_hash: 'state hash',\n    sub: 'subject',\n    ath: 'access token hash',\n    htm: 'http method',\n    htu: 'http uri',\n    cnf: 'confirmation',\n    auth_time: 'authentication time',\n};\nfunction validatePresence(required, result) {\n    for (const claim of required) {\n        if (result.claims[claim] === undefined) {\n            throw OPE(`JWT \"${claim}\" (${jwtClaimNames[claim]}) claim missing`, INVALID_RESPONSE, {\n                claims: result.claims,\n            });\n        }\n    }\n    return result;\n}\nconst expectNoNonce = Symbol();\nconst skipAuthTimeCheck = Symbol();\nasync function processAuthorizationCodeResponse(as, client, response, options) {\n    if (typeof options?.expectedNonce === 'string' ||\n        typeof options?.maxAge === 'number' ||\n        options?.requireIdToken) {\n        return processAuthorizationCodeOpenIDResponse(as, client, response, options.expectedNonce, options.maxAge, {\n            [jweDecrypt]: options[jweDecrypt],\n        });\n    }\n    return processAuthorizationCodeOAuth2Response(as, client, response, options);\n}\nasync function processAuthorizationCodeOpenIDResponse(as, client, response, expectedNonce, maxAge, options) {\n    const additionalRequiredClaims = [];\n    switch (expectedNonce) {\n        case undefined:\n            expectedNonce = expectNoNonce;\n            break;\n        case expectNoNonce:\n            break;\n        default:\n            assertString(expectedNonce, '\"expectedNonce\" argument');\n            additionalRequiredClaims.push('nonce');\n    }\n    maxAge ??= client.default_max_age;\n    switch (maxAge) {\n        case undefined:\n            maxAge = skipAuthTimeCheck;\n            break;\n        case skipAuthTimeCheck:\n            break;\n        default:\n            assertNumber(maxAge, true, '\"maxAge\" argument');\n            additionalRequiredClaims.push('auth_time');\n    }\n    const result = await processGenericAccessTokenResponse(as, client, response, additionalRequiredClaims, options);\n    assertString(result.id_token, '\"response\" body \"id_token\" property', INVALID_RESPONSE, {\n        body: result,\n    });\n    const claims = getValidatedIdTokenClaims(result);\n    if (maxAge !== skipAuthTimeCheck) {\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n        }\n    }\n    if (expectedNonce === expectNoNonce) {\n        if (claims.nonce !== undefined) {\n            throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n                expected: undefined,\n                claims,\n                claim: 'nonce',\n            });\n        }\n    }\n    else if (claims.nonce !== expectedNonce) {\n        throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n            expected: expectedNonce,\n            claims,\n            claim: 'nonce',\n        });\n    }\n    return result;\n}\nasync function processAuthorizationCodeOAuth2Response(as, client, response, options) {\n    const result = await processGenericAccessTokenResponse(as, client, response, undefined, options);\n    const claims = getValidatedIdTokenClaims(result);\n    if (claims) {\n        if (client.default_max_age !== undefined) {\n            assertNumber(client.default_max_age, true, '\"client.default_max_age\"');\n            const now = epochTime() + getClockSkew(client);\n            const tolerance = getClockTolerance(client);\n            if (claims.auth_time + client.default_max_age < now - tolerance) {\n                throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n            }\n        }\n        if (claims.nonce !== undefined) {\n            throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n                expected: undefined,\n                claims,\n                claim: 'nonce',\n            });\n        }\n    }\n    return result;\n}\nconst WWW_AUTHENTICATE_CHALLENGE = 'OAUTH_WWW_AUTHENTICATE_CHALLENGE';\nconst RESPONSE_BODY_ERROR = 'OAUTH_RESPONSE_BODY_ERROR';\nconst UNSUPPORTED_OPERATION = 'OAUTH_UNSUPPORTED_OPERATION';\nconst AUTHORIZATION_RESPONSE_ERROR = 'OAUTH_AUTHORIZATION_RESPONSE_ERROR';\nconst JWT_USERINFO_EXPECTED = 'OAUTH_JWT_USERINFO_EXPECTED';\nconst PARSE_ERROR = 'OAUTH_PARSE_ERROR';\nconst INVALID_RESPONSE = 'OAUTH_INVALID_RESPONSE';\nconst INVALID_REQUEST = 'OAUTH_INVALID_REQUEST';\nconst RESPONSE_IS_NOT_JSON = 'OAUTH_RESPONSE_IS_NOT_JSON';\nconst RESPONSE_IS_NOT_CONFORM = 'OAUTH_RESPONSE_IS_NOT_CONFORM';\nconst HTTP_REQUEST_FORBIDDEN = 'OAUTH_HTTP_REQUEST_FORBIDDEN';\nconst REQUEST_PROTOCOL_FORBIDDEN = 'OAUTH_REQUEST_PROTOCOL_FORBIDDEN';\nconst JWT_TIMESTAMP_CHECK = 'OAUTH_JWT_TIMESTAMP_CHECK_FAILED';\nconst JWT_CLAIM_COMPARISON = 'OAUTH_JWT_CLAIM_COMPARISON_FAILED';\nconst JSON_ATTRIBUTE_COMPARISON = 'OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED';\nconst KEY_SELECTION = 'OAUTH_KEY_SELECTION_FAILED';\nconst MISSING_SERVER_METADATA = 'OAUTH_MISSING_SERVER_METADATA';\nconst INVALID_SERVER_METADATA = 'OAUTH_INVALID_SERVER_METADATA';\nfunction checkJwtType(expected, result) {\n    if (typeof result.header.typ !== 'string' || normalizeTyp(result.header.typ) !== expected) {\n        throw OPE('unexpected JWT \"typ\" header parameter value', INVALID_RESPONSE, {\n            header: result.header,\n        });\n    }\n    return result;\n}\nasync function clientCredentialsGrantRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'client_credentials', new URLSearchParams(parameters), options);\n}\nasync function genericTokenEndpointRequest(as, client, clientAuthentication, grantType, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(grantType, '\"grantType\"');\n    return tokenEndpointRequest(as, client, clientAuthentication, grantType, new URLSearchParams(parameters), options);\n}\nasync function processGenericTokenEndpointResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function processClientCredentialsResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function revocationRequest(as, client, clientAuthentication, token, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(token, '\"token\"');\n    const url = resolveEndpoint(as, 'revocation_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    headers.delete('accept');\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processRevocationResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Revocation Endpoint');\n    return undefined;\n}\nfunction assertReadableResponse(response) {\n    if (response.bodyUsed) {\n        throw CodedTypeError('\"response\" body has been used already', ERR_INVALID_ARG_VALUE);\n    }\n}\nasync function introspectionRequest(as, client, clientAuthentication, token, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(token, '\"token\"');\n    const url = resolveEndpoint(as, 'introspection_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    if (options?.requestJwtResponse ?? client.introspection_signed_response_alg) {\n        headers.set('accept', 'application/token-introspection+jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n    }\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processIntrospectionResponse(as, client, response, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Introspection Endpoint');\n    let json;\n    if (getContentType(response) === 'application/token-introspection+jwt') {\n        assertReadableResponse(response);\n        const { claims, jwt } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.introspection_signed_response_alg, as.introspection_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(checkJwtType.bind(undefined, 'token-introspection+jwt'))\n            .then(validatePresence.bind(undefined, ['aud', 'iat', 'iss']))\n            .then(validateIssuer.bind(undefined, as))\n            .then(validateAudience.bind(undefined, client.client_id));\n        jwtRefs.set(response, jwt);\n        if (!isJsonObject(claims.token_introspection)) {\n            throw OPE('JWT \"token_introspection\" claim must be a JSON object', INVALID_RESPONSE, {\n                claims,\n            });\n        }\n        json = claims.token_introspection;\n    }\n    else {\n        assertReadableResponse(response);\n        json = await getResponseJsonBody(response);\n    }\n    if (typeof json.active !== 'boolean') {\n        throw OPE('\"response\" body \"active\" property must be a boolean', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function jwksRequest(as, options) {\n    assertAs(as);\n    const url = resolveEndpoint(as, 'jwks_uri', false, options?.[allowInsecureRequests] !== true);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    headers.append('accept', 'application/jwk-set+json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body: undefined,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: signal(url, options?.signal),\n    });\n}\nasync function processJwksResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform JSON Web Key Set response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response, (response) => assertContentTypes(response, 'application/json', 'application/jwk-set+json'));\n    if (!Array.isArray(json.keys)) {\n        throw OPE('\"response\" body \"keys\" property must be an array', INVALID_RESPONSE, { body: json });\n    }\n    if (!Array.prototype.every.call(json.keys, isJsonObject)) {\n        throw OPE('\"response\" body \"keys\" property members must be JWK formatted objects', INVALID_RESPONSE, { body: json });\n    }\n    return json;\n}\nfunction supported(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'ES256':\n        case 'RS256':\n        case 'PS384':\n        case 'ES384':\n        case 'RS384':\n        case 'PS512':\n        case 'ES512':\n        case 'RS512':\n        case 'Ed25519':\n        case 'EdDSA':\n            return true;\n        default:\n            return false;\n    }\n}\nfunction checkSupportedJwsAlg(header) {\n    if (!supported(header.alg)) {\n        throw new UnsupportedOperationError('unsupported JWS \"alg\" identifier', {\n            cause: { alg: header.alg },\n        });\n    }\n}\nfunction checkRsaKeyAlgorithm(key) {\n    const { algorithm } = key;\n    if (typeof algorithm.modulusLength !== 'number' || algorithm.modulusLength < 2048) {\n        throw new UnsupportedOperationError(`unsupported ${algorithm.name} modulusLength`, {\n            cause: key,\n        });\n    }\n}\nfunction ecdsaHashName(key) {\n    const { algorithm } = key;\n    switch (algorithm.namedCurve) {\n        case 'P-256':\n            return 'SHA-256';\n        case 'P-384':\n            return 'SHA-384';\n        case 'P-521':\n            return 'SHA-512';\n        default:\n            throw new UnsupportedOperationError('unsupported ECDSA namedCurve', { cause: key });\n    }\n}\nfunction keyToSubtle(key) {\n    switch (key.algorithm.name) {\n        case 'ECDSA':\n            return {\n                name: key.algorithm.name,\n                hash: ecdsaHashName(key),\n            };\n        case 'RSA-PSS': {\n            checkRsaKeyAlgorithm(key);\n            switch (key.algorithm.hash.name) {\n                case 'SHA-256':\n                case 'SHA-384':\n                case 'SHA-512':\n                    return {\n                        name: key.algorithm.name,\n                        saltLength: parseInt(key.algorithm.hash.name.slice(-3), 10) >> 3,\n                    };\n                default:\n                    throw new UnsupportedOperationError('unsupported RSA-PSS hash name', { cause: key });\n            }\n        }\n        case 'RSASSA-PKCS1-v1_5':\n            checkRsaKeyAlgorithm(key);\n            return key.algorithm.name;\n        case 'Ed25519':\n            return key.algorithm.name;\n    }\n    throw new UnsupportedOperationError('unsupported CryptoKey algorithm name', { cause: key });\n}\nasync function validateJwsSignature(protectedHeader, payload, key, signature) {\n    const data = buf(`${protectedHeader}.${payload}`);\n    const algorithm = keyToSubtle(key);\n    const verified = await crypto.subtle.verify(algorithm, key, signature, data);\n    if (!verified) {\n        throw OPE('JWT signature verification failed', INVALID_RESPONSE, {\n            key,\n            data,\n            signature,\n            algorithm,\n        });\n    }\n}\nasync function validateJwt(jws, checkAlg, clockSkew, clockTolerance, decryptJwt) {\n    let { 0: protectedHeader, 1: payload, length } = jws.split('.');\n    if (length === 5) {\n        if (decryptJwt !== undefined) {\n            jws = await decryptJwt(jws);\n            ({ 0: protectedHeader, 1: payload, length } = jws.split('.'));\n        }\n        else {\n            throw new UnsupportedOperationError('JWE decryption is not configured', { cause: jws });\n        }\n    }\n    if (length !== 3) {\n        throw OPE('Invalid JWT', INVALID_RESPONSE, jws);\n    }\n    let header;\n    try {\n        header = JSON.parse(buf(b64u(protectedHeader)));\n    }\n    catch (cause) {\n        throw OPE('failed to parse JWT Header body as base64url encoded JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(header)) {\n        throw OPE('JWT Header must be a top level object', INVALID_RESPONSE, jws);\n    }\n    checkAlg(header);\n    if (header.crit !== undefined) {\n        throw new UnsupportedOperationError('no JWT \"crit\" header parameter extensions are supported', {\n            cause: { header },\n        });\n    }\n    let claims;\n    try {\n        claims = JSON.parse(buf(b64u(payload)));\n    }\n    catch (cause) {\n        throw OPE('failed to parse JWT Payload body as base64url encoded JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(claims)) {\n        throw OPE('JWT Payload must be a top level object', INVALID_RESPONSE, jws);\n    }\n    const now = epochTime() + clockSkew;\n    if (claims.exp !== undefined) {\n        if (typeof claims.exp !== 'number') {\n            throw OPE('unexpected JWT \"exp\" (expiration time) claim type', INVALID_RESPONSE, { claims });\n        }\n        if (claims.exp <= now - clockTolerance) {\n            throw OPE('unexpected JWT \"exp\" (expiration time) claim value, expiration is past current timestamp', JWT_TIMESTAMP_CHECK, { claims, now, tolerance: clockTolerance, claim: 'exp' });\n        }\n    }\n    if (claims.iat !== undefined) {\n        if (typeof claims.iat !== 'number') {\n            throw OPE('unexpected JWT \"iat\" (issued at) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    if (claims.iss !== undefined) {\n        if (typeof claims.iss !== 'string') {\n            throw OPE('unexpected JWT \"iss\" (issuer) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    if (claims.nbf !== undefined) {\n        if (typeof claims.nbf !== 'number') {\n            throw OPE('unexpected JWT \"nbf\" (not before) claim type', INVALID_RESPONSE, { claims });\n        }\n        if (claims.nbf > now + clockTolerance) {\n            throw OPE('unexpected JWT \"nbf\" (not before) claim value', JWT_TIMESTAMP_CHECK, {\n                claims,\n                now,\n                tolerance: clockTolerance,\n                claim: 'nbf',\n            });\n        }\n    }\n    if (claims.aud !== undefined) {\n        if (typeof claims.aud !== 'string' && !Array.isArray(claims.aud)) {\n            throw OPE('unexpected JWT \"aud\" (audience) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    return { header, claims, jwt: jws };\n}\nasync function validateJwtAuthResponse(as, client, parameters, expectedState, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, or URL', ERR_INVALID_ARG_TYPE);\n    }\n    const response = getURLSearchParameter(parameters, 'response');\n    if (!response) {\n        throw OPE('\"parameters\" does not contain a JARM response', INVALID_RESPONSE);\n    }\n    const { claims, header, jwt } = await validateJwt(response, checkSigningAlgorithm.bind(undefined, client.authorization_signed_response_alg, as.authorization_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n        .then(validatePresence.bind(undefined, ['aud', 'exp', 'iss']))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwt.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    const result = new URLSearchParams();\n    for (const [key, value] of Object.entries(claims)) {\n        if (typeof value === 'string' && key !== 'aud') {\n            result.set(key, value);\n        }\n    }\n    return validateAuthResponse(as, client, result, expectedState);\n}\nasync function idTokenHash(data, header, claimName) {\n    let algorithm;\n    switch (header.alg) {\n        case 'RS256':\n        case 'PS256':\n        case 'ES256':\n            algorithm = 'SHA-256';\n            break;\n        case 'RS384':\n        case 'PS384':\n        case 'ES384':\n            algorithm = 'SHA-384';\n            break;\n        case 'RS512':\n        case 'PS512':\n        case 'ES512':\n        case 'Ed25519':\n        case 'EdDSA':\n            algorithm = 'SHA-512';\n            break;\n        default:\n            throw new UnsupportedOperationError(`unsupported JWS algorithm for ${claimName} calculation`, { cause: { alg: header.alg } });\n    }\n    const digest = await crypto.subtle.digest(algorithm, buf(data));\n    return b64u(digest.slice(0, digest.byteLength / 2));\n}\nasync function idTokenHashMatches(data, actual, header, claimName) {\n    const expected = await idTokenHash(data, header, claimName);\n    return actual === expected;\n}\nasync function validateDetachedSignatureResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    return validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, true);\n}\nasync function validateCodeIdTokenResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    return validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, false);\n}\nasync function consumeStream(request) {\n    if (request.bodyUsed) {\n        throw CodedTypeError('form_post Request instances must contain a readable body', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    return request.text();\n}\nasync function formPostResponse(request) {\n    if (request.method !== 'POST') {\n        throw CodedTypeError('form_post responses are expected to use the POST method', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    if (getContentType(request) !== 'application/x-www-form-urlencoded') {\n        throw CodedTypeError('form_post responses are expected to use the application/x-www-form-urlencoded content-type', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    return consumeStream(request);\n}\nasync function validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, fapi) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        if (!parameters.hash.length) {\n            throw CodedTypeError('\"parameters\" as an instance of URL must contain a hash (fragment) with the Authorization Response parameters', ERR_INVALID_ARG_VALUE);\n        }\n        parameters = new URLSearchParams(parameters.hash.slice(1));\n    }\n    else if (looseInstanceOf(parameters, Request)) {\n        parameters = new URLSearchParams(await formPostResponse(parameters));\n    }\n    else if (parameters instanceof URLSearchParams) {\n        parameters = new URLSearchParams(parameters);\n    }\n    else {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, URL, or Response', ERR_INVALID_ARG_TYPE);\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    parameters.delete('id_token');\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            break;\n        default:\n            assertString(expectedState, '\"expectedState\" argument');\n    }\n    const result = validateAuthResponse({\n        ...as,\n        authorization_response_iss_parameter_supported: false,\n    }, client, parameters, expectedState);\n    if (!id_token) {\n        throw OPE('\"parameters\" does not contain an ID Token', INVALID_RESPONSE);\n    }\n    const code = getURLSearchParameter(parameters, 'code');\n    if (!code) {\n        throw OPE('\"parameters\" does not contain an Authorization Code', INVALID_RESPONSE);\n    }\n    const requiredClaims = [\n        'aud',\n        'exp',\n        'iat',\n        'iss',\n        'sub',\n        'nonce',\n        'c_hash',\n    ];\n    const state = parameters.get('state');\n    if (fapi && (typeof expectedState === 'string' || state !== null)) {\n        requiredClaims.push('s_hash');\n    }\n    if (maxAge !== undefined) {\n        assertNumber(maxAge, true, '\"maxAge\" argument');\n    }\n    else if (client.default_max_age !== undefined) {\n        assertNumber(client.default_max_age, true, '\"client.default_max_age\"');\n    }\n    maxAge ??= client.default_max_age ?? skipAuthTimeCheck;\n    if (client.require_auth_time || maxAge !== skipAuthTimeCheck) {\n        requiredClaims.push('auth_time');\n    }\n    const { claims, header, jwt } = await validateJwt(id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const clockSkew = getClockSkew(client);\n    const now = epochTime() + clockSkew;\n    if (claims.iat < now - 3600) {\n        throw OPE('unexpected JWT \"iat\" (issued at) claim value, it is too far in the past', JWT_TIMESTAMP_CHECK, { now, claims, claim: 'iat' });\n    }\n    assertString(claims.c_hash, 'ID Token \"c_hash\" (code hash) claim value', INVALID_RESPONSE, {\n        claims,\n    });\n    if (claims.auth_time !== undefined) {\n        assertNumber(claims.auth_time, true, 'ID Token \"auth_time\" (authentication time)', INVALID_RESPONSE, { claims });\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n        }\n    }\n    assertString(expectedNonce, '\"expectedNonce\" argument');\n    if (claims.nonce !== expectedNonce) {\n        throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n            expected: expectedNonce,\n            claims,\n            claim: 'nonce',\n        });\n    }\n    if (Array.isArray(claims.aud) && claims.aud.length !== 1) {\n        if (claims.azp === undefined) {\n            throw OPE('ID Token \"aud\" (audience) claim includes additional untrusted audiences', JWT_CLAIM_COMPARISON, { claims, claim: 'aud' });\n        }\n        if (claims.azp !== client.client_id) {\n            throw OPE('unexpected ID Token \"azp\" (authorized party) claim value', JWT_CLAIM_COMPARISON, {\n                expected: client.client_id,\n                claims,\n                claim: 'azp',\n            });\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwt.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    if ((await idTokenHashMatches(code, claims.c_hash, header, 'c_hash')) !== true) {\n        throw OPE('invalid ID Token \"c_hash\" (code hash) claim value', JWT_CLAIM_COMPARISON, {\n            code,\n            alg: header.alg,\n            claim: 'c_hash',\n            claims,\n        });\n    }\n    if ((fapi && state !== null) || claims.s_hash !== undefined) {\n        assertString(claims.s_hash, 'ID Token \"s_hash\" (state hash) claim value', INVALID_RESPONSE, {\n            claims,\n        });\n        assertString(state, '\"state\" response parameter', INVALID_RESPONSE, { parameters });\n        if ((await idTokenHashMatches(state, claims.s_hash, header, 's_hash')) !== true) {\n            throw OPE('invalid ID Token \"s_hash\" (state hash) claim value', JWT_CLAIM_COMPARISON, {\n                state,\n                alg: header.alg,\n                claim: 's_hash',\n                claims,\n            });\n        }\n    }\n    return result;\n}\nfunction checkSigningAlgorithm(client, issuer, fallback, header) {\n    if (client !== undefined) {\n        if (typeof client === 'string' ? header.alg !== client : !client.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: client,\n                reason: 'client configuration',\n            });\n        }\n        return;\n    }\n    if (Array.isArray(issuer)) {\n        if (!issuer.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: issuer,\n                reason: 'authorization server metadata',\n            });\n        }\n        return;\n    }\n    if (fallback !== undefined) {\n        if (typeof fallback === 'string'\n            ? header.alg !== fallback\n            : typeof fallback === 'function'\n                ? !fallback(header.alg)\n                : !fallback.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: fallback,\n                reason: 'default value',\n            });\n        }\n        return;\n    }\n    throw OPE('missing client or server configuration to verify used JWT \"alg\" header parameter', undefined, { client, issuer, fallback });\n}\nfunction getURLSearchParameter(parameters, name) {\n    const { 0: value, length } = parameters.getAll(name);\n    if (length > 1) {\n        throw OPE(`\"${name}\" parameter must be provided only once`, INVALID_RESPONSE);\n    }\n    return value;\n}\nconst skipStateCheck = Symbol();\nconst expectNoState = Symbol();\nfunction validateAuthResponse(as, client, parameters, expectedState) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, or URL', ERR_INVALID_ARG_TYPE);\n    }\n    if (getURLSearchParameter(parameters, 'response')) {\n        throw OPE('\"parameters\" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()', INVALID_RESPONSE, { parameters });\n    }\n    const iss = getURLSearchParameter(parameters, 'iss');\n    const state = getURLSearchParameter(parameters, 'state');\n    if (!iss && as.authorization_response_iss_parameter_supported) {\n        throw OPE('response parameter \"iss\" (issuer) missing', INVALID_RESPONSE, { parameters });\n    }\n    if (iss && iss !== as.issuer) {\n        throw OPE('unexpected \"iss\" (issuer) response parameter value', INVALID_RESPONSE, {\n            expected: as.issuer,\n            parameters,\n        });\n    }\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            if (state !== undefined) {\n                throw OPE('unexpected \"state\" response parameter encountered', INVALID_RESPONSE, {\n                    expected: undefined,\n                    parameters,\n                });\n            }\n            break;\n        case skipStateCheck:\n            break;\n        default:\n            assertString(expectedState, '\"expectedState\" argument');\n            if (state !== expectedState) {\n                throw OPE(state === undefined\n                    ? 'response parameter \"state\" missing'\n                    : 'unexpected \"state\" response parameter value', INVALID_RESPONSE, { expected: expectedState, parameters });\n            }\n    }\n    const error = getURLSearchParameter(parameters, 'error');\n    if (error) {\n        throw new AuthorizationResponseError('authorization response from the server is an error', {\n            cause: parameters,\n        });\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    const token = getURLSearchParameter(parameters, 'token');\n    if (id_token !== undefined || token !== undefined) {\n        throw new UnsupportedOperationError('implicit and hybrid flows are not supported');\n    }\n    return brand(new URLSearchParams(parameters));\n}\nfunction algToSubtle(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n        case 'ES256':\n        case 'ES384':\n            return { name: 'ECDSA', namedCurve: `P-${alg.slice(-3)}` };\n        case 'ES512':\n            return { name: 'ECDSA', namedCurve: 'P-521' };\n        case 'Ed25519':\n        case 'EdDSA':\n            return 'Ed25519';\n        default:\n            throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg } });\n    }\n}\nasync function importJwk(alg, jwk) {\n    const { ext, key_ops, use, ...key } = jwk;\n    return crypto.subtle.importKey('jwk', key, algToSubtle(alg), true, ['verify']);\n}\nasync function deviceAuthorizationRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'device_authorization_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processDeviceAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Device Authorization Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.device_code, '\"response\" body \"device_code\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.user_code, '\"response\" body \"user_code\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.verification_uri, '\"response\" body \"verification_uri\" property', INVALID_RESPONSE, { body: json });\n    let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n    assertNumber(expiresIn, true, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.expires_in = expiresIn;\n    if (json.verification_uri_complete !== undefined) {\n        assertString(json.verification_uri_complete, '\"response\" body \"verification_uri_complete\" property', INVALID_RESPONSE, { body: json });\n    }\n    if (json.interval !== undefined) {\n        assertNumber(json.interval, false, '\"response\" body \"interval\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function deviceCodeGrantRequest(as, client, clientAuthentication, deviceCode, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(deviceCode, '\"deviceCode\"');\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('device_code', deviceCode);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'urn:ietf:params:oauth:grant-type:device_code', parameters, options);\n}\nasync function processDeviceCodeResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function generateKeyPair(alg, options) {\n    assertString(alg, '\"alg\"');\n    const algorithm = algToSubtle(alg);\n    if (alg.startsWith('PS') || alg.startsWith('RS')) {\n        Object.assign(algorithm, {\n            modulusLength: options?.modulusLength ?? 2048,\n            publicExponent: new Uint8Array([0x01, 0x00, 0x01]),\n        });\n    }\n    return crypto.subtle.generateKey(algorithm, options?.extractable ?? false, [\n        'sign',\n        'verify',\n    ]);\n}\nfunction normalizeHtu(htu) {\n    const url = new URL(htu);\n    url.search = '';\n    url.hash = '';\n    return url.href;\n}\nasync function validateDPoP(request, accessToken, accessTokenClaims, options) {\n    const headerValue = request.headers.get('dpop');\n    if (headerValue === null) {\n        throw OPE('operation indicated DPoP use but the request has no DPoP HTTP Header', INVALID_REQUEST, { headers: request.headers });\n    }\n    if (request.headers.get('authorization')?.toLowerCase().startsWith('dpop ') === false) {\n        throw OPE(`operation indicated DPoP use but the request's Authorization HTTP Header scheme is not DPoP`, INVALID_REQUEST, { headers: request.headers });\n    }\n    if (typeof accessTokenClaims.cnf?.jkt !== 'string') {\n        throw OPE('operation indicated DPoP use but the JWT Access Token has no jkt confirmation claim', INVALID_REQUEST, { claims: accessTokenClaims });\n    }\n    const clockSkew = getClockSkew(options);\n    const proof = await validateJwt(headerValue, checkSigningAlgorithm.bind(undefined, options?.signingAlgorithms, undefined, supported), clockSkew, getClockTolerance(options), undefined)\n        .then(checkJwtType.bind(undefined, 'dpop+jwt'))\n        .then(validatePresence.bind(undefined, ['iat', 'jti', 'ath', 'htm', 'htu']));\n    const now = epochTime() + clockSkew;\n    const diff = Math.abs(now - proof.claims.iat);\n    if (diff > 300) {\n        throw OPE('DPoP Proof iat is not recent enough', JWT_TIMESTAMP_CHECK, {\n            now,\n            claims: proof.claims,\n            claim: 'iat',\n        });\n    }\n    if (proof.claims.htm !== request.method) {\n        throw OPE('DPoP Proof htm mismatch', JWT_CLAIM_COMPARISON, {\n            expected: request.method,\n            claims: proof.claims,\n            claim: 'htm',\n        });\n    }\n    if (typeof proof.claims.htu !== 'string' ||\n        normalizeHtu(proof.claims.htu) !== normalizeHtu(request.url)) {\n        throw OPE('DPoP Proof htu mismatch', JWT_CLAIM_COMPARISON, {\n            expected: normalizeHtu(request.url),\n            claims: proof.claims,\n            claim: 'htu',\n        });\n    }\n    {\n        const expected = b64u(await crypto.subtle.digest('SHA-256', buf(accessToken)));\n        if (proof.claims.ath !== expected) {\n            throw OPE('DPoP Proof ath mismatch', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: proof.claims,\n                claim: 'ath',\n            });\n        }\n    }\n    {\n        let components;\n        switch (proof.header.jwk.kty) {\n            case 'EC':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                    y: proof.header.jwk.y,\n                };\n                break;\n            case 'OKP':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                };\n                break;\n            case 'RSA':\n                components = {\n                    e: proof.header.jwk.e,\n                    kty: proof.header.jwk.kty,\n                    n: proof.header.jwk.n,\n                };\n                break;\n            default:\n                throw new UnsupportedOperationError('unsupported JWK key type', { cause: proof.header.jwk });\n        }\n        const expected = b64u(await crypto.subtle.digest('SHA-256', buf(JSON.stringify(components))));\n        if (accessTokenClaims.cnf.jkt !== expected) {\n            throw OPE('JWT Access Token confirmation mismatch', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: accessTokenClaims,\n                claim: 'cnf.jkt',\n            });\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = headerValue.split('.');\n    const signature = b64u(encodedSignature);\n    const { jwk, alg } = proof.header;\n    if (!jwk) {\n        throw OPE('DPoP Proof is missing the jwk header parameter', INVALID_REQUEST, {\n            header: proof.header,\n        });\n    }\n    const key = await importJwk(alg, jwk);\n    if (key.type !== 'public') {\n        throw OPE('DPoP Proof jwk header parameter must contain a public key', INVALID_REQUEST, {\n            header: proof.header,\n        });\n    }\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n}\nasync function validateJwtAccessToken(as, request, expectedAudience, options) {\n    assertAs(as);\n    if (!looseInstanceOf(request, Request)) {\n        throw CodedTypeError('\"request\" must be an instance of Request', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(expectedAudience, '\"expectedAudience\"');\n    const authorization = request.headers.get('authorization');\n    if (authorization === null) {\n        throw OPE('\"request\" is missing an Authorization HTTP Header', INVALID_REQUEST, {\n            headers: request.headers,\n        });\n    }\n    let { 0: scheme, 1: accessToken, length } = authorization.split(' ');\n    scheme = scheme.toLowerCase();\n    switch (scheme) {\n        case 'dpop':\n        case 'bearer':\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported Authorization HTTP Header scheme', {\n                cause: { headers: request.headers },\n            });\n    }\n    if (length !== 2) {\n        throw OPE('invalid Authorization HTTP Header format', INVALID_REQUEST, {\n            headers: request.headers,\n        });\n    }\n    const requiredClaims = [\n        'iss',\n        'exp',\n        'aud',\n        'sub',\n        'iat',\n        'jti',\n        'client_id',\n    ];\n    if (options?.requireDPoP || scheme === 'dpop' || request.headers.has('dpop')) {\n        requiredClaims.push('cnf');\n    }\n    const { claims, header } = await validateJwt(accessToken, checkSigningAlgorithm.bind(undefined, options?.signingAlgorithms, undefined, supported), getClockSkew(options), getClockTolerance(options), undefined)\n        .then(checkJwtType.bind(undefined, 'at+jwt'))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, expectedAudience))\n        .catch(reassignRSCode);\n    for (const claim of ['client_id', 'jti', 'sub']) {\n        if (typeof claims[claim] !== 'string') {\n            throw OPE(`unexpected JWT \"${claim}\" claim type`, INVALID_REQUEST, { claims });\n        }\n    }\n    if ('cnf' in claims) {\n        if (!isJsonObject(claims.cnf)) {\n            throw OPE('unexpected JWT \"cnf\" (confirmation) claim value', INVALID_REQUEST, { claims });\n        }\n        const { 0: cnf, length } = Object.keys(claims.cnf);\n        if (length) {\n            if (length !== 1) {\n                throw new UnsupportedOperationError('multiple confirmation claims are not supported', {\n                    cause: { claims },\n                });\n            }\n            if (cnf !== 'jkt') {\n                throw new UnsupportedOperationError('unsupported JWT Confirmation method', {\n                    cause: { claims },\n                });\n            }\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = accessToken.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    if (options?.requireDPoP ||\n        scheme === 'dpop' ||\n        claims.cnf?.jkt !== undefined ||\n        request.headers.has('dpop')) {\n        await validateDPoP(request, accessToken, claims, options).catch(reassignRSCode);\n    }\n    return claims;\n}\nfunction reassignRSCode(err) {\n    if (err instanceof OperationProcessingError && err?.code === INVALID_REQUEST) {\n        err.code = INVALID_RESPONSE;\n    }\n    throw err;\n}\nasync function backchannelAuthenticationRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'backchannel_authentication_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processBackchannelAuthenticationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 200, 'Backchannel Authentication Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.auth_req_id, '\"response\" body \"auth_req_id\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n    assertNumber(expiresIn, true, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.expires_in = expiresIn;\n    if (json.interval !== undefined) {\n        assertNumber(json.interval, false, '\"response\" body \"interval\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function backchannelAuthenticationGrantRequest(as, client, clientAuthentication, authReqId, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(authReqId, '\"authReqId\"');\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('auth_req_id', authReqId);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'urn:openid:params:grant-type:ciba', parameters, options);\n}\nasync function processBackchannelAuthenticationGrantResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function dynamicClientRegistrationRequest(as, metadata, options) {\n    assertAs(as);\n    const url = resolveEndpoint(as, 'registration_endpoint', metadata.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    headers.set('content-type', 'application/json');\n    const method = 'POST';\n    if (options?.DPoP) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, method, options.initialAccessToken);\n    }\n    if (options?.initialAccessToken) {\n        headers.set('authorization', `${headers.has('dpop') ? 'DPoP' : 'Bearer'} ${options.initialAccessToken}`);\n    }\n    const response = await (options?.[customFetch] || fetch)(url.href, {\n        body: JSON.stringify(metadata),\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: signal(url, options?.signal),\n    });\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nasync function processDynamicClientRegistrationResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    checkAuthenticationChallenges(response);\n    await checkOAuthBodyError(response, 201, 'Dynamic Client Registration Endpoint');\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.client_id, '\"response\" body \"client_id\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    if (json.client_secret !== undefined) {\n        assertString(json.client_secret, '\"response\" body \"client_secret\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    if (json.client_secret) {\n        assertNumber(json.client_secret_expires_at, true, '\"response\" body \"client_secret_expires_at\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function resourceDiscoveryRequest(resourceIdentifier, options) {\n    return performDiscovery(resourceIdentifier, 'resourceIdentifier', (url) => {\n        prependWellKnown(url, '.well-known/oauth-protected-resource', true);\n        return url;\n    }, options);\n}\nasync function processResourceDiscoveryResponse(expectedResourceIdentifier, response) {\n    const expected = expectedResourceIdentifier;\n    if (!(expected instanceof URL) && expected !== _nodiscoverycheck) {\n        throw CodedTypeError('\"expectedResourceIdentifier\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform Resource Server Metadata response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    const json = await getResponseJsonBody(response);\n    assertString(json.resource, '\"response\" body \"resource\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    if (expected !== _nodiscoverycheck && new URL(json.resource).href !== expected.href) {\n        throw OPE('\"response\" body \"resource\" property does not match the expected value', JSON_ATTRIBUTE_COMPARISON, { expected: expected.href, body: json, attribute: 'resource' });\n    }\n    return json;\n}\nasync function getResponseJsonBody(response, check = assertApplicationJson) {\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        check(response);\n        throw OPE('failed to parse \"response\" body as JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(json)) {\n        throw OPE('\"response\" body must be a top level object', INVALID_RESPONSE, { body: json });\n    }\n    return json;\n}\nconst _nopkce = nopkce;\nconst _nodiscoverycheck = Symbol();\nconst _expectedIssuer = Symbol();\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth4webapi/build/index.js\n");

/***/ })

};
;