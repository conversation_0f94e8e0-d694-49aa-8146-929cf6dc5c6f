'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { FaArrow<PERSON><PERSON>t, FaSpinner, FaEdit, FaTrash, FaCheck, FaTimes } from 'react-icons/fa';
import { getImageUrl } from '@/lib/imageUtils';

interface AdminUser {
  username: string;
  admintype: string;
  allowedcases: string;
  discordUserId?: string;
}

interface Product {
  _id: string;
  projectName: string;
  projectDescription: string;
  version: string;
  productType: string;
  price: number;
  discount?: number;
  createdBy: string;
  discordUserId?: string;
  createdAt: string;
  status: string;
  screenshotUrls: Array<{
    fileId: string;
    filename: string;
    contentType: string;
  }>;
}

// Calculate discounted price
const calculateDiscountedPrice = (price: number, discount?: number): number => {
  if (!discount || discount <= 0 || price <= 0) return price;
  return Math.round(price * (1 - discount / 100));
};

export default function MyProductsPage() {
  const router = useRouter();
  const [admin, setAdmin] = useState<AdminUser | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getAdminInfo = async () => {
      try {
        const response = await fetch('/api/admin/me');
        
        if (!response.ok) {
          if (response.status === 401) {
            router.push('/admin/login');
            return;
          }
          throw new Error('Kunne ikke hente admin information');
        }
        
        const data = await response.json();
        setAdmin(data.user);
        
        // Ensure only freelancers can access this page
        if (data.user.admintype !== 'Freelancer') {
          router.push('/admin/dashboard');
        } else {
          // Fetch user's products
          fetchMyProducts(data.user.username);
        }
      } catch (error) {
        console.error('Error fetching admin info:', error);
        setError('Kunne ikke hente admin information');
        router.push('/admin/login');
      }
    };

    getAdminInfo();
  }, [router]);

  useEffect(() => {
    if (loading) {
      document.body.style.overflow = 'hidden';
      window.scrollTo(0, 0);
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [loading]);

  const fetchMyProducts = async (username: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/products/my-products?username=${encodeURIComponent(username)}`);
      
      if (!response.ok) {
        throw new Error('Kunne ikke hente dine produkter');
      }
      
      const data = await response.json();
      setProducts(data);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Der opstod en fejl');
    } finally {
      setLoading(false);
    }
  };

  const getProductTypeLabel = (type: string) => {
    switch (type) {
      case 'plugin':
        return 'Plugin';
      case 'script':
        return 'Skript';
      case 'map':
        return 'Map';
      case 'build':
        return 'Build';
      default:
        return type;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <FaCheck className="w-3 h-3 mr-1" />
            Godkendt
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <FaTimes className="w-3 h-3 mr-1" />
            Afvist
          </span>
        );
      case 'pending':
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <svg className="w-3 h-3 mr-1 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Afventer godkendelse
          </span>
        );
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('da-DK', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90 backdrop-blur-sm">
        <div className="flex flex-col items-center max-w-sm mx-auto p-8 rounded-xl bg-white shadow-lg border border-gray-100">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mb-6" style={{ animationDuration: '0.4s' }}></div>
          <div className="text-blue-700 text-lg font-medium">Indlæser dine produkter...</div>
          <p className="text-gray-500 text-sm mt-2 text-center">Vent venligst mens vi henter dine produkter</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20 px-4 pb-12">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <Link href="/admin/dashboard" className="inline-flex items-center text-gray-600 hover:text-blue-600 mb-2 group">
              <FaArrowLeft className="mr-2 transform group-hover:-translate-x-1 group-hover:scale-125 transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]" />
              <span>Tilbage til Dashboard</span>
            </Link>
            <h1 className="text-3xl font-bold text-gray-800">Mine Produkter</h1>
          </div>
          <Link 
            href="/admin/products/upload" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Upload Nyt Produkt
          </Link>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
            <div className="flex">
              <svg className="h-5 w-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-sm text-red-600">{error}</p>
            </div>
          </div>
        )}

        {!error && products.length === 0 && (
          <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
            <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Ingen produkter endnu</h2>
            <p className="text-gray-600 mb-6">Du har ikke uploadet nogen produkter endnu.</p>
            <Link 
              href="/admin/products/upload" 
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Upload Dit Første Produkt
            </Link>
          </div>
        )}

        {!error && products.length > 0 && (
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Produkt
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Version
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pris
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Oprettet
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Handlinger
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {products.map((product) => (
                    <tr key={product._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0 bg-gray-100 rounded">
                            {product.screenshotUrls && product.screenshotUrls.length > 0 ? (
                              <img 
                                src={getImageUrl(product.screenshotUrls[0])}
                                alt={product.projectName}
                                className="h-10 w-10 rounded object-cover"
                              />
                            ) : (
                              <div className="h-10 w-10 rounded flex items-center justify-center bg-blue-100 text-blue-500">
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{product.projectName}</div>
                            <div className="text-sm text-gray-500 truncate max-w-xs">{product.projectDescription.substring(0, 24)}...</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900">{getProductTypeLabel(product.productType)}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900">{product.version}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {product.price === 0 ? (
                          <span className="text-sm text-green-600 font-medium">Gratis</span>
                        ) : product.discount && product.discount > 0 ? (
                          <div className="flex flex-col">
                            <span className="text-sm text-gray-900 font-medium flex items-center">
                              {calculateDiscountedPrice(product.price, product.discount)} DKK
                              <span className="ml-2 px-2 py-0.5 bg-amber-100 text-amber-800 text-xs rounded-full">
                                -{product.discount}%
                              </span>
                            </span>
                            <span className="text-xs text-gray-500 line-through">
                              {product.price} DKK
                            </span>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-900">{product.price} DKK</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(product.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(product.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Link 
                          href={`/admin/products/edit/${product._id}`}
                          className="text-indigo-600 hover:text-indigo-900 mr-4"
                        >
                          <FaEdit className="inline mr-1" />
                          Rediger
                        </Link>
                        <Link 
                          href={`/products/${product._id}`}
                          className="text-blue-600 hover:text-blue-900 mr-4"
                        >
                          Se produktside
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 