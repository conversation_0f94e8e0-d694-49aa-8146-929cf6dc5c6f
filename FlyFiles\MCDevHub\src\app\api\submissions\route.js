import { NextResponse } from 'next/server';
import { MongoClient } from 'mongodb';

export const runtime = 'nodejs'; // Use Node.js runtime

// MongoDB configuration
if (!process.env.MONGODB_URI) {
  throw new Error('Please add your Mongo URI to .env.local');
}

const uri = process.env.MONGODB_URI;
const options = {};

// Set up MongoDB client with proper connection pooling
let client;
let clientPromise;

if (process.env.NODE_ENV === 'development') {
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri, options);
    global._mongoClientPromise = client.connect();
  }
  clientPromise = global._mongoClientPromise;
} else {
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

export async function GET() {
  try {
    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db("codehub");
    
    // Get all submissions sorted by date (newest first)
    const submissions = await db
      .collection("form_submissions")
      .find({})
      .sort({ submittedAt: -1 })
      .toArray();
    
    // Return submissions as JSON
    return NextResponse.json(submissions);
  } catch (error) {
    console.error('Error fetching submissions:', error);
    return NextResponse.json(
      { error: 'Kunne ikke hente indsendelser' },
      { status: 500 }
    );
  }
} 