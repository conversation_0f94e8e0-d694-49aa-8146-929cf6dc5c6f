'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';

/**
 * This component syncs the user's Discord avatar with the database
 * on page load if they're already logged in.
 */
export default function AvatarSync() {
  const { user, isLoading } = useAuth();
  
  useEffect(() => {
    // Only run this effect if the user is logged in and auth is not loading
    if (user && !isLoading) {
      // Check if we've synced recently (in the last 5 minutes)
      const now = Date.now();
      const lastSyncTime = parseInt(localStorage.getItem('lastAvatarSync') || '0', 10);
      const fiveMinutes = 5 * 60 * 1000;
      
      // Only sync if it's been more than 5 minutes since the last sync
      if (now - lastSyncTime > fiveMinutes) {
        const syncAvatar = async () => {
          try {
            const response = await fetch('/api/auth/update-discord', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              }
            });
            
            const result = await response.json();
            console.log('Avatar sync result:', result);
            
            // Store the sync time
            localStorage.setItem('lastAvatarSync', now.toString());
          } catch (error) {
            console.error('Error syncing avatar:', error);
          }
        };
        
        // Add a small delay to ensure we don't interfere with other initialization
        const timeoutId = setTimeout(syncAvatar, 1000);
        return () => clearTimeout(timeoutId);
      }
    }
  }, [user, isLoading]);
  
  // This component doesn't render anything
  return null;
} 