'use client';

import { useSession } from "next-auth/react";
import Link from "next/link";
import { FaBolt, FaLock, FaHeadset, FaServer, FaTools, FaCloud, FaCheckCircle, FaUpload } from 'react-icons/fa';

export default function Home() {
  const { data: session } = useSession();

  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 text-white py-16 sm:py-20 md:py-28 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/home-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="flex flex-col lg:flex-row items-center">
            <div className="lg:w-1/2 lg:pr-10 text-center lg:text-left mb-10 lg:mb-0">
              <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/30 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium cursor-default">
                Danmarks nye fildelingsplatform
              </div>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 sm:mb-6 leading-tight">
                Send store filer <span className="text-blue-300">sikkert</span> og nemt
              </h1>
              <p className="text-lg sm:text-xl text-blue-100 mb-6 sm:mb-8 max-w-xl mx-auto lg:mx-0">
                FlyFiles gør det nemt at dele store filer med venner, familie og kolleger. Sikker, hurtig og brugervenlig.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                {session ? (
                  <Link
                    href="/dashboard"
                    className="group relative bg-white text-blue-800 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20 text-center sm:text-left"
                  >
                    <span className="relative z-10 flex items-center justify-center">
                      <FaUpload className="mr-2" />
                      Gå til Dashboard
                      <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </Link>
                ) : (
                  <>
                    <button className="group relative bg-white text-blue-800 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20 text-center sm:text-left">
                      <span className="relative z-10 flex items-center justify-center">
                        <FaUpload className="mr-2" />
                        Upload som gæst (250MB)
                        <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      </span>
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </button>
                    <Link
                      href="/pricing"
                      className="group relative bg-transparent border-2 border-white text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-white/20 text-center sm:text-left"
                    >
                      <span className="relative z-10 flex items-center justify-center">
                        Log ind for 15GB
                        <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      </span>
                      <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </Link>
                  </>
                )}
              </div>
            </div>
            <div className="lg:w-1/2 relative w-full max-w-md mx-auto lg:max-w-none">
              <div className="w-full h-[300px] sm:h-[350px] md:h-[400px] relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-indigo-600/30 rounded-2xl overflow-hidden shadow-2xl backdrop-blur-sm border border-white/10">
                  {/* Upload interface mockup */}
                  <div className="absolute top-0 left-0 right-0 h-8 bg-[#333333] flex items-center justify-between px-2">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-[#ec6a5f] rounded-full mr-1.5"></div>
                      <div className="w-3 h-3 bg-[#f4bf4f] rounded-full mr-1.5"></div>
                      <div className="w-3 h-3 bg-[#61c554] rounded-full"></div>
                    </div>
                    <div className="text-gray-200 text-xs font-mono flex-1 text-center">FlyFiles - Send Filer</div>
                    <div className="w-6"></div>
                  </div>
                  {/* Upload area */}
                  <div className="absolute top-8 left-0 right-0 bottom-0 bg-white p-6 flex flex-col items-center justify-center">
                    <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                      <FaCloud className="w-10 h-10 text-blue-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">Træk filer hertil</h3>
                    <p className="text-gray-600 text-center mb-4">eller klik for at vælge filer</p>
                    <div className="w-full max-w-xs bg-blue-600 text-white py-2 px-4 rounded-lg text-center font-medium">
                      Vælg filer
                    </div>
                    <p className="text-xs text-gray-500 mt-3">Op til 250MB som gæst • 15GB med konto</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-gray-50 to-transparent"></div>
      </section>

      {/* Guest Upload Section */}
      {!session && (
        <section className="py-16 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="border-2 border-dashed border-gray-300 hover:border-blue-500 transition-colors rounded-xl p-8 bg-gray-50/50">
              <div className="text-center">
                <FaUpload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Træk filer hertil eller klik for at uploade</h3>
                <p className="text-gray-600 mb-4">
                  Som gæst kan du uploade op til 250MB per session. Filer udløber efter 7 dage.
                </p>
                <button className="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                  Vælg filer
                </button>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Features Section */}
      <section className="py-16 sm:py-20 md:py-24 bg-white">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-10 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Hvorfor vælge FlyFiles?
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-base sm:text-lg">
              Bygget til danskere med fokus på sikkerhed, hastighed og brugervenlighed
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 relative overflow-hidden group hover:border-blue-200">
              <div className="absolute top-0 right-0 w-40 h-40 bg-blue-100 rounded-full -mr-20 -mt-20 transition-all duration-700 group-hover:scale-150 group-hover:bg-blue-200"></div>
              <div className="relative">
                <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6 text-blue-600 group-hover:bg-blue-600 group-hover:text-white transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-3">
                  <FaLock className="w-7 h-7" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Sikker opbevaring</h3>
                <p className="text-gray-600 mb-4">
                  Dine filer krypteres og opbevares sikkert med automatisk sletning efter udløb.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>End-to-end kryptering</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>GDPR-compliant</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Automatisk sletning</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 relative overflow-hidden group hover:border-green-200">
              <div className="absolute top-0 right-0 w-40 h-40 bg-green-100 rounded-full -mr-20 -mt-20 transition-all duration-700 group-hover:scale-150 group-hover:bg-green-200"></div>
              <div className="relative">
                <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mb-6 text-green-600 group-hover:bg-green-600 group-hover:text-white transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-3">
                  <FaBolt className="w-7 h-7" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Lynhurtig upload</h3>
                <p className="text-gray-600 mb-4">
                  Optimeret infrastruktur sikrer hurtig upload og download af dine filer.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-green-100 text-green-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Hurtig upload</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-green-100 text-green-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Resumable uploads</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-green-100 text-green-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Batch upload</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 relative overflow-hidden group hover:border-indigo-200">
              <div className="absolute top-0 right-0 w-40 h-40 bg-indigo-100 rounded-full -mr-20 -mt-20 transition-all duration-700 group-hover:scale-150 group-hover:bg-indigo-200"></div>
              <div className="relative">
                <div className="w-16 h-16 bg-indigo-100 rounded-xl flex items-center justify-center mb-6 text-indigo-600 group-hover:bg-indigo-600 group-hover:text-white transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-3">
                  <FaHeadset className="w-7 h-7" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Dansk support</h3>
                <p className="text-gray-600 mb-4">
                  Lokalproduceret platform med dansk kundeservice og GDPR-compliance.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Dansk support</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Hurtig svartid</span>
                  </li>
                  <li className="flex items-center text-gray-600">
                    <span className="h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center flex-shrink-0 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span>Lokalt team</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 sm:py-20 md:py-24 bg-gradient-to-r from-blue-700 to-blue-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/cta-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 text-center relative z-10">
          <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/40 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium">
            Start i dag
          </div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6">
            Klar til at dele dine filer?
          </h2>
          <p className="text-lg sm:text-xl text-blue-100 mb-8 sm:mb-10 max-w-2xl mx-auto">
            Kom i gang med FlyFiles i dag og oplev hvor nemt det er at dele store filer sikkert.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {session ? (
              <Link
                href="/dashboard"
                className="group relative bg-white text-blue-800 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-white/20 text-center"
              >
                <span className="relative z-10 flex items-center justify-center">
                  Gå til Dashboard
                  <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
              </Link>
            ) : (
              <>
                <button className="group relative bg-white text-blue-800 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-white/20 text-center">
                  <span className="relative z-10 flex items-center justify-center">
                    Start gratis upload
                    <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                </button>
                <Link
                  href="/pricing"
                  className="group relative bg-transparent border-2 border-white text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-white/20 text-center"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Se priser
                    <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </Link>
              </>
            )}
          </div>
        </div>
      </section>
    </main>
  )
}
