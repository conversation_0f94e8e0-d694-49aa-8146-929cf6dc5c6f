"use client"

import { useSession } from "next-auth/react"
import Link from "next/link"
import { Button } from "./components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./components/ui/card"
import { Upload, Shield, Clock, Users } from "lucide-react"

export default function Home() {
  const { data: session } = useSession()

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-950 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Send store filer <span className="text-blue-600">sikkert</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              FlyFiles er Danmarks nye fildelingsplatform. Send filer op til 50GB med nem deling og sikker opbevaring.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {session ? (
                <Link href="/dashboard">
                  <Button size="lg" className="flex items-center space-x-2">
                    <Upload className="h-5 w-5" />
                    <span>Gå til Dashboard</span>
                  </Button>
                </Link>
              ) : (
                <>
                  <Button size="lg" className="flex items-center space-x-2">
                    <Upload className="h-5 w-5" />
                    <span>Upload som gæst (250MB)</span>
                  </Button>
                  <Link href="/login">
                    <Button variant="outline" size="lg">
                      Log ind for 15GB
                    </Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Guest Upload Section */}
      {!session && (
        <section className="py-16 bg-white dark:bg-gray-950">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <Card className="border-2 border-dashed border-gray-300 dark:border-gray-700 hover:border-blue-500 transition-colors">
              <CardContent className="p-8">
                <div className="text-center">
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Drag filer hertil eller klik for at uploade</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Som gæst kan du uploade op til 250MB per session. Filer udløber efter 7 dage.
                  </p>
                  <Button className="mt-4">Vælg filer</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      )}

      {/* Features Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Hvorfor vælge FlyFiles?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Bygget til danskere med fokus på sikkerhed og brugervenlighed
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <Shield className="h-8 w-8 text-blue-600 mb-2" />
                <CardTitle>Sikker opbevaring</CardTitle>
                <CardDescription>
                  Dine filer krypteres og opbevares sikkert med automatisk sletning
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Clock className="h-8 w-8 text-blue-600 mb-2" />
                <CardTitle>Automatisk udløb</CardTitle>
                <CardDescription>
                  Filer slettes automatisk efter udløbsdatoen for din sikkerhed
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Users className="h-8 w-8 text-blue-600 mb-2" />
                <CardTitle>Dansk support</CardTitle>
                <CardDescription>
                  Lokalproduceret platform med dansk kundeservice og GDPR-compliance
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Pricing Teaser */}
      <section className="py-20 bg-white dark:bg-gray-950">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Vælg den plan der passer til dig
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            Fra gratis gæsteadgang til professionelle løsninger
          </p>
          <Link href="/pricing">
            <Button size="lg" variant="outline">
              Se alle priser
            </Button>
          </Link>
        </div>
      </section>
    </div>
  )
}
