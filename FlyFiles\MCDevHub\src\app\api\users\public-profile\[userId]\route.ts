import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb.js';
import { auth } from '@/lib/auth.js';
import { ObjectId } from 'mongodb';
import { getProfileDataAccess, filterProfileData } from '@/lib/profilePermissions';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params;
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Get the current user from NextAuth (optional - for permission checks)
    const session = await auth();
    
    // Connect to database
    const { db } = await connectToDatabase();
    
    // First, try to find the user in adminusers collection (freelancers/developers)
    let user = await db.collection('adminusers').findOne(
      { discordUserId: userId },
      { 
        projection: {
          username: 1,
          discordUserId: 1,
          avatar: 1,
          avatarUrl: 1,
          bannerImage: 1,
          description: 1,
          isVerified: 1,
          verifiedAt: 1,
          email: 1,
          youtubeUrl: 1,
          githubUsername: 1,
          createdAt: 1,
          openForTasks: 1,
          admintype: 1,
          badges: 1,
          // Exclude sensitive data
          password: 0,
          allowedcases: 0
        }
      }
    );

    let isFreelancer = false;
    if (user) {
      isFreelancer = user.admintype === 'Freelancer';
    } else {
      // If not found in adminusers, check regular users collection
      user = await db.collection('users').findOne(
        { discordUserId: userId },
        { 
          projection: {
            username: 1,
            discordUserId: 1,
            createdAt: 1,
            badges: 1,
            // Exclude sensitive data
            auth_id: 0,
            balance: 0,
            notifications: 0
          }
        }
      );
    }

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get user's public purchases (exclude sensitive payment information)
    const purchases = await db.collection('user_purchases')
      .find(
        { userId: userId },
        { 
          projection: {
            productId: 1,
            productName: 1,
            productType: 1,
            amount: 1,
            purchaseDate: 1,
            seller: 1,
            // Exclude sensitive payment data
            paymentMethod: 0,
            transactionId: 0,
            stripeSessionId: 0
          }
        }
      )
      .sort({ purchaseDate: -1 })
      .limit(50) // Limit to recent purchases
      .toArray();

    // Enhance purchases with product thumbnails
    const enhancedPurchases = await Promise.all(purchases.map(async (purchase: any) => {
      try {
        let product = null;
        const productId = purchase.productId;
        
        // Try to find the product
        product = await db.collection('products').findOne({ _id: productId });
        
        if (!product && ObjectId.isValid(productId)) {
          product = await db.collection('products').findOne({ _id: new ObjectId(productId) });
        }
        
        if (product && product.screenshotUrls && product.screenshotUrls.length > 0) {
          const screenshot = product.screenshotUrls.find((ss: any) => ss.url) || product.screenshotUrls[0];
          
          return {
            ...purchase,
            thumbnailUrl: screenshot.url || null
          };
        }
        
        return purchase;
      } catch (error) {
        return purchase;
      }
    }));

    // Get user's public favorites
    const userWithFavorites = await db.collection('users').findOne(
      { discordUserId: userId },
      { projection: { favorites: 1 } }
    );
    
    const favorites = userWithFavorites?.favorites || [];

    // Get verification status
    const verificationStatus = await db.collection('verified').findOne(
      { discordId: userId },
      { projection: { isVerified: 1, verifiedAt: 1 } }
    );

    // Get access permissions for the current user
    const currentUserId = session?.user?.id;
    const isAdmin = session?.user?.isAdmin || false;
    const access = getProfileDataAccess(currentUserId, userId, isAdmin);

    // Build full profile data
    const fullProfile = {
      user: {
        username: user.username,
        discordUserId: user.discordUserId,
        avatar: user.avatar,
        avatarUrl: user.avatarUrl,
        bannerImage: user.bannerImage,
        description: user.description,
        createdAt: user.createdAt,
        badges: user.badges || [],
        isFreelancer,
        email: user.email,
        youtubeUrl: user.youtubeUrl,
        githubUsername: user.githubUsername,
        openForTasks: user.openForTasks
      },
      purchases: enhancedPurchases,
      favorites,
      verification: {
        isVerified: verificationStatus?.isVerified || user.isVerified || false,
        verifiedAt: verificationStatus?.verifiedAt || user.verifiedAt || null
      },
      isOwnProfile: currentUserId === userId
    };

    // Filter the profile data based on access permissions
    const filteredProfile = filterProfileData(fullProfile, access);

    return NextResponse.json(filteredProfile);
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
