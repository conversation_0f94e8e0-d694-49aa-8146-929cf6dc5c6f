'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

interface LogoutButtonProps {
  className?: string;
}

export default function LogoutButton({ className = '' }: LogoutButtonProps) {
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    if (isLoggingOut) return;
    
    try {
      setIsLoggingOut(true);
      const startTime = Date.now();
      
      try {
        const response = await fetch('/api/admin/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(2000 - elapsedTime, 0);
        await new Promise(resolve => setTimeout(resolve, remainingTime));

        if (response.ok) {
          router.push('/admin/login');
        } else {
          console.error('Fejl ved logout');
          setIsLoggingOut(false);
        }
      } catch (error) {
        console.error('Fejl under logout:', error);
        setIsLoggingOut(false);
      }
    } catch (error) {
        console.error('Fejl under logout:', error);
      setIsLoggingOut(false);
    }
  };

    return (
      <button
        onClick={handleLogout}
        disabled={isLoggingOut}
        className={`flex items-center justify-center px-4 py-2 rounded-lg transition-all 
          ${isLoggingOut 
            ? 'bg-gray-100 cursor-default' 
            : 'bg-white hover:bg-red-50 border border-gray-200 hover:border-red-200 shadow-sm hover:shadow-md'
          } ${className}`}
      >
        {isLoggingOut ? (
          <div className="flex items-center text-gray-600">
            <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Logger ud...
          </div>
        ) : (
          <div className="flex items-center text-red-600 hover:text-red-700">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            Log ud
          </div>
        )}
      </button>
    );
  }