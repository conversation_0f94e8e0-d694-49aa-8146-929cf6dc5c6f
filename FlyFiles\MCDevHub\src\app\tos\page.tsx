'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { FaGavel, FaKey, FaExclamationTriangle, FaHeadset, FaTasks, FaShieldAlt, FaEnvelope, FaArrowRight, FaChevronDown, FaChevronUp, FaClock, FaArrowUp, FaLock, FaExclamationCircle, FaArrowLeft } from 'react-icons/fa';

export default function TOSPage() {
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('');
  
  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      // Show scroll to top button after scrolling down 500px
      setShowScrollTop(window.scrollY > 500);
      
      // Update active section based on scroll position
      const sections = document.querySelectorAll('div[id]');
      sections.forEach(section => {
        const sectionTop = section.getBoundingClientRect().top;
        if (sectionTop < 100) {
          setActiveSection(section.id);
        }
      });
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // Smooth scroll function
  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 80,
        behavior: 'smooth'
      });
      setIsMobileNavOpen(false);
    }
  };
  
  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <main className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      {/* Hero Section */}
      <section id="hero" className="bg-gradient-to-r from-blue-700 via-blue-800 to-indigo-900 text-white py-24 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/tos-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="absolute -bottom-10 -left-10 right-0 w-[120%] h-40 bg-white/5 blur-3xl transform rotate-2 rounded-full opacity-60"></div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="inline-flex items-center justify-center mb-6 bg-white/10 p-4 rounded-full backdrop-blur-sm border border-white/20 shadow-lg">
            <FaGavel className="text-4xl text-white" />
          </div>
          <h1 className="text-4xl md:text-6xl font-extrabold mb-6 tracking-tight">Brugsvilkår</h1>
          <p className="text-xl text-blue-100 md:max-w-2xl mx-auto font-light">
            Disse vilkår gælder for brug af MCDevHub tjenester og produkter. Læs dem venligst grundigt, inden du fortsætter.
          </p>
          <div className="flex items-center justify-center mt-8 text-sm md:text-base">
            <div className="bg-white/10 px-4 py-2 rounded-full backdrop-blur-sm border border-white/20 shadow-sm flex items-center font-medium relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-105 group">
              <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute -bottom-8 left-1/2 w-32 h-32 bg-white/5 rounded-full blur-xl transform -translate-x-1/2 scale-0 group-hover:scale-100 transition-all duration-500"></div>
              <FaClock className="mr-2 text-blue-200" />
              <span>Sidst opdateret: 17. april 2025</span>
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-white to-transparent"></div>
        <div className="flex justify-center mt-8">
          <button aria-label="Scroll til generelt" onClick={() => scrollToSection('generelt')} className="p-3 bg-white/20 hover:bg-white/30 rounded-full transition">
            <FaChevronDown className="text-white w-6 h-6" />
          </button>
        </div>
      </section>

      {/* Mobile TOC Toggle - visible only on mobile devices */}
      <div className="lg:hidden sticky top-4 z-40 px-4 mt-4">
        <button 
          onClick={() => setIsMobileNavOpen(!isMobileNavOpen)}
          className="w-full bg-white rounded-xl shadow-lg p-4 flex items-center justify-between border border-gray-100 transition-all duration-300 hover:shadow-xl"
        >
          <div className="flex items-center">
            <div className="bg-blue-100 text-blue-600 rounded-lg w-8 h-8 inline-flex items-center justify-center mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
              </svg>
            </div>
            <span className="font-bold text-gray-900">Indholdsfortegnelse</span>
          </div>
          {isMobileNavOpen ? <FaChevronUp className="text-gray-500" /> : <FaChevronDown className="text-gray-500" />}
        </button>
        
        {/* Mobile TOC content */}
        <div className={`bg-white rounded-xl shadow-xl mt-2 border border-gray-100 overflow-hidden transition-all duration-300 ${isMobileNavOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'}`}>
          <div className="p-4">
            <ul className="space-y-3">
              <li>
                <button 
                  onClick={() => scrollToSection('generelt')} 
                  className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'generelt' ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-500'}`}
                >
                  Generelle vilkår
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('licens')} 
                  className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'licens' ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-500'}`}
                >
                  Licensbetingelser
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('brug')} 
                  className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'brug' ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-500'}`}
                >
                  Begrænset brug
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('support')} 
                  className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'support' ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-500'}`}
                >
                  Support
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('special')} 
                  className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'special' ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-500'}`}
                >
                  Specialbestillinger
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('ansvar')} 
                  className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'ansvar' ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-500'}`}
                >
                  Ansvarsbegrænsning
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('kontakt')} 
                  className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'kontakt' ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-500'}`}
                >
                  Kontakt
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* TOS Content */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Navigation sidebar for desktop */}
            <div className="lg:w-80 shrink-0 hidden lg:block">
              <div className="bg-white rounded-2xl shadow-xl p-6 sticky top-28 border border-gray-100 overflow-hidden group transition-all duration-300 hover:shadow-2xl">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
                <div className="absolute -right-20 -top-20 w-40 h-40 bg-blue-50 rounded-full opacity-70 group-hover:bg-blue-100 transition-all duration-500"></div>
                <div className="relative">
                  <h3 className="font-bold text-xl text-gray-900 mb-6 flex items-center">
                    <span className="bg-blue-100 text-blue-600 rounded-lg w-8 h-8 inline-flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                      </svg>
                    </span>
                    Indhold
                  </h3>
                  <ul className="space-y-5">
                    <li>
                      <button
                        onClick={() => scrollToSection('generelt')}
                        className={`text-left w-full ${activeSection === 'generelt' ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'} flex items-center group transition-all duration-300 ease-in-out`}
                      >
                        <span className={`${activeSection === 'generelt' ? 'bg-blue-100' : 'bg-blue-50'} text-blue-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-blue-100 transition-all duration-300 ease-in-out shadow-sm`}>
                          <FaGavel className="text-sm" />
                        </span>
                        <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Generelle vilkår</span>
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={() => scrollToSection('licens')}
                        className={`text-left w-full ${activeSection === 'licens' ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'} flex items-center group transition-all duration-300 ease-in-out`}
                      >
                        <span className={`${activeSection === 'licens' ? 'bg-blue-100' : 'bg-blue-50'} text-blue-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-blue-100 transition-all duration-300 ease-in-out shadow-sm`}>
                          <FaKey className="text-sm" />
                        </span>
                        <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Licensbetingelser</span>
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={() => scrollToSection('brug')}
                        className={`text-left w-full ${activeSection === 'brug' ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'} flex items-center group transition-all duration-300 ease-in-out`}
                      >
                        <span className={`${activeSection === 'brug' ? 'bg-blue-100' : 'bg-blue-50'} text-blue-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-blue-100 transition-all duration-300 ease-in-out shadow-sm`}>
                          <FaExclamationTriangle className="text-sm" />
                        </span>
                        <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Begrænset brug</span>
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={() => scrollToSection('support')}
                        className={`text-left w-full ${activeSection === 'support' ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'} flex items-center group transition-all duration-300 ease-in-out`}
                      >
                        <span className={`${activeSection === 'support' ? 'bg-blue-100' : 'bg-blue-50'} text-blue-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-blue-100 transition-all duration-300 ease-in-out shadow-sm`}>
                          <FaHeadset className="text-sm" />
                        </span>
                        <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Support</span>
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={() => scrollToSection('special')}
                        className={`text-left w-full ${activeSection === 'special' ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'} flex items-center group transition-all duration-300 ease-in-out`}
                      >
                        <span className={`${activeSection === 'special' ? 'bg-blue-100' : 'bg-blue-50'} text-blue-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-blue-100 transition-all duration-300 ease-in-out shadow-sm`}>
                          <FaTasks className="text-sm" />
                        </span>
                        <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Specialbestillinger</span>
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={() => scrollToSection('ansvar')}
                        className={`text-left w-full ${activeSection === 'ansvar' ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'} flex items-center group transition-all duration-300 ease-in-out`}
                      >
                        <span className={`${activeSection === 'ansvar' ? 'bg-blue-100' : 'bg-blue-50'} text-blue-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-blue-100 transition-all duration-300 ease-in-out shadow-sm`}>
                          <FaExclamationCircle className="text-sm" />
                        </span>
                        <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Ansvarsbegrænsning</span>
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={() => scrollToSection('kontakt')}
                        className={`text-left w-full ${activeSection === 'kontakt' ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'} flex items-center group transition-all duration-300 ease-in-out`}
                      >
                        <span className={`${activeSection === 'kontakt' ? 'bg-blue-100' : 'bg-blue-50'} text-blue-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-blue-100 transition-all duration-300 ease-in-out shadow-sm`}>
                          <FaEnvelope className="text-sm" />
                        </span>
                        <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Kontakt</span>
                      </button>
                    </li>
                  </ul>
                  
                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <Link href="/privatlivspolitik" className="group bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 p-4 rounded-xl flex items-center transition-all duration-300 border border-blue-100">
                      <div className="bg-white shadow-sm rounded-lg p-2 mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <span className="text-sm text-blue-700 font-medium">Se også vores</span>
                        <p className="text-blue-900 font-semibold">Privatlivspolitik</p>
                      </div>
                      <FaArrowRight className="text-blue-400 group-hover:text-blue-600 group-hover:translate-x-1.5 transition-all duration-300 ease-in-out" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Main content */}
            <div className="flex-1">
              <div id="generelt" className="relative mb-8">
                <div className="absolute left-0 top-6 h-16 w-1.5 bg-blue-400 rounded-r-full z-10"></div>
                <div className="backdrop-blur-md bg-white/70 border border-blue-100 rounded-3xl shadow-2xl px-10 py-12 md:px-16 md:py-14 transition-all duration-300">
                  <div className="flex items-center gap-5 mb-8">
                    <div className="bg-blue-100 text-blue-700 rounded-2xl w-16 h-16 flex items-center justify-center shadow-lg">
                      <FaGavel className="text-3xl" />
                    </div>
                    <h2 className="text-4xl font-extrabold text-blue-900 tracking-tight">Generelle vilkår</h2>
                  </div>
                  <div className="text-gray-700 space-y-6 leading-relaxed text-lg">
                    <p>
                      MCDevHub ("Vi", "os") stiller følgende vilkår for din adgang til og brug af vores website, apps, plugins og andre produkter ("Tjenesterne"). Ved at benytte Tjenesterne bekræfter du, at du har læst, forstået og accepteret disse vilkår.
                    </p>
                    <p><strong>1. Accept af vilkår:</strong> Du accepterer at overholde gældende lovgivning, respektere tredjeparts rettigheder og indhente nødvendigt samtykke, hvis du er under 18 år.</p>
                    <p><strong>2. Ændringer:</strong> Vi forbeholder os ret til at opdatere vilkårene til enhver tid. Ændringer offentliggøres med en revisionsdato på vores hjemmeside, og din fortsatte brug af Tjenesterne efter ikrafttrædelse udgør din accept af de opdaterede vilkår.</p>
                    <p><strong>3. Adgang og licens:</strong> Vi tildeler dig en begrænset, ikke-eksklusiv, ikke-overdragelig licens til at bruge Tjenesterne til personligt brug i overensstemmelse med disse vilkår. Du må ikke kopiere, distribuere, reverse-engineere eller videregive Tjenesterne uden skriftlig tilladelse.</p>
                    <p><strong>4. Ansvarsfraskrivelse:</strong> Tjenesterne leveres "som de er" uden garantier af nogen art. Vi fraskriver os ethvert ansvar for direkte, indirekte eller følgeskader som følge af brugen af Tjenesterne.</p>
                    <p className="text-blue-800 font-semibold">Se venligst de følgende sektioner for detaljer om licensbetingelser, ansvarsbegrænsninger og support.</p>
                  </div>
                </div>
              </div>

              <div id="licens" className="relative mb-8">
                <div className="absolute left-0 top-6 h-16 w-1.5 bg-blue-400 rounded-r-full z-10"></div>
                <div className="backdrop-blur-md bg-white/70 border border-blue-100 rounded-3xl shadow-2xl px-10 py-12 md:px-16 md:py-14 transition-all duration-300">
                  <div className="flex items-center gap-5 mb-8">
                    <div className="bg-blue-100 text-blue-700 rounded-2xl w-16 h-16 flex items-center justify-center shadow-lg">
                      <FaKey className="text-3xl" />
                    </div>
                    <h2 className="text-4xl font-extrabold text-blue-900 tracking-tight">Licensbetingelser for plugins, skripts og builds</h2>
                  </div>
                  <div className="text-gray-700 space-y-6 leading-relaxed text-lg">
                    <p>
                      Vi giver dig en begrænset, ikke-eksklusiv licens til at installere og bruge MCDevHub‑produkter på servere, som du ejer eller administrerer. Licensen gælder kun til personligt, ikke-kommercielt brug, medmindre andet er aftalt skriftligt.
                    </p>
                    <ul className="list-disc list-inside space-y-3 ml-4">
                      <li>Videresalg, distribution, sublicensiering eller overførsel af licensen er forbudt uden forudgående skriftlig tilladelse.</li>
                      <li>Reverse engineering, dekompilering eller anden form for kildekodeudtræk er ikke tilladt.</li>
                      <li>Enhver kommerciel hostejning eller udlejning uden skriftlig aftale er forbudt.</li>
                    </ul>
                    <p><strong>Varighed og ophævelse:</strong> Licensen gælder, indtil den ophæves ved misligholdelse af disse vilkår eller ved opsigelse som beskrevet i Ansvarsbegrænsning.</p>
                  </div>
                </div>
              </div>

              <div id="brug" className="relative mb-8">
                <div className="absolute left-0 top-6 h-16 w-1.5 bg-amber-400 rounded-r-full z-10"></div>
                <div className="backdrop-blur-md bg-white/70 border border-amber-100 rounded-3xl shadow-md px-10 py-12 md:px-16 md:py-14 transition-all duration-300">
                  <div className="flex items-center gap-5 mb-8">
                    <div className="bg-amber-100 text-amber-700 rounded-2xl w-16 h-16 flex items-center justify-center shadow-lg">
                      <FaExclamationTriangle className="text-3xl" />
                    </div>
                    <h2 className="text-4xl font-extrabold text-amber-900 tracking-tight">Begrænset brug</h2>
                  </div>
                  <div className="text-gray-700 space-y-7 leading-relaxed text-lg">
                    <p>
                      MCDevHub‑produkter må kun anvendes under følgende betingelser:
                    </p>
                    <ul className="list-disc list-inside space-y-3 ml-4">
                      <li>Brug kun på servere, som du ejer eller administrativt kontrollerer.</li>
                      <li>Ikke i strid med Mojangs EULA, gældende lovgivning eller tredjeparts rettigheder.</li>
                      <li>Ikke til skadevoldende aktiviteter eller forstyrrelse af netværk eller servere.</li>
                      <li>Indsamling af personoplysninger kræver eksplicit samtykke fra de berørte personer.</li>
                      <li>Enhver ulovlig eller uetisk aktivitet er forbudt.</li>
                    </ul>
                    <p className="text-amber-800 font-medium">Overtrædelse af disse begrænsninger kan føre til ophævelse af licens og retslige skridt.</p>
                  </div>
                </div>
              </div>

              <div id="support" className="relative mb-8">
                <div className="absolute left-0 top-6 h-16 w-1.5 bg-blue-400 rounded-r-full z-10"></div>
                <div className="backdrop-blur-md bg-white/70 border border-blue-100 rounded-3xl shadow-2xl px-10 py-12 md:px-16 md:py-14 transition-all duration-300">
                  <div className="flex items-center gap-5 mb-8">
                    <div className="bg-blue-100 text-blue-700 rounded-2xl w-16 h-16 flex items-center justify-center shadow-lg">
                      <FaHeadset className="text-3xl" />
                    </div>
                    <h2 className="text-4xl font-extrabold text-blue-900 tracking-tight">Support og opdateringer</h2>
                  </div>
                  <div className="text-gray-700 space-y-6 leading-relaxed text-lg">
                    <p><strong>Supportperiode:</strong> Vi tilbyder gratis fejlrettelser og sikkerhedsopdateringer i 90 dage fra leveringsdato.</p>
                    <p><strong>Opdateringer:</strong> Væsentlige opdateringer, der tilføjer nye funktioner, leveres løbende uden beregning i supportperioden.</p>
                    <p><strong>Udvidet support:</strong> Kan købes som tillægsydelse. Kontakt os for at få et tilbud.</p>
                    <p><strong>Kontakt:</strong> Send henvendelser til <a href="mailto:<EMAIL>" className="text-blue-600 underline"><EMAIL></a> og angiv dit licensnummer.</p>
                  </div>
                </div>
              </div>

              <div id="special" className="relative mb-8">
                <div className="absolute left-0 top-6 h-16 w-1.5 bg-purple-400 rounded-r-full z-10"></div>
                <div className="backdrop-blur-md bg-white/80 border border-purple-100 rounded-3xl shadow-md px-10 py-10 md:px-14 md:py-12 transition-all duration-300">
                  <div className="flex items-center gap-5 mb-6">
                    <div className="bg-purple-100 text-purple-700 rounded-2xl w-14 h-14 flex items-center justify-center shadow-lg">
                      <FaTasks className="text-2xl" />
                    </div>
                    <h2 className="text-3xl font-extrabold text-purple-900 tracking-tight">Specialbestillinger</h2>
                  </div>
                  <div className="text-gray-700 space-y-7 leading-relaxed text-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      {/* Refundering */}
                      <div className="bg-white/80 p-7 rounded-xl shadow-md border border-purple-100 flex flex-col hover:shadow-lg transition-shadow duration-300">
                        <div className="mb-4 p-3 bg-purple-50 rounded-lg w-14 h-14 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <h3 className="font-medium text-purple-900 mb-2 text-lg">Ingen refundering</h3>
                        <p className="text-gray-600 text-base flex-grow">
                          Når projektet er i gang, kan betaling ikke refunderes.
                        </p>
                      </div>
                      {/* Mindre ændringer */}
                      <div className="bg-white/80 p-7 rounded-xl shadow-md border border-purple-100 flex flex-col hover:shadow-lg transition-shadow duration-300">
                        <div className="mb-4 p-3 bg-purple-50 rounded-lg w-14 h-14 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </div>
                        <h3 className="font-medium text-purple-900 mb-2 text-lg">Små justeringer</h3>
                        <p className="text-gray-600 text-base flex-grow">
                          Vi laver små tilpasninger uden ekstra omkostninger.
                        </p>
                      </div>
                      {/* Større ændringer */}
                      <div className="bg-white/80 p-7 rounded-xl shadow-md border border-purple-100 flex flex-col hover:shadow-lg transition-shadow duration-300">
                        <div className="mb-4 p-3 bg-purple-50 rounded-lg w-14 h-14 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M6.938 20h10.124c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 17c-.77 1.333.192 3 1.732 3z" />
                          </svg>
                        </div>
                        <h3 className="font-medium text-purple-900 mb-2 text-lg">Store ændringer</h3>
                        <p className="text-gray-600 text-base flex-grow">
                          Store ændringer efter opstart kan medføre ekstra omkostninger.
                        </p>
                      </div>
                      {/* Rettigheder */}
                      <div className="bg-white/80 p-7 rounded-xl shadow-md border border-purple-100 flex flex-col hover:shadow-lg transition-shadow duration-300">
                        <div className="mb-4 p-3 bg-purple-50 rounded-lg w-14 h-14 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                          </svg>
                        </div>
                        <h3 className="font-medium text-purple-900 mb-2 text-lg">Ophavsret</h3>
                        <p className="text-gray-600 text-base flex-grow">
                          Vi ejer koden; du får lov til at bruge den, medmindre andet aftales.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div id="ansvar" className="relative mb-8">
                <div className="absolute left-0 top-6 h-16 w-1.5 bg-rose-400 rounded-r-full z-10"></div>
                <div className="backdrop-blur-md bg-white/70 border border-rose-100 rounded-3xl shadow-md px-10 py-12 md:px-16 md:py-14 transition-all duration-300">
                  <div className="flex items-center gap-5 mb-8">
                    <div className="bg-rose-100 text-rose-700 rounded-2xl w-16 h-16 flex items-center justify-center shadow-lg">
                      <FaExclamationCircle className="text-3xl" />
                    </div>
                    <h2 className="text-4xl font-extrabold text-rose-900 tracking-tight">Ansvarsbegrænsning</h2>
                  </div>
                  <div className="text-gray-700 space-y-6 leading-relaxed text-lg">
                    <p>
                      MCDevHub leverer Tjenesterne "som de er", uden nogen form for garanti. I videst muligt omfang tilladt ved lov, fraskriver vi os ethvert ansvar for:
                    </p>
                    <ul className="list-disc list-inside space-y-3 ml-4">
                      <li>Direkte eller indirekte skader, herunder tab af data eller indtægter.</li>
                      <li>Driftsstop, hacking eller sikkerhedsbrud.</li>
                      <li>Tredjepartssoftware eller -plugins, du bruger sammen med vores produkter.</li>
                    </ul>
                    <p><strong>Ansvarsgrænse:</strong> Vores maksimale ansvar kan ikke overstige det beløb, du har betalt for det pågældende produkt.</p>
                  </div>
                  <p className="text-rose-800 font-medium mt-6">Brug af Tjenesterne sker på eget ansvar.</p>
                </div>
              </div>

              <div id="kontakt" className="relative mb-8">
                <div className="absolute left-0 top-0 w-full h-1 bg-gradient-to-r from-sky-400 to-blue-400 rounded-t-2xl"></div>
                <div className="bg-white border border-sky-100 rounded-3xl shadow-lg overflow-hidden">
                  <div className="flex items-center gap-5 px-8 pt-8 pb-4">
                    <div className="bg-sky-100 rounded-2xl w-16 h-16 flex items-center justify-center shadow-md">
                      <FaEnvelope className="text-3xl text-sky-600" />
                    </div>
                    <h2 className="text-3xl md:text-4xl font-extrabold text-gray-900 tracking-tight">Kontakt</h2>
                  </div>
                  <div className="px-6 pb-8">
                    <div className="bg-gradient-to-br from-sky-50 to-blue-50 border border-sky-100 rounded-2xl shadow p-6 space-y-4">
                      <p className="text-gray-800 text-base md:text-lg"><strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-sky-600 underline"><EMAIL></a></p>
                      <p className="text-gray-800 text-base md:text-lg"><strong>Kontaktformular:</strong> Brug vores <Link href="/kontakt" className="text-sky-600 underline">kontaktside</Link>.</p>
                      <p className="text-gray-800 text-base md:text-lg"><strong>Åbningstider:</strong> -</p>
                      <p className="text-gray-600 text-sm md:text-base"><strong>Svarfrist:</strong> Vi besvarer henvendelser inden for 2 arbejdsdage.</p>
                    </div>
                  </div>
                </div>
              </div>
              <div id="opdatering" className="relative mb-8">
                <div className="absolute left-0 top-6 h-16 w-1.5 bg-emerald-400 rounded-r-full z-10"></div>
                <div className="backdrop-blur-md bg-white/80 border border-emerald-100 rounded-3xl shadow-lg px-10 py-10 md:px-14 md:py-12 transition-all duration-300">
                  <div className="flex items-center gap-5 mb-6">
                    <div className="bg-emerald-100 text-emerald-700 rounded-2xl w-14 h-14 flex items-center justify-center shadow-lg">
                      <FaClock className="text-2xl" />
                    </div>
                    <h2 className="text-3xl font-extrabold text-emerald-900 tracking-tight">Seneste opdatering</h2>
                  </div>
                  <div className="text-gray-700 leading-relaxed">
                    <div className="flex flex-col sm:flex-row items-center p-6 bg-emerald-50 rounded-xl border border-emerald-100 shadow-sm">
                      <div className="bg-white p-4 rounded-xl shadow-sm mb-4 sm:mb-0 sm:mr-6">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div className="text-center sm:text-left">
                        <p className="text-lg mb-2">
                          Disse vilkår blev sidst opdateret den <span className="font-semibold text-emerald-900">17. april 2025</span>.
                        </p>
                        <p className="text-gray-600">
                          Tidligere versioner kan fås ved at{' '}
                          <Link href="/kontakt" className="text-emerald-600 hover:text-emerald-800 font-medium underline underline-offset-4 decoration-emerald-200 hover:decoration-emerald-500 transition-all duration-300">
                            kontakte os
                          </Link>.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom navigation */}
          <footer className="w-full flex items-center justify-center mt-16 mb-4">
            <div className="backdrop-blur-md bg-white/80 border border-gray-100 rounded-2xl shadow-lg px-8 py-5 flex flex-wrap gap-4 md:gap-8 items-center justify-center">
              <Link href="/" className="group flex items-center gap-2 px-5 py-2 rounded-full bg-gray-50 hover:bg-blue-100 text-blue-700 font-medium shadow transition-all duration-200">
                <FaArrowLeft className="text-lg transform transition-all duration-300 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:-translate-x-1.5" strokeWidth={1.5} />
                Tilbage til forsiden
              </Link>
              <Link href="/privatlivspolitik" className="flex items-center gap-2 px-5 py-2 rounded-full bg-gray-50 hover:bg-emerald-100 text-emerald-700 font-medium shadow transition-all duration-200">
                <FaShieldAlt className="text-lg" />
                Privatlivspolitik
              </Link>
              <Link href="/kontakt" className="flex items-center gap-2 px-5 py-2 rounded-full bg-gray-50 hover:bg-sky-100 text-sky-700 font-medium shadow transition-all duration-200">
                <FaEnvelope className="text-lg" />
                Kontakt
              </Link>
            </div>
          </footer>
        </div>
      </section>

      {/* Scroll to top button */}
      <button 
        onClick={scrollToTop}
        className={`fixed bottom-8 right-8 bg-blue-600 text-white p-4 rounded-full shadow-lg transition-all duration-300 z-40 ${showScrollTop ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10 pointer-events-none'}`}
        aria-label="Scroll to top"
      >
        <FaArrowUp />
      </button>
    </main>
  );
}