import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';
import { validateProfileAction, sanitizeProfileInput } from '@/lib/profilePermissions';

export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params;
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Validate that the user can edit this profile
    const validation = await validateProfileAction(userId, 'edit');
    if (!validation.allowed) {
      return NextResponse.json(
        { error: validation.reason || 'Ikke autoriseret' },
        { status: 403 }
      );
    }

    // Get the current user from NextAuth
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and sanitize the request body
    const body = await request.json();
    const sanitizedInput = sanitizeProfileInput(body);

    if (Object.keys(sanitizedInput).length === 0) {
      return NextResponse.json(
        { error: 'Ingen gyldige felter at opdatere' },
        { status: 400 }
      );
    }

    // Connect to database
    const { db } = await connectToDatabase();
    
    // Check if user exists in adminusers collection (freelancer/admin)
    const adminUser = await db.collection('adminusers').findOne({
      discordUserId: userId
    });

    let updateResult;
    
    if (adminUser) {
      // Update adminusers collection
      updateResult = await db.collection('adminusers').updateOne(
        { discordUserId: userId },
        { 
          $set: {
            ...sanitizedInput,
            updatedAt: new Date()
          }
        }
      );
    } else {
      // Update regular users collection
      updateResult = await db.collection('users').updateOne(
        { discordUserId: userId },
        { 
          $set: {
            ...sanitizedInput,
            updatedAt: new Date()
          }
        },
        { upsert: true } // Create if doesn't exist
      );
    }

    if (updateResult.matchedCount === 0 && updateResult.upsertedCount === 0) {
      return NextResponse.json(
        { error: 'Bruger ikke fundet' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Profil opdateret succesfuldt',
      updatedFields: Object.keys(sanitizedInput)
    });
    
  } catch (error) {
    console.error('Error updating user profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params;
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Validate that the user can delete this profile
    const validation = await validateProfileAction(userId, 'delete');
    if (!validation.allowed) {
      return NextResponse.json(
        { error: validation.reason || 'Ikke autoriseret' },
        { status: 403 }
      );
    }

    // Get the current user from NextAuth
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    const { db } = await connectToDatabase();
    
    // Note: We don't actually delete user accounts, just mark them as deleted
    // This preserves data integrity for purchases, transactions, etc.
    
    const updateData = {
      deleted: true,
      deletedAt: new Date(),
      deletedBy: session.user.id
    };

    // Try to update in adminusers collection first
    let deleteResult = await db.collection('adminusers').updateOne(
      { discordUserId: userId },
      { $set: updateData }
    );

    // If not found in adminusers, try regular users collection
    if (deleteResult.matchedCount === 0) {
      deleteResult = await db.collection('users').updateOne(
        { discordUserId: userId },
        { $set: updateData }
      );
    }

    if (deleteResult.matchedCount === 0) {
      return NextResponse.json(
        { error: 'Bruger ikke fundet' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Profil markeret som slettet'
    });
    
  } catch (error) {
    console.error('Error deleting user profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
