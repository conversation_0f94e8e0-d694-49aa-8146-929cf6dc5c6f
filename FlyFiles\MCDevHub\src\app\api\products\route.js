import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

export async function GET(request) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const createdBy = url.searchParams.get('createdBy');
    const includeAll = url.searchParams.get('includeAll') === 'true';
    
    // Connect to the database
    const { db } = await connectToDatabase();
    
    // Build query based on parameters
    const query = {};
    
    if (createdBy) {
      query.createdBy = createdBy;
    }
    
    // Only include approved or active products by default, unless includeAll is specified
    if (!includeAll) {
      query.status = { $in: ['approved', 'active'] };
    }
    
    // First, check if the collection is empty
    const collectionCount = await db.collection('products').countDocuments({});
    
    // Fetch products
    const products = await db.collection('products')
      .find(query)
      .sort({ createdAt: -1 })
      .toArray();
    
    
    // If no products found, return empty array
    if (products.length === 0) {
      return NextResponse.json({ products: [] });
    }
    
    return NextResponse.json({
      products: products
    });
  } catch (error) {
    return NextResponse.json(
      { message: 'Der opstod en fejl ved hentning af produkter', error: error.message },
      { status: 500 }
    );
  }
} 