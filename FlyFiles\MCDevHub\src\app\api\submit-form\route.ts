import { NextResponse } from 'next/server';
import { MongoClient } from 'mongodb';

if (!process.env.MONGODB_URI) {
  throw new Error('Please add your Mongo URI to .env.local');
}

const uri = process.env.MONGODB_URI;
const options = {};

let client;
let clientPromise: Promise<MongoClient>;

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  let globalWithMongo = global as typeof globalThis & {
    _mongoClientPromise?: Promise<MongoClient>;
  };

  if (!globalWithMongo._mongoClientPromise) {
    client = new MongoClient(uri, options);
    globalWithMongo._mongoClientPromise = client.connect();
  }
  clientPromise = globalWithMongo._mongoClientPromise;
} else {
  // In production mode, it's best to not use a global variable.
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

export async function POST(request: Request) {
  try {
    console.log('Received form submission request');
    const body = await request.json();
    console.log('Request body:', body);

    console.log('Connecting to MongoDB...');
    const client = await clientPromise;
    console.log('Connected to MongoDB');

    console.log('Accessing database...');
    const db = client.db("codehub");
    console.log('Database accessed');

    console.log('Accessing collection...');
    const collection = db.collection("form_submissions");
    console.log('Collection accessed');

    // Add timestamp to the submission
    const submission = {
      ...body,
      submittedAt: new Date().toISOString(),
    };
    console.log('Prepared submission:', submission);

    console.log('Inserting document...');
    const result = await collection.insertOne(submission);
    console.log('Document inserted:', result);

    return NextResponse.json({ 
      success: true, 
      message: 'Form submitted successfully',
      id: result.insertedId 
    });
  } catch (error) {
    console.error('Detailed error in form submission:', error);
    // Log the full error object
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    return NextResponse.json(
      { 
        success: false, 
        message: 'Error submitting form',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 