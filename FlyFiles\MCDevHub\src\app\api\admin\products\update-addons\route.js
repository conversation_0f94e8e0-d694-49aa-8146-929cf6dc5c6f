import { NextResponse } from 'next/server';
import { verifyAdminToken } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function POST(request) {
  try {
    // Verify the admin user is logged in
    const admin = await verifyAdminToken();
    
    if (!admin) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }
    
    // Only allow freelancers to update their products
    if (admin.admintype !== 'Freelancer') {
      return NextResponse.json({ message: 'Only freelancers can update products' }, { status: 403 });
    }
    
    // Get request body
    const body = await request.json();
    const { productId, addons } = body;
    
    if (!productId || !addons) {
      return NextResponse.json({ message: 'Missing required fields' }, { status: 400 });
    }
    
    // Connect to database
    const { db } = await connectToDatabase();
    
    // Check if the product exists and belongs to this admin
    const product = await db.collection('products').findOne({ 
      _id: new ObjectId(productId),
      createdBy: admin.username 
    });
    
    if (!product) {
      return NextResponse.json({ message: 'Product not found or you do not have permission to update it' }, { status: 404 });
    }
    
    // Validate addons array
    if (!Array.isArray(addons)) {
      return NextResponse.json({ message: 'Addons must be an array' }, { status: 400 });
    }
    
    // Validate each addon in the array
    for (const addon of addons) {
      if (!addon.name || typeof addon.name !== 'string') {
        return NextResponse.json({ message: 'Each addon must have a name property' }, { status: 400 });
      }
      
      if (addon.url && typeof addon.url !== 'string') {
        return NextResponse.json({ message: 'Addon URL must be a string' }, { status: 400 });
      }
      
      if (addon.version && typeof addon.version !== 'string') {
        return NextResponse.json({ message: 'Addon version must be a string' }, { status: 400 });
      }
      
      if (addon.required !== undefined && typeof addon.required !== 'boolean') {
        return NextResponse.json({ message: 'Addon required must be a boolean' }, { status: 400 });
      }
    }
    
    // Update the product with new addons
    const result = await db.collection('products').updateOne(
      { _id: new ObjectId(productId) },
      { $set: { 
          addons: addons,
          updatedAt: new Date()
        } 
      }
    );
    
    if (result.matchedCount === 0) {
      return NextResponse.json({ message: 'Product not found' }, { status: 404 });
    }
    
    if (result.modifiedCount === 0) {
      return NextResponse.json({ message: 'No changes made to the product' }, { status: 200 });
    }
    
    // Return success response
    return NextResponse.json({ 
      message: 'Product addons updated successfully',
      success: true
    });
    
  } catch (error) {
    console.error('Error updating product addons:', error);
    return NextResponse.json({ message: 'Server error', error: error.message }, { status: 500 });
  }
} 