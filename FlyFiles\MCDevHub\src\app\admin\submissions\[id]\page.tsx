'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { FaDiscord } from 'react-icons/fa';

interface Submission {
  _id: string;
  formType: string;
  name?: string;
  email?: string;
  subject?: string;
  message?: string;
  discordUsername?: string;
  platformType?: string;
  platformLink?: string;
  followers?: string;
  serverInfo?: string;
  pluginInterest?: string;
  promotionPlans?: string;
  serverType?: string;
  orderType?: string;
  description?: string;
  budget?: string;
  deadline?: string;
  submittedAt: string;
  isRead?: boolean;
  readBy?: string;
  readAt?: string;
  notes?: string;
}

interface AdminUser {
  username: string;
  admintype: string;
  allowedcases: string;
}

export default function SubmissionDetails() {
  const params = useParams();
  const router = useRouter();
  const [submission, setSubmission] = useState<Submission | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [admin, setAdmin] = useState<AdminUser | null>(null);
  const [allowedTypes, setAllowedTypes] = useState<string[]>([]);
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [notes, setNotes] = useState<string>('');
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const [updateMessage, setUpdateMessage] = useState<string | null>(null);
  const [notesDisabled, setNotesDisabled] = useState<boolean>(false);

  useEffect(() => {
    // Get admin info from cookie
    const getAdminInfo = async () => {
      try {
        // Fetch admin info from server
        const response = await fetch('/api/admin/me');
        
        if (!response.ok) {
          if (response.status === 401) {
            // Unauthorized, redirect to login
            router.push('/admin/login');
            return;
          }
          throw new Error('Kunne ikke hente admin information');
        }
        
        const data = await response.json();
        setAdmin(data.user);
        
        // Parse allowed case types
        if (data.user.allowedcases) {
          const types = data.user.allowedcases.split(',').map((type: string) => type.trim());
          setAllowedTypes(types);
        }
      } catch (error) {
        console.error('Error fetching admin info:', error);
        setError('Kunne ikke hente admin information');
      }
    };

    getAdminInfo();
  }, [router]);

  useEffect(() => {
    if (admin) {
      fetchSubmission();
    }
  }, [admin, params.id]);

  useEffect(() => {
    if (loading) {
      document.body.style.overflow = 'hidden';
      window.scrollTo(0, 0);
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [loading]);

  // Helper function to format notes with Discord links
  const formatNotesWithLinks = (notesText: string) => {
    if (!notesText) return '';
    
    // Look for the pattern "- username||discordId" at the end of notes
    // This regex matches the last line that starts with "- " and captures the username and discordId
    const signatureRegex = /- ([^|]+)\|\|([^|\s]+)$/;
    const match = notesText.match(signatureRegex);
    
    if (match && match[1] && match[2]) {
      // We found a username and discordId
      const username = match[1];
      const discordId = match[2];
      
      // Replace the pattern with a linked username
      return notesText.replace(
        signatureRegex,
        `- <a href="https://discord.com/users/${discordId}" target="_blank" class="text-blue-600">${username}</a>`
      );
    }
    
    // If no match found, return the original text
    return notesText;
  };

  const fetchSubmission = async () => {
    try {
      const response = await fetch(`/api/submissions/${params.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch submission details');
      }
      const data = await response.json();
      setSubmission(data);
      setNotes(data.notes || '');
      
      // Disable notes editing if they already exist and are not empty
      if (data.notes && data.notes.trim() !== '') {
        setNotesDisabled(true);
      } else {
        setNotesDisabled(false);
      }

      // Check if admin has permission to view this submission type
      if (data.formType) {
        const hasAccess = allowedTypes.includes(data.formType) || allowedTypes.length === 0;
        setHasPermission(hasAccess);
        
        if (!hasAccess) {
          setError('Du har ikke tilladelse til at se denne indsendelse');
        }
      }
    } catch (error) {
      console.error('Error fetching submission:', error);
      setError('Failed to load submission details');
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsRead = async () => {
    if (!submission) return;
    
    try {
      setIsUpdating(true);
      setUpdateMessage(null);
      
      const response = await fetch(`/api/submissions/${params.id}/update`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isRead: true
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update submission');
      }
      
      const data = await response.json();
      
      // Update local state
      setSubmission(prev => prev ? { 
        ...prev, 
        isRead: true, 
        readBy: admin?.username,
        readAt: new Date().toISOString()
      } : null);
      
      setUpdateMessage('Markeret som læst');
      
      // Clear message after 3 seconds
      setTimeout(() => {
        setUpdateMessage(null);
      }, 3000);
    } catch (error) {
      console.error('Error updating submission:', error);
      setUpdateMessage('Fejl ved opdatering');
    } finally {
      setIsUpdating(false);
    }
  };
  
  const handleSaveNotes = async () => {
    if (!submission) return;
    
    try {
      setIsUpdating(true);
      setUpdateMessage(null);
      
      const response = await fetch(`/api/submissions/${params.id}/update`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update notes');
      }
      
      const data = await response.json();
      
      // The backend adds the admin signature, so we need to update with the returned data
      const updatedNotes = data.updates.notes;
      
      // Update local state
      setSubmission(prev => prev ? { ...prev, notes: updatedNotes } : null);
      setNotes(updatedNotes);
      
      // Disable notes editing after saving successfully
      setNotesDisabled(true);
      
      setUpdateMessage('Noter gemt');
      
      // Clear message after 3 seconds
      setTimeout(() => {
        setUpdateMessage(null);
      }, 3000);
    } catch (error) {
      console.error('Error updating notes:', error);
      setUpdateMessage(error instanceof Error ? error.message : 'Fejl ved gemning af noter');
    } finally {
      setIsUpdating(false);
    }
  };

  const getFormTypeLabel = (type: string) => {
    switch (type) {
      case 'partner':
        return 'Partner Ansøgning';
      case 'contact':
        return 'Kontakt Formular';
      case 'custom-order':
        return 'Custom Order';
      default:
        return type;
    }
  };

  const getFormTypeColor = (type: string) => {
    switch (type) {
      case 'partner':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'contact':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'custom-order':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('da-DK', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90 backdrop-blur-sm">
        <div className="flex flex-col items-center max-w-sm mx-auto p-8 rounded-xl bg-white shadow-lg border border-gray-100">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mb-6" style={{ animationDuration: '0.4s' }}></div>
          <div className="text-blue-700 text-lg font-medium">Indlæser submission...</div>
          <p className="text-gray-500 text-sm mt-2 text-center">Vent venligst mens vi henter submissionsdetaljer</p>
        </div>
      </div>
    );
  }

  if (error || !submission) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-5xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-red-700">
            <h2 className="text-xl font-semibold mb-2">Fejl</h2>
            <p>{error || 'Indsendelse ikke fundet'}</p>
            <div className="mt-4">
              <Link 
                href="/admin/dashboard"
                className="text-blue-600 hover:text-blue-900 flex items-center group inline-block"
              >
                <svg className="w-5 h-5 mr-2 transform group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Tilbage til Dashboard
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!hasPermission) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-5xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-red-700">
            <h2 className="text-xl font-semibold mb-2">Adgang nægtet</h2>
            <p>Du har ikke tilladelse til at se denne type af indsendelse.</p>
            <div className="mt-4">
              <Link 
                href="/admin/dashboard"
                className="text-blue-600 hover:text-blue-900 flex items-center group inline-block"
              >
                <svg className="w-5 h-5 mr-2 transform group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Tilbage til Dashboard
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 pt-16 sm:pt-20">
      <div className="max-w-6xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Header Section with status banner */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
            <Link 
              href="/admin/dashboard"
              className="inline-flex items-center text-gray-600 hover:text-blue-600 transition-colors duration-200 group"
            >
              <svg className="w-5 h-5 mr-2 transform group-hover:-translate-x-1 transition-transform duration-200" 
                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              <span className="font-medium">Tilbage til Dashboard</span>
            </Link>
            <div className="flex items-center gap-2">
              <span className={`px-4 py-2 rounded-full text-sm font-medium border shadow-sm ${getFormTypeColor(submission.formType)}`}>
                {getFormTypeLabel(submission.formType)}
              </span>
              {!submission.isRead ? (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 border border-amber-200">
                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Ny
                </span>
              ) : (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Læst
                </span>
              )}
            </div>
          </div>
          
          <div className="bg-white rounded-2xl shadow-md border border-gray-200 overflow-hidden">
            <div className="p-6 sm:p-8 border-b border-gray-100">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3">Detaljer for Indsendelse</h1>
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-gray-500">
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span className="cursor-default">{formatDateTime(submission.submittedAt)}</span>
                </div>
                {submission.isRead && submission.readAt && (
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <span className="cursor-default">Læst af <span className="font-medium text-gray-700">{submission.readBy}</span> den {formatDateTime(submission.readAt)}</span>
                  </div>
                )}
              </div>
            </div>
            
            {!submission.isRead && (
              <div className="px-6 sm:px-8 py-4 bg-blue-50 border-b border-blue-100">
                <button
                  onClick={handleMarkAsRead}
                  disabled={isUpdating}
                  className="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-blue-600 text-white shadow-sm hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  {isUpdating ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Markerer...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      Marker som læst
                    </>
                  )}
                </button>
                <span className="ml-3 text-sm text-blue-600">Markér denne indsendelse som læst for at indikere, at du har set den</span>
              </div>
            )}
          </div>
        </div>

        {/* Notes Section */}
        <div className="mb-8">
          <div className="bg-white rounded-2xl shadow-md border border-gray-200 overflow-hidden">
            <div className="p-6 sm:p-8">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-gray-900 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  <span className="cursor-default">Freelance Noter</span>
                </h2>
              </div>
              
              {submission.isRead && submission.readAt && (
                <div className="mb-5 p-3 bg-green-50 border border-green-200 rounded-lg text-green-800">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>
                      Markeret som læst af <strong>{submission.readBy}</strong> den {formatDateTime(submission.readAt)}
                    </span>
                  </div>
                </div>
              )}
              
              <div className="mb-5">
                {notesDisabled ? (
                  <div 
                    className="w-full min-h-[8rem] p-4 border border-gray-300 rounded-lg bg-gray-50 text-gray-700 overflow-auto whitespace-pre-wrap"
                    dangerouslySetInnerHTML={{ __html: formatNotesWithLinks(notes) }}
                  />
                ) : (
                  <div className="space-y-2">
                    <label htmlFor="notes" className="block text-sm font-medium text-gray-700 cursor-default">
                      Tilføj note til denne indsendelse
                    </label>
                    <textarea
                      id="notes"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Skriv hvad du har gjort ved denne indsendelse..."
                      disabled={notesDisabled}
                      className="w-full min-h-[8rem] p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm resize-y transition-colors"
                    ></textarea>
                  </div>
                )}
                
                {notesDisabled && (
                  <div className="flex items-center mt-3 text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <p className="text-sm">Noter kan ikke ændres efter de er gemt</p>
                  </div>
                )}
              </div>
              
              <div className="flex flex-col sm:flex-row items-center sm:justify-between gap-3">
                <button
                  onClick={handleSaveNotes}
                  disabled={isUpdating || notesDisabled || notes.length < 3}
                  className={`w-full sm:w-auto px-6 py-3 rounded-lg font-medium transition-all flex items-center justify-center ${
                    isUpdating || notesDisabled || notes.length < 3
                      ? 'bg-gray-300 text-gray-500 cursor-default'
                      : 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm hover:shadow'
                  }`}
                >
                  {isUpdating ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Gemmer...
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                      </svg>
                      Gem Noter
                    </>
                  )}
                </button>
                {updateMessage && (
                  <span className="px-4 py-2 bg-green-100 text-green-800 rounded-lg border border-green-200 flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    {updateMessage}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Submission Details Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Basic Information Card */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-md border border-gray-200 overflow-hidden h-full">
              <div className="p-6 border-b border-gray-100">
                <h2 className="flex items-center text-lg font-bold text-gray-900">
                  <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Kontaktinformation
                </h2>
              </div>
              <div className="p-6">
                <dl className="space-y-5">
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center cursor-default">
                      <svg className="w-4 h-4 mr-1 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      Navn
                    </dt>
                    <dd className="mt-1 text-base text-gray-900 font-medium">{submission.name || 'N/A'}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center cursor-default">
                      <svg className="w-4 h-4 mr-1 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      Email
                    </dt>
                    <dd className="mt-1 text-base text-blue-600 underline">
                      <a href={`mailto:${submission.email}`}>{submission.email || 'N/A'}</a>
                    </dd>
                  </div>
                  {submission.discordUsername && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500 flex items-center cursor-default">
                        <FaDiscord className="w-4 h-4 mr-1 text-[#5865F2]" />
                        Discord
                      </dt>
                      <dd className="mt-1 text-base text-indigo-600 font-medium">{submission.discordUsername}</dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>
          </div>

          {/* Form Specific Information */}
          <div className="lg:col-span-2">
            {submission.formType === 'partner' && (
              <div className="bg-white rounded-2xl shadow-md border border-gray-200 overflow-hidden">
                <div className="p-6 border-b border-gray-100">
                  <h2 className="flex items-center text-lg font-bold text-gray-900">
                    <svg className="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Partner Information
                  </h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-base font-medium text-gray-800 mb-3">Platform Detaljer</h3>
                      <dl className="space-y-4">
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Platform Type</dt>
                          <dd className="mt-1 text-base text-gray-900">{submission.platformType || 'N/A'}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Platform Link</dt>
                          <dd className="mt-1 text-base">
                            {submission.platformLink ? (
                              <a href={submission.platformLink} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 transition-colors flex items-center">
                                {submission.platformLink}
                                <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                              </a>
                            ) : 'N/A'}
                          </dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Følgere</dt>
                          <dd className="mt-1 text-base text-gray-900">{submission.followers || 'N/A'}</dd>
                        </div>
                      </dl>
                    </div>
                    
                    <div>
                      <h3 className="text-base font-medium text-gray-800 mb-3">Server & Interesser</h3>
                      <dl className="space-y-4">
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Server Information</dt>
                          <dd className="mt-1 text-base text-gray-900 whitespace-pre-wrap">{submission.serverInfo || 'N/A'}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Plugin Interesse</dt>
                          <dd className="mt-1 text-base text-gray-900 whitespace-pre-wrap">{submission.pluginInterest || 'N/A'}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Promotions Planer</dt>
                          <dd className="mt-1 text-base text-gray-900 whitespace-pre-wrap">{submission.promotionPlans || 'N/A'}</dd>
                        </div>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {submission.formType === 'contact' && (
              <div className="bg-white rounded-2xl shadow-md border border-gray-200 overflow-hidden">
                <div className="p-6 border-b border-gray-100">
                  <h2 className="flex items-center text-lg font-bold text-gray-900">
                    <div className="w-5 h-5 mr-2 text-blue-500 flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-full w-full" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    Kontakt Information
                  </h2>
                </div>
                <div className="p-6">
                  <dl className="space-y-5">
                    <div>
                      <dt className="text-sm font-medium text-gray-500 cursor-default flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Emne
                      </dt>
                      <dd className="mt-1 text-lg font-medium text-gray-900">{submission.subject || 'N/A'}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500 cursor-default flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                        </svg>
                        Besked
                      </dt>
                      <dd className="mt-2 p-4 bg-gray-50 border border-gray-100 rounded-lg text-gray-900 whitespace-pre-wrap">
                        {submission.message || 'N/A'}
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>
            )}

            {submission.formType === 'custom-order' && (
              <div className="bg-white rounded-2xl shadow-md border border-gray-200 overflow-hidden">
                <div className="p-6 border-b border-gray-100">
                  <h2 className="flex items-center text-lg font-bold text-gray-900">
                    <svg className="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    Custom Order Detaljer
                  </h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <dl className="space-y-4">
                        <div>
                          <dt className="text-sm font-medium text-gray-500 flex items-center">
                            <svg className="w-4 h-4 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                            </svg>
                            Server Type
                          </dt>
                          <dd className="mt-1 px-3 py-1 inline-block bg-gray-100 rounded text-gray-900 font-medium">{submission.serverType || 'N/A'}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500 flex items-center">
                            <svg className="w-4 h-4 mr-1 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            Ordre Type
                          </dt>
                          <dd className="mt-1 px-3 py-1 inline-block bg-gray-100 rounded text-gray-900 font-medium">{submission.orderType ? submission.orderType.charAt(0).toUpperCase() + submission.orderType.slice(1) : 'N/A'}</dd>
                        </div>
                      </dl>
                    </div>
                    <div>
                      <dl className="space-y-4">
                        <div>
                          <dt className="text-sm font-medium text-gray-500 flex items-center">
                            <svg className="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            Budget
                          </dt>
                          <dd className="mt-1 text-lg font-medium text-green-600">{submission.budget ? `${submission.budget} kr` : 'N/A'}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500 flex items-center">
                            <svg className="w-4 h-4 mr-1 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Deadline
                          </dt>
                          <dd className="mt-1 text-base text-gray-900">{submission.deadline || 'N/A'}</dd>
                        </div>
                      </dl>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-base font-medium text-gray-800 mb-3 flex items-center">
                      <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Projekt Beskrivelse
                    </h3>
                    <div className="p-4 bg-gray-50 border border-gray-100 rounded-lg">
                      <div className="text-gray-900 whitespace-pre-wrap">
                        {submission.description || 'Ingen beskrivelse angivet'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 