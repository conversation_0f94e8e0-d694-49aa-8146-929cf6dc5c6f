'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  <PERSON>a<PERSON><PERSON>, 
  FaCheck, 
  FaKey, 
  FaUser, 
  FaShieldAlt, 
  FaDiscord, 
  FaTimes, 
  FaArrowLeft, 
  FaInfoCircle,
  FaUserPlus
} from 'react-icons/fa';
import { motion } from 'framer-motion';

interface AdminUser {
  username: string;
  admintype: string;
  allowedcases: string;
}

export default function AddUsersPage() {
  const router = useRouter();
  const [admin, setAdmin] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    admintype: 'Freelancer',
    allowedcases: 'contact,partner,custom-order',
    discordUserId: '',
    description: ''
  });
  const [generatedLink, setGeneratedLink] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [showAccessOptions, setShowAccessOptions] = useState(false);

  useEffect(() => {
    const getAdminInfo = async () => {
      try {
        const response = await fetch('/api/admin/me');
        
        if (!response.ok) {
          if (response.status === 401) {
            router.push('/admin/login');
            return;
          }
          throw new Error('Kunne ikke hente admin information');
        }
        
        const data = await response.json();
        setAdmin(data.user);
        
        // Ensure only MyckasP can access this page
        if (data.user.username !== 'MyckasP') {
          router.push('/admin/dashboard');
        }
      } catch (error) {
        console.error('Error fetching admin info:', error);
        setError('Kunne ikke hente admin information');
        router.push('/admin/login');
      } finally {
        setLoading(false);
      }
    };

    getAdminInfo();
  }, [router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target;
    
    setFormData(prev => {
      const currentCases = prev.allowedcases.split(',').filter(c => c.trim() !== '');
      
      if (checked && !currentCases.includes(value)) {
        // Add the case
        currentCases.push(value);
      } else if (!checked && currentCases.includes(value)) {
        // Remove the case
        const index = currentCases.indexOf(value);
        currentCases.splice(index, 1);
      }
      
      return {
        ...prev,
        allowedcases: currentCases.join(',')
      };
    });
  };

  const generateLink = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.discordUserId.trim()) {
      alert('Indtast venligst et Discord bruger-ID');
      return;
    }
    
    setGenerating(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/generate-invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Kunne ikke generere invitationslink');
      }

      const data = await response.json();
      setGeneratedLink(data.inviteUrl);
    } catch (error) {
      console.error('Error generating invite link:', error);
      setError('Der opstod en fejl ved generering af invitationslink. Prøv igen senere.');
    } finally {
      setGenerating(false);
    }
  };

  const copyToClipboard = () => {
    if (generatedLink) {
      navigator.clipboard.writeText(generatedLink)
        .then(() => {
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        })
        .catch(err => {
          console.error('Failed to copy:', err);
        });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
          <p className="text-gray-600 font-medium">Indlæser admin panel...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white border border-red-200 rounded-xl p-8 shadow-lg text-red-700">
            <div className="flex items-center mb-4">
              <div className="bg-red-100 p-2 rounded-full mr-3">
                <FaTimes className="h-6 w-6 text-red-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Der opstod en fejl</h2>
            </div>
            <p className="text-gray-700 mb-6">{error}</p>
            <Link 
              href="/admin/dashboard"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors group"
            >
              <FaArrowLeft className="w-4 h-4 mr-2 transform group-hover:-translate-x-1 transition-transform duration-300" />
              Tilbage til Dashboard
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 pt-7.5">
      <div className="max-w-5xl mx-auto px-4 py-8 sm:px-6">
        <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between mb-8">
          <div className="flex items-center mb-4 lg:mb-0">
            <Link 
              href="/admin/dashboard"
              className="inline-flex items-center text-gray-600 hover:text-blue-600 transition-colors duration-200 group mr-4"
            >
              <FaArrowLeft className="w-4 h-4 mr-2 transform group-hover:-translate-x-1 transition-transform duration-300" />
              <span className="font-medium">Dashboard</span>
            </Link>
            
            <h1 className="text-2xl font-bold text-gray-900">Administrer Brugere</h1>
          </div>
          
          <div className="flex items-center bg-white px-4 py-2 rounded-lg shadow-sm border border-gray-100">
            <FaUser className="text-blue-600 mr-2" />
            <span className="text-sm text-gray-500 mr-1">Logget ind som:</span>
            <span className="font-medium text-gray-900">{admin?.username}</span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-8">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-6">
                <div className="flex items-center text-white">
                  <div className="p-2 bg-white/15 rounded-lg mr-4">
                    <FaUserPlus className="w-6 h-6 text-white" />
                  </div>
                  <h2 className="text-xl font-bold">Tilføj Ny Bruger</h2>
                </div>
              </div>
              
              <div className="p-6">
                {generatedLink ? (
                  <motion.div 
                    className="space-y-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <div className="bg-green-100 p-2 rounded-full">
                            <FaCheck className="h-5 w-5 text-green-600" />
                          </div>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-lg font-medium text-green-800">Success!</h3>
                          <div className="mt-2 text-sm text-green-700">
                            <p>Dit invitationslink er klar til at blive delt. Linket vil udløbe efter 10 minutter eller efter første brug.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 p-4 rounded-xl border border-gray-200 relative">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 font-mono text-sm text-gray-800 break-all pr-12 py-2 overflow-x-auto">
                          {generatedLink}
                        </div>
                        <button 
                          onClick={copyToClipboard}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 inline-flex items-center p-2 rounded-md bg-blue-100 hover:bg-blue-200 text-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          aria-label="Kopier link"
                        >
                          {copied ? <FaCheck className="h-5 w-5" /> : <FaCopy className="h-5 w-5" />}
                        </button>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">Opsummering af brugerindstillinger:</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="bg-white border border-gray-100 shadow-sm rounded-lg p-4">
                          <div className="text-sm font-medium text-gray-500 mb-1">Admin Type:</div>
                          <div className="flex">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-md text-sm font-medium bg-blue-100 text-blue-800">
                              {formData.admintype}
                            </span>
                          </div>
                        </div>
                        <div className="bg-white border border-gray-100 shadow-sm rounded-lg p-4">
                          <div className="text-sm font-medium text-gray-500 mb-1">Discord ID:</div>
                          <div className="flex items-center">
                            <FaDiscord className="text-indigo-500 mr-2" />
                            <span className="text-gray-800">{formData.discordUserId}</span>
                          </div>
                        </div>
                        <div className="sm:col-span-2 bg-white border border-gray-100 shadow-sm rounded-lg p-4">
                          <div className="text-sm font-medium text-gray-500 mb-1">Tilladte formulartyper:</div>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {formData.allowedcases.split(',').map((caseType) => (
                              <span key={caseType} className="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium bg-green-100 text-green-800">
                                {caseType}
                              </span>
                            ))}
                          </div>
                        </div>
                        {formData.description && (
                          <div className="sm:col-span-2 bg-white border border-gray-100 shadow-sm rounded-lg p-4">
                            <div className="text-sm font-medium text-gray-500 mb-1">Beskrivelse:</div>
                            <div className="text-gray-800 text-sm">{formData.description}</div>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex justify-center pt-4">
                      <button
                        onClick={() => setGeneratedLink(null)}
                        className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                      >
                        Generer nyt invitationslink
                      </button>
                    </div>
                  </motion.div>
                ) : (
                  <motion.form 
                    onSubmit={generateLink} 
                    className="space-y-6"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="grid grid-cols-1 gap-6">
                      <div>
                        <label htmlFor="discordUserId" className="block text-sm font-medium text-gray-700 mb-1">
                          Discord Bruger ID <span className="text-red-500">*</span>
                        </label>
                        <div className="relative mt-1 rounded-md shadow-sm">
                          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                            <FaDiscord className="h-5 w-5 text-indigo-500" />
                          </div>
                          <input
                            type="text"
                            name="discordUserId"
                            id="discordUserId"
                            value={formData.discordUserId}
                            onChange={handleChange}
                            className="block w-full rounded-lg border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                            placeholder="f.eks. 929307108002889769"
                            required
                          />
                        </div>
                        <p className="mt-1 text-xs text-gray-500 flex items-center">
                          <FaInfoCircle className="mr-1 text-gray-400" />
                          Højreklik på brugeren i Discord og vælg "Kopier ID"
                        </p>
                      </div>
                      
                      <div>
                        <div className="flex items-center justify-between">
                          <label htmlFor="admintype" className="block text-sm font-medium text-gray-700">
                            Brugertype
                          </label>
                          <button 
                            type="button"
                            onClick={() => setShowAccessOptions(!showAccessOptions)}
                            className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
                          >
                            {showAccessOptions ? 'Skjul adgangsindstillinger' : 'Vis adgangsindstillinger'}
                          </button>
                        </div>
                        <select
                          id="admintype"
                          name="admintype"
                          value={formData.admintype}
                          onChange={handleChange}
                          className="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value="Freelancer">Freelancer</option>
                          <option value="admin">Admin</option>
                          <option value="support">Support</option>
                        </select>
                      </div>
                      
                      {showAccessOptions && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <fieldset className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                            <legend className="text-sm font-medium text-gray-700 px-2">
                              Formular adgang
                            </legend>
                            <div className="mt-2 space-y-3">
                              <div className="flex items-start">
                                <div className="flex items-center h-5">
                                  <input
                                    id="contact"
                                    name="formTypes"
                                    type="checkbox"
                                    value="contact"
                                    checked={formData.allowedcases.includes('contact')}
                                    onChange={handleCheckboxChange}
                                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                  />
                                </div>
                                <div className="ml-3 text-sm">
                                  <label htmlFor="contact" className="font-medium text-gray-700">
                                    Kontakt Formularer
                                  </label>
                                  <p className="text-gray-500">Giver adgang til at håndtere kontakt henvendelser</p>
                                </div>
                              </div>
                              <div className="flex items-start">
                                <div className="flex items-center h-5">
                                  <input
                                    id="partner"
                                    name="formTypes"
                                    type="checkbox"
                                    value="partner"
                                    checked={formData.allowedcases.includes('partner')}
                                    onChange={handleCheckboxChange}
                                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                  />
                                </div>
                                <div className="ml-3 text-sm">
                                  <label htmlFor="partner" className="font-medium text-gray-700">
                                    Partner Ansøgninger
                                  </label>
                                  <p className="text-gray-500">Giver adgang til at håndtere partner ansøgninger</p>
                                </div>
                              </div>
                              <div className="flex items-start">
                                <div className="flex items-center h-5">
                                  <input
                                    id="custom-order"
                                    name="formTypes"
                                    type="checkbox"
                                    value="custom-order"
                                    checked={formData.allowedcases.includes('custom-order')}
                                    onChange={handleCheckboxChange}
                                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                  />
                                </div>
                                <div className="ml-3 text-sm">
                                  <label htmlFor="custom-order" className="font-medium text-gray-700">
                                    Custom Orders
                                  </label>
                                  <p className="text-gray-500">Giver adgang til at håndtere specialbestillinger</p>
                                </div>
                              </div>
                            </div>
                          </fieldset>
                        </motion.div>
                      )}
                      
                      <div>
                        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                          Beskrivelse (valgfri)
                        </label>
                        <textarea
                          id="description"
                          name="description"
                          rows={3}
                          value={formData.description}
                          onChange={handleChange}
                          className="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="Tilføj en kort beskrivelse af brugerens rolle og ansvar..."
                        />
                      </div>
                    </div>
                    
                    <div className="pt-4">
                      <button
                        type="submit"
                        disabled={generating}
                        className={`w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white transition-all ${generating ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 hover:shadow-md transform hover:-translate-y-0.5'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
                      >
                        {generating ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Genererer link...
                          </>
                        ) : (
                          <>
                            <FaKey className="w-5 h-5 mr-2" />
                            Generer invitationslink
                          </>
                        )}
                      </button>
                    </div>
                  </motion.form>
                )}
              </div>
            </div>
          </div>
          
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-6">
                <div className="bg-blue-50 px-6 py-4 border-b border-blue-100">
                  <div className="flex items-center text-blue-800">
                    <FaInfoCircle className="w-5 h-5 mr-2" />
                    <h3 className="font-semibold">Vejledning</h3>
                  </div>
                </div>
                <div className="p-5 text-sm text-gray-600 space-y-4">
                  <p>
                    Invitationslinks giver en sikker måde at tilføje nye brugere til admin systemet.
                    Links kan kun bruges én gang og udløber efter 10 minutter.
                  </p>
                  <div className="space-y-2">
                    <div className="font-medium text-gray-700">Sådan gør du:</div>
                    <ol className="list-decimal list-inside space-y-1 pl-2">
                      <li>Indtast brugerens Discord ID</li>
                      <li>Vælg brugertype (som standard "Freelancer")</li>
                      <li>Vælg hvilke formularer brugeren skal have adgang til</li>
                      <li>Tilføj eventuelt en beskrivelse</li>
                      <li>Klik på "Generer invitationslink"</li>
                      <li>Del det genererede link med brugeren</li>
                    </ol>
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-br from-blue-600 to-indigo-700 rounded-2xl shadow-lg overflow-hidden">
                <div className="p-5 text-white">
                  <div className="flex items-center mb-4">
                    <FaShieldAlt className="w-5 h-5 mr-2" />
                    <h3 className="font-semibold">Sikkerhedspåmindelse</h3>
                  </div>
                  <p className="text-sm text-blue-100 mb-4">
                    Del kun invitationslinks gennem sikre kanaler og kun med betroede personer.
                    Links kan ikke bruges igen efter de er blevet anvendt.
                  </p>
                  <div className="text-xs text-blue-200 border-t border-blue-500 pt-3">
                    <p>Hvis du har brug for at tilbagekalde en brugers adgang, skal du gøre det via Dashboard.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 