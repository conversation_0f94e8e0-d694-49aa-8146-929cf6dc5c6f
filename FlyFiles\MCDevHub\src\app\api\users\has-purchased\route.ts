import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Get the product ID from query parameters
    const productId = request.nextUrl.searchParams.get('productId');
    
    if (!productId) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }
    
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ hasPurchased: false, loggedIn: false });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ hasPurchased: false, loggedIn: true });
    }
    
    // Connect to database
    const { db } = await connectToDatabase();
    
    // Check if the user has purchased this product
    const purchase = await db.collection('user_purchases').findOne({ 
      userId: discordId,
      productId: productId
    });
    
    return NextResponse.json({
      hasPurchased: <PERSON><PERSON>an(purchase),
      purchaseDate: purchase ? purchase.purchaseDate : null,
      loggedIn: true
    });
    
  } catch (error) {
    console.error('Error checking purchase status:', error);
    return NextResponse.json({
      error: 'Failed to check purchase status',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
} 