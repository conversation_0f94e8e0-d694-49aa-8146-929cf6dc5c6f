import { verifyAdminToken } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    // Verify the admin user
    const admin = await verifyAdminToken(request);
    if (!admin || admin.admintype !== 'Freelancer') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    
    const { productId, description } = body;
    
    if (!productId) {
      return NextResponse.json({ message: 'Produkt ID mangler' }, { status: 400 });
    }
    
    if (description === undefined) {
      return NextResponse.json({ message: 'Beskrivelse mangler' }, { status: 400 });
    }

    // Connect to the database
    const { db } = await connectToDatabase();
    
    // Find the product
    const product = await db.collection('products').findOne({
      _id: new ObjectId(productId),
      createdBy: admin.username
    });
    
    if (!product) {
      return NextResponse.json({ message: 'Produkt ikke fundet eller du har ikke adgang til det' }, { status: 404 });
    }

    // Determine which field to update based on what exists already
    const updateField = product.hasOwnProperty('projectDescription') ? 'projectDescription' : 'description';
    console.log(`Updating product using field: ${updateField}`);

    // Update the product description
    await db.collection('products').updateOne(
      { _id: new ObjectId(productId) },
      { $set: { 
          [updateField]: description,
          updatedAt: new Date()
        } 
      }
    );
    
    return NextResponse.json({ 
      message: 'Produktbeskrivelsen blev opdateret succesfuldt',
      updatedField: updateField
    });
    
  } catch (error) {
    console.error('Error in update-description API route:', error);
    return NextResponse.json({ message: error.message || 'Der opstod en fejl' }, { status: 500 });
  }
} 