'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';

export default function AdminLogin() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [currentQuote, setCurrentQuote] = useState(0);
  const router = useRouter();

  // Check for registration success parameter
  useEffect(() => {
    // Check URL parameters for registration success
    const url = new URL(window.location.href);
    const registered = url.searchParams.get('registered');
    
    if (registered === 'true') {
      setSuccessMessage('Din konto er blevet oprettet! Du kan nu logge ind med dine oplysninger.');
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Login fejlede');
      }

      // Omdiriger til freelance dashboard ved succesfuld login
      router.push('/admin/dashboard');
    } catch (error) {
      console.error('Login fejl:', error);
      setError(error instanceof Error ? error.message : 'Login fejlede');
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className="relative min-h-screen bg-gradient-to-b from-indigo-50 via-blue-50 to-gray-100 flex flex-col justify-center overflow-hidden">
      {/* Decorative background elements - Disabled */}
      {/* <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none"></div>
      <div className="absolute inset-0 opacity-10 pointer-events-none bg-[radial-gradient(circle_at_center,rgba(99,102,241,0.15),transparent_35%),radial-gradient(circle_at_25%_25%,rgba(79,70,229,0.15),transparent_25%),radial-gradient(circle_at_75%_75%,rgba(59,130,246,0.15),transparent_20%)]"></div> */}
      
      {/* Bevægelige partikler - Deaktiveret */}
      {/* <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {Array.from({ length: 15 }).map((_, i) => (
          <div 
            key={i} 
            className="absolute rounded-full bg-blue-600/10 animate-float-slow"
            style={{
              width: `${Math.random() * 2 + 1}rem`,
              height: `${Math.random() * 2 + 1}rem`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 10}s`,
              animationDuration: `${Math.random() * 20 + 20}s`
            }}
          />
        ))}
      </div> */}
      
      {/* Svævende geometriske former - Deaktiveret */}
      {/* <div className="absolute top-20 left-10 w-64 h-64 bg-gradient-to-br from-blue-400/10 to-indigo-500/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-72 h-72 bg-gradient-to-tr from-indigo-400/10 to-purple-500/10 rounded-full blur-3xl"></div>
      <div className="absolute top-1/3 right-1/4 w-48 h-48 bg-gradient-to-tl from-blue-300/15 to-purple-400/15 rounded-full blur-2xl"></div> */}
      
      {/* Animated circles - Disabled */}
      {/* <div className="absolute top-1/4 left-1/4 w-4 h-4 bg-blue-600/20 rounded-full animate-pulse"></div>
      <div className="absolute top-3/4 right-1/3 w-6 h-6 bg-indigo-600/20 rounded-full animate-pulse delay-700"></div>
      <div className="absolute bottom-1/4 left-1/3 w-5 h-5 bg-purple-600/20 rounded-full animate-pulse delay-1000"></div>
      <div className="absolute top-2/4 right-1/4 w-3 h-3 bg-cyan-600/20 rounded-full animate-pulse delay-1500"></div>
      <div className="absolute bottom-1/3 right-2/3 w-4 h-4 bg-sky-600/20 rounded-full animate-pulse delay-2000"></div> */}
      
      {/* Animated gradient border */}
      <div className="absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 animate-gradient-x"></div>
      
      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 py-12 flex flex-col lg:flex-row items-center justify-center lg:justify-between">
        {/* Venstre side branding sektion */}
        <div className="hidden lg:block lg:w-5/12 xl:w-1/2 mb-12 lg:mb-0 px-4">
          <div className="max-w-lg">
            <div className="flex items-center mb-6 animate-fade-in-up cursor-default">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl shadow-lg flex items-center justify-center transform transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)] hover:scale-105 hover:shadow-xl">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
              <h2 className="ml-3 text-2xl font-bold text-gray-800">MCDevHub</h2>
            </div>
            
            <h1 className="text-4xl sm:text-5xl font-extrabold text-gray-900 tracking-tight leading-tight mb-6 animate-fade-in-up delay-100">
              Freelance <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-700 animate-text">Portal</span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 animate-fade-in-up delay-200 cursor-default">
              Velkommen til freelance panelet. Log ind for at administrere indsendelser og forespørgsler fra brugere.
            </p>
            
            <div className="space-y-4 mb-10">
              <div className="flex items-start p-4 rounded-xl hover:bg-gradient-to-r from-red-100 to-orange-100 transition-all duration-200 animate-fade-in-up delay-300 cursor-default group">
                <div className="flex-shrink-0 mt-1">
                  <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-r from-red-200 to-orange-200 text-red-600 group-hover:bg-gradient-to-r group-hover:from-red-300 group-hover:to-orange-300 group-hover:text-red-700 transition-all duration-200">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-base font-medium text-gray-800 group-hover:text-red-700 transition-colors duration-200">Se og administrer alle formularer</p>
                  <p className="mt-1 text-sm text-gray-600 group-hover:text-orange-700 transition-colors duration-200">Få et komplet overblik over alle indsendte formularer i systemet.</p>
                </div>
              </div>
              
              <div className="flex items-start p-4 rounded-xl hover:bg-gradient-to-r from-purple-100 to-pink-100 transition-all duration-200 animate-fade-in-up delay-400 cursor-default group">
                <div className="flex-shrink-0 mt-1">
                  <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-r from-purple-200 to-pink-200 text-purple-600 group-hover:bg-gradient-to-r group-hover:from-purple-300 group-hover:to-pink-300 group-hover:text-purple-700 transition-all duration-200">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-base font-medium text-gray-800 group-hover:text-purple-700 transition-colors duration-200">Opdater status og tilføj noter</p>
                  <p className="mt-1 text-sm text-gray-600 group-hover:text-purple-500 transition-colors duration-200">Hold styr på processen og kommunikér effektivt med teamet.</p>
                </div>
              </div>
              
              <div className="flex items-start p-4 rounded-xl hover:bg-gradient-to-r from-green-100 to-teal-100 transition-all duration-200 animate-fade-in-up delay-500 cursor-default group">
                <div className="flex-shrink-0 mt-1">
                  <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-r from-green-200 to-teal-200 text-green-600 group-hover:bg-gradient-to-r group-hover:from-green-300 group-hover:to-teal-300 group-hover:text-green-700 transition-all duration-200">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-base font-medium text-gray-800 group-hover:text-green-700 transition-colors duration-200">Sikker adgang til brugerdata</p>
                  <p className="mt-1 text-sm text-gray-600 group-hover:text-teal-700 transition-colors duration-200">Beskyttet adgang til følsomme oplysninger med rollebaserede tilladelser.</p>
                </div>
              </div>
            </div>
            
            {/* Support sektion */}
            <div className="flex items-center p-4 bg-blue-50 border border-blue-100 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="flex items-center group">
                  <svg className="w-5 h-5 text-blue-600 mr-1.5 transition-colors duration-200 group-hover:text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  <a href="https://discord.com/users/929307108002889769" className="text-sm text-blue-800 font-medium underline hover:text-orange-600 hover:decoration-orange-600 transition-colors duration-200">
                    Kan ikke logge ind?
                  </a>
                </div>
                <span className="text-blue-600">•</span>
                <div className="flex items-center group">
                  <svg className="w-5 h-5 text-blue-600 mr-1.5 transition-colors duration-200 group-hover:text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                  </svg>
                  <a href="https://discord.com/users/929307108002889769" className="text-sm text-blue-800 font-medium underline hover:text-rose-600 hover:decoration-rose-600 transition-colors duration-200">
                    Glemt adgangskode?
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Højre side login formular */}
        <div className="w-full lg:w-5/12 xl:w-5/12 lg:pl-8">
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-gray-100 relative">
            <div className="absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 rounded-t-lg"></div>
            
            {/* Grafisk element - øverste højre hjørne */}
            <div className="absolute top-0 right-0 w-24 h-24 bg-indigo-50 rounded-bl-[5rem] opacity-50"></div>
            <div className="absolute top-0 right-0 w-16 h-16 bg-indigo-100 rounded-bl-[3rem] opacity-50"></div>
            
            <div className="text-center py-8 px-6 sm:px-8 border-b border-gray-100 relative">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-indigo-700 shadow-lg mb-4">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 tracking-tight">
                Freelance Panel
              </h1>
              <p className="mt-2 text-gray-600">
                Log ind for at administrere formularer og forespørgsler
              </p>
            </div>

            {error && (
              <div className="bg-red-50 border-l-4 border-red-500 p-4">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-sm text-red-700 font-medium">{error}</p>
                </div>
              </div>
            )}

            {successMessage && (
              <div className="bg-green-50 border-l-4 border-green-500 p-4">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <p className="text-sm text-green-700 font-medium">{successMessage}</p>
                </div>
              </div>
            )}
            
            <div className="p-6 sm:p-8 relative">
              {/* Grafisk element - nederste venstre hjørne */}
              <div className="absolute -bottom-6 -left-6 w-12 h-12 rounded-full bg-blue-50 opacity-70"></div>
              <div className="absolute -bottom-3 -left-3 w-6 h-6 rounded-full bg-blue-100 opacity-70"></div>
              
              <form className="space-y-6 relative z-10" onSubmit={handleSubmit}>
                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                    Brugernavn
                  </label>
                  <div className="relative group">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <input
                      id="username"
                      name="username"
                      type="text"
                      required
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="appearance-none block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-all duration-200 hover:border-blue-300"
                      placeholder="Indtast dit brugernavn"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                    Adgangskode
                  </label>
                  <div className="relative group">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                      </svg>
                    </div>
                    <input
                      id="password"
                      name="password"
                      type="password"
                      required
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="appearance-none block w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-all duration-200 hover:border-blue-300"
                      placeholder="Indtast din adgangskode"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <div 
                        className="p-1.5 rounded-full bg-gray-100 hover:bg-blue-50 transition-colors duration-200 cursor-pointer"
                        onClick={() => {
                          const passwordInput = document.getElementById('password') as HTMLInputElement;
                          const isVisible = passwordInput.type === 'password';
                          passwordInput.type = isVisible ? 'text' : 'password';
                          const eyeIcon = document.getElementById('eye-icon');
                          if (eyeIcon) {
                            eyeIcon.innerHTML = isVisible ? 
                              `<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />` :
                              `<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                               <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />`;
                          }
                        }}
                      >
                        <svg id="eye-icon" className="h-5 w-5 text-gray-600 hover:text-blue-600 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1.5">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-end mt-2">
                    <a href="/reset-password" className="text-xs text-blue-600 hover:text-blue-800 hover:underline transition-colors flex items-center">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                      Glemt adgangskode?
                    </a>
                  </div>
                </div>

                <div className="bg-blue-50/70 rounded-lg p-4 flex items-start border border-blue-100">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-blue-700">
                      Din login session udløber efter 24 timer af sikkerhedsårsager.
                    </p>
                  </div>
                </div>

                <div>
                  <button
                    type="submit"
                    disabled={loading}
                    className={`w-full flex justify-center items-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-150 ${
                      loading ? 'opacity-80 cursor-default' : ''
                    }`}
                  >
                    {loading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Logger ind...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 512 512">
                          <path d="M416 448h-84c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h84c17.7 0 32-14.3 32-32V160c0-17.7-14.3-32-32-32h-84c-6.6 0-12-5.4-12-12V76c0-6.6 5.4-12 12-12h84c53 0 96 43 96 96v192c0 53-43 96-96 96zm-47-201L201 79c-15-15-41-4.5-41 17v96H24c-13.3 0-24 10.7-24 24v96c0 13.3 10.7 24 24 24h136v96c0 21.5 26 32 41 17l168-168c9.3-9.4 9.3-24.6 0-34z"/>
                        </svg>
                        Log ind
                      </>
                    )}
                  </button>
                  <p className="mt-2 text-center text-xs text-gray-500 cursor-default">
                    Ved at logge ind accepterer du vores{' '}
                    <Link href="/privatlivspolitik" className="text-blue-600 hover:text-blue-700">privatlivspolitik</Link> og{' '}
                    <Link href="/tos" className="text-blue-600 hover:text-blue-700">brugsvilkår</Link>.
                  </p>
                </div>
              </form>

              <div className="mt-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">
                      Eller
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex items-center justify-center">
                <Link 
                  href="/"
                  className="inline-flex items-center text-sm text-gray-600 hover:text-blue-600 transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)] group"
                >
                  <svg 
                    className="w-5 h-5 mr-2 transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)] transform group-hover:-translate-x-2 group-hover:scale-110" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth="2" 
                      d="M10 19l-7-7m0 0l7-7m-7 7h18" 
                    />
                  </svg>
                  Tilbage til hjemmesiden
                </Link>
              </div>
            </div>
            

            
            <div className="bg-white/50 backdrop-blur-sm p-6 border-t border-gray-200/50 text-center text-sm text-gray-600 shadow-[0_-1px_3px_rgba(0,0,0,0.05)]">
              © {new Date().getFullYear()} MCDevHub. Alle rettigheder forbeholdes.
              <div className="flex justify-center mt-3 space-x-6">
                <a href="/privatlivspolitik" className="text-gray-600 hover:text-purple-700 transition-all duration-300 flex items-center font-medium hover:scale-105">
                  <svg className="w-5 h-5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  Privatlivspolitik
                </a>
                <a href="/tos" className="text-gray-600 hover:text-green-700 transition-all duration-300 flex items-center font-medium hover:scale-105">
                  <svg className="w-5 h-5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Brugsvilkår
                </a>
                <a href="https://discord.com/users/929307108002889769" target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-red-700 transition-all duration-300 flex items-center font-medium hover:scale-105">
                  <svg className="w-5 h-5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                  </svg>
                  Kan ikke logge ind?
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Bunddekoration */}
      <div className="absolute bottom-0 left-0 right-0 h-1.5 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600"></div>
    </div>
  );
} 