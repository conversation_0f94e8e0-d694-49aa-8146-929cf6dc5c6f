import Link from 'next/link';
import { FaD<PERSON>rd, FaHeadset } from 'react-icons/fa';

const Footer = () => {
  return (
    <footer className="bg-gradient-to-br from-blue-900 to-blue-950 text-white relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-10">
        <div className="absolute top-10 left-10 w-40 h-40 rounded-full bg-blue-400 blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-60 h-60 rounded-full bg-indigo-500 blur-3xl"></div>
        <div className="absolute top-1/3 right-1/4 w-20 h-20 rounded-full bg-blue-300 blur-xl"></div>
      </div>
      
      {/* Main content */}
      <div className="container mx-auto px-4 py-16 relative z-10">
        {/* Main sections */}
        <div className="flex flex-col lg:flex-row">
          {/* Brand section - taking 1/3 of the space */}
          <div className="lg:w-1/3 mb-12 lg:mb-0 lg:pr-12">
            <div className="flex items-center mb-6">
              <img
                src="/images/McDevHub.png"
                alt="MCDevHub Logo"
                width={40}
                height={40}
                className="mr-3 filter-none"
              />
              <span className="text-2xl font-bold">MCDevHub</span>
            </div>
            
            <p className="text-blue-200 leading-relaxed">
              Danmarks førende markedsplads for Minecraft produkter. Vi tilbyder høj kvalitet til konkurrencedygtige priser.
            </p>
          </div>
          
          {/* Links sections - taking 2/3 of the space */}
          <div className="lg:w-2/3 grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Hurtige Links */}
            <div className="space-y-5">
              <h3 className="text-lg font-bold relative inline-block pb-2">
                Hurtige Links
                <span className="absolute -bottom-1 left-0 w-12 h-0.5 bg-blue-500"></span>
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/" className="text-blue-200 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">
                    Forside
                  </Link>
                </li>
                <li>
                  <Link href="/products" className="text-blue-200 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">
                    Produkter
                  </Link>
                </li>
                <li>
                  <Link href="/custom-orders" className="text-blue-200 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">
                    Specialbestillinger
                  </Link>
                </li>
              </ul>
            </div>
            
            {/* Kontakt */}
            <div className="space-y-5">
              <h3 className="text-lg font-bold relative inline-block pb-2">
                Kontakt
                <span className="absolute -bottom-1 left-0 w-12 h-0.5 bg-blue-500"></span>
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/om-os" className="text-blue-200 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">
                    Om Os
                  </Link>
                </li>
                <li>
                  <Link href="/developers" className="text-blue-200 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">
                  Freelancers
                  </Link>
                </li>
                <li>
                  <Link href="/kontakt" className="text-blue-200 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">
                    Kontakt Os
                  </Link>
                </li>
              </ul>
            </div>
            
            {/* Legal */}
            <div className="space-y-5">
              <h3 className="text-lg font-bold relative inline-block pb-2">
                Juridisk
                <span className="absolute -bottom-1 left-0 w-12 h-0.5 bg-blue-500"></span>
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/tos" className="text-blue-200 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">
                    Handelsbetingelser
                  </Link>
                </li>
                <li>
                  <Link href="/privatlivspolitik" className="text-blue-200 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">
                    Privatlivspolitik
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        {/* Footer Bottom */}
        <div className="mt-16 pt-8 border-t border-blue-800/50 flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="text-blue-300 text-sm">&copy; {new Date().getFullYear()} MCDevHub. Alle rettigheder forbeholdes.</p>
          
          <div className="flex items-center space-x-6 text-blue-300 text-sm">
            <a 
              href="https://discord.mcdevhub.dk" 
              target="_blank" 
              rel="noopener noreferrer" 
              className="hover:text-white transition-colors flex items-center group relative"
              aria-label="Join our Discord"
            >
              <FaDiscord className="mr-1" />
              <span>Discord</span>
              <span className="absolute -top-8 left-1/2 -translate-x-1/2 bg-blue-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                Tilslut dig vores fællesskab!
              </span>
            </a>
            <span>•</span>
            <Link href="/kontakt" className="hover:text-white transition-colors flex items-center group relative">
              <FaHeadset className="mr-1" />
              <span>Support</span>
              <span className="absolute -top-8 left-1/2 -translate-x-1/2 bg-blue-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                Få hjælp her
              </span>
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;