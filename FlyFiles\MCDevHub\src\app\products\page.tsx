'use client';

import React, { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaSearch, FaMap, FaPuzzlePiece, FaCode, FaCubes, FaQuestionCircle, FaCodeBranch, FaPlug, FaFilter, FaArrowRight } from 'react-icons/fa';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import Image from 'next/image';

// Declare global interface for Window
declare global {
  interface Window {
    lastProductsResponse: any;
  }
}

interface Product {
  _id: string;
  projectName: string;
  productType: string;
  price: number;
  discount?: number;
  createdBy: string;
  discordUserId?: string;
  status?: string;
  screenshotUrls: Array<{
    fileId: string;
    filename: string;
    contentType: string;
    url?: string;
  }>;
}

// Hardcoded animation service product
const ANIMATION_SERVICE: Product = {
  _id: 'animation-service',
  projectName: 'Animeret Billede Service',
  productType: 'service',
  price: 199,
  discount: 50,
  createdBy: 'MyckasP',
  status: 'approved',
  screenshotUrls: [
    {
      fileId: 'handdrawn-image-1',
      filename: 'handdrawn1.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745352236/tegnet_ytjlpi.png'
    },
    {
      fileId: 'colored-image-1',
      filename: 'myckasp1.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745352245/myckasp_whxdzt.png'
    },
    {
      fileId: 'handdrawn-image-2',
      filename: 'handdrawn2.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745428885/FlickMC_Tegnet-removebg-preview_vqq6zu.png'
    },
    {
      fileId: 'colored-image-2',
      filename: 'FlickMC.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745428835/FlickMC__2_-removebg-preview_eexzik.png'
    }
  ]
};

// Define all possible filter values for type and price
const FilterValues = {
  types: ['all', 'plugin', 'script', 'map', 'build', 'service'],
  priceRanges: ['all', 'free', 'under100', '100to250', 'over250']
};

// Create a separate component for the search params handling
function ProductsWithSearchParams({ 
  products, 
  setFilteredProducts, 
  setFilters, 
  searchQuery 
}: { 
  products: Product[], 
  setFilteredProducts: React.Dispatch<React.SetStateAction<Product[]>>,
  setFilters: React.Dispatch<React.SetStateAction<{type: string, priceRange: string}>>,
  searchQuery: string
}) {
  const searchParams = useSearchParams();
  
  useEffect(() => {
    const typeParam = searchParams.get('type');
    const priceParam = searchParams.get('priceRange');
    setFilters({
      type: FilterValues.types.includes(typeParam) ? typeParam : 'all',
      priceRange: FilterValues.priceRanges.includes(priceParam) ? priceParam : 'all',
    });
  }, [searchParams, setFilters]);
  
  useEffect(() => {
    // This effect is moved to the parent component
  }, []);
  
  return null; // This component doesn't render anything
}

// Calculate discounted price
const calculateDiscountedPrice = (price: number, discount?: number): number => {
  if (!discount || discount <= 0 || price <= 0) return price;
  return Math.round(price * (1 - discount / 100));
};

export default function ProductsPage() {
  const router = useRouter();
  const pathname = usePathname();
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    type: 'all',
    priceRange: 'all',
  });
  
  // Set page title
  useEffect(() => {
    // Set initial title
    document.title = "Produkter | MCDevHub";
    
    // Maintain title with an interval in case it gets overwritten
    const titleInterval = setInterval(() => {
      if (document.title !== "Produkter | MCDevHub") {
        document.title = "Produkter | MCDevHub";
      }
    }, 500);
    
    // Cleanup
    return () => clearInterval(titleInterval);
  }, []);
  
  // The useSearchParams hook is now in a separate component wrapped in Suspense

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch('/api/products');
        
        if (!response.ok) {
          throw new Error('Kunne ikke hente produkter');
        }
        
        const data = await response.json();
        
        // Make sure we're extracting the array of products
        const productsArray = data.products || [];
        
        // Add this to help debugging in the browser console
        window.lastProductsResponse = data;
        
        // Filter to only include approved or active products
        const approvedProducts = productsArray.filter(p => 
          p.status === 'approved' || p.status === 'active'
        );
        
        // Add our hardcoded animation service product to the list (it's already approved)
        const productsWithHardcoded = [ANIMATION_SERVICE, ...approvedProducts];
        
        // Ensure it's an array before setting state
        if (!Array.isArray(productsArray)) {
          console.error('API returned products in unexpected format:', data);
          setError('Produktdata er i uventet format');
          setProducts([]);
          setFilteredProducts([]);
        } else {
          setProducts(productsWithHardcoded);
          setFilteredProducts(productsWithHardcoded);
        }
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Der opstod en fejl');
        // Initialize with empty arrays to prevent further errors
        setProducts([]);
        setFilteredProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Function to normalize product types
  const normalizeProductType = (type: string): string => {
    if (!type) return '';
    
    type = type.toLowerCase().trim();
    
    // Handle script/skript variations
    if (type === 'script' || type === 'skript') {
      return 'script';
    }
    
    // Handle plugin/plugins variations
    if (type === 'plugin' || type === 'plugins') {
      return 'plugin';
    }
    
    // Handle map/maps variations
    if (type === 'map' || type === 'maps') {
      return 'map';
    }
    
    // Handle build/builds variations
    if (type === 'build' || type === 'builds') {
      return 'build';
    }
    
    return type;
  };

  useEffect(() => {
    // Apply filters and search
    let result = [...products];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(product => 
        product.projectName.toLowerCase().includes(query)
      );
      console.log(`After search filter "${searchQuery}":`, result.length);
    }
    
    // Apply type filter
    if (filters.type !== 'all') {
      const targetType = filters.type;
      result = result.filter(product => {
        const normalizedType = normalizeProductType(product.productType);
        return normalizedType === normalizeProductType(targetType);
      });
      console.log(`After type filter "${filters.type}":`, result.length);
    }
    
    // Apply price filter
    if (filters.priceRange !== 'all') {
      switch (filters.priceRange) {
        case 'free':
          result = result.filter(product => {
            const finalPrice = product.discount ? product.price * (1 - product.discount/100) : product.price;
            return finalPrice === 0;
          });
          break;
        case 'under100':
          result = result.filter(product => {
            const finalPrice = product.discount ? product.price * (1 - product.discount/100) : product.price;
            return finalPrice >= 0 && finalPrice < 99;
          });
          break;
        case '100to250':
          result = result.filter(product => {
            const finalPrice = product.discount ? product.price * (1 - product.discount/100) : product.price;
            return finalPrice >= 100 && finalPrice <= 250;
          });
          break;
        case 'over250':
          result = result.filter(product => {
            const finalPrice = product.discount ? product.price * (1 - product.discount/100) : product.price;
            return finalPrice > 250;
          });
          break;
      }
      console.log(`After price filter "${filters.priceRange}":`, result.length);
    }
    
    setFilteredProducts(result);
  }, [products, searchQuery, filters]);

  const getProductTypeLabel = (type: string) => {
    const iconClass = "w-4 h-4 mr-2";
    const labelClass = "text-sm font-medium";
    
    switch (type) {
      case 'plugin':
        return <div className="flex items-center"><FaPlug className={iconClass} /><span className={labelClass}>Plugin</span></div>;
      case 'script':
        return <div className="flex items-center"><FaCode className={iconClass} /><span className={labelClass}>Skript</span></div>;
      case 'map':
        return <div className="flex items-center"><FaMap className={iconClass} /><span className={labelClass}>Map</span></div>;
      case 'build':
        return <div className="flex items-center"><FaCubes className={iconClass} /><span className={labelClass}>Build</span></div>;
      case 'service':
        return <div className="flex items-center"><FaCodeBranch className={iconClass} /><span className={labelClass}>Service</span></div>;
      default:
        return <div className="flex items-center"><FaQuestionCircle className={iconClass} /><span className={labelClass}>{type}</span></div>;
    }
  };

  const getProductTypeColor = (type: string) => {
    switch (type) {
      case 'plugin':
        return 'bg-pink-500/90 hover:bg-pink-600/90 shadow-pink-200';
      case 'script':
        return 'bg-green-500/90 hover:bg-green-600/90 shadow-green-200';
      case 'map':
        return 'bg-purple-500/90 hover:bg-purple-600/90 shadow-purple-200';
      case 'build':
        return 'bg-amber-500/90 hover:bg-amber-600/90 shadow-amber-200';
      case 'service':
        return 'bg-blue-500/90 hover:bg-blue-600/90 shadow-blue-200';
      default:
        return 'bg-gray-500/90 hover:bg-gray-600/90 shadow-gray-200';
    }
  };
  
  // Handle filter changes with URL hash updates
  const handleFilterChange = (filterType: 'type' | 'priceRange', value: string) => {
    setFilters(prev => ({ 
      ...prev,
      [filterType]: value 
    }));
  };
  
  // Reset all filters and clear URL hash
  const resetFilters = () => {
    setFilters({ type: 'all', priceRange: 'all' });
    setSearchQuery('');
    history.replaceState(null, '', pathname); // Remove hash from URL
  };

  // Add scroll-lock effect for loading overlay
  useEffect(() => {
    if (loading) {
      document.body.style.overflow = 'hidden';
      window.scrollTo(0, 0);
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [loading]);

  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90 backdrop-blur-sm">
        <div className="flex flex-col items-center max-w-sm mx-auto p-8 rounded-xl bg-white shadow-lg border border-gray-100">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mb-6" style={{ animationDuration: '0.4s' }}></div>
          <div className="text-blue-700 text-lg font-medium">Indlæser produkter...</div>
          <p className="text-gray-500 text-sm mt-2 text-center">Vent venligst mens vi henter produktlisten</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 py-12 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="bg-red-50 rounded-xl p-8 text-center shadow-md">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Fejl ved indlæsning</h2>
            <p className="text-gray-600 max-w-2xl mx-auto mb-6">{error}</p>
            <button 
              onClick={() => window.location.reload()}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              Prøv igen
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4">
      <div className="container mx-auto max-w-7xl">
        {/* Wrap useSearchParams component in Suspense boundary */}
        <Suspense fallback={null}>
          <ProductsWithSearchParams 
            products={products} 
            setFilteredProducts={setFilteredProducts} 
            setFilters={setFilters} 
            searchQuery={searchQuery} 
          />
        </Suspense>
        
        <div className="flex flex-col lg:flex-row gap-10">
          {/* Main Content */}
          <div className="lg:w-3/4">
            <div className="mb-12"></div>

            {/* Search Bar */}
            <div className="mb-8 relative">
              <div className="relative group">
                <input
                  type="text"
                  placeholder="Søg efter produkter..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-14 pr-12 py-4 rounded-2xl border border-blue-100 bg-white/60 backdrop-blur-md shadow transition-all duration-300
                  focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent focus:bg-white focus:shadow-lg
                  text-lg placeholder-gray-400"
                />
                <div className="absolute left-5 top-1/2 -translate-y-1/2 text-blue-400 transition-all duration-300
                  group-focus-within:text-blue-600">
                  <FaSearch className="text-xl transition-transform duration-300 group-focus-within:scale-110" />
                </div>
                {searchQuery && (
                  <button 
                    onClick={() => setSearchQuery('')}
                    className="absolute right-5 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 
                    focus:outline-none focus:text-blue-500 p-1 rounded-full hover:bg-gray-100 transition-all duration-200"
                    aria-label="Clear search"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </button>
                )}
              </div>
              {searchQuery && (
                <div className="absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-blue-400 to-blue-600 rounded animate-pulse"></div>
              )}
            </div>

            {/* Active Filter Indicators */}
            {(filters.type !== 'all' || filters.priceRange !== 'all') && (
              <div className="mb-7 flex flex-wrap gap-3">
                {filters.type !== 'all' && (
                  <div className="inline-flex items-center bg-blue-100 text-blue-800 px-4 py-1.5 rounded-full text-sm font-medium shadow-sm">
                    <span>Type: {getProductTypeLabel(filters.type)}</span>
                    <button 
                      onClick={() => handleFilterChange('type', 'all')}
                      className="ml-2 text-blue-700 hover:text-blue-900 rounded-full px-1"
                    >
                      &times;
                    </button>
                  </div>
                )}
                {filters.priceRange !== 'all' && (
                  <div className="inline-flex items-center bg-green-100 text-green-800 px-4 py-1.5 rounded-full text-sm font-medium shadow-sm">
                    <span>
                      Pris: {
                        filters.priceRange === 'free' ? (
                          <span className="text-green-500">Gratis</span>
                        ) : (
                          filters.priceRange === 'under100' ? 'Under 100 DKK' :
                          filters.priceRange === '100to250' ? '100 - 250 DKK' :
                          'Over 250 DKK'
                        )
                      }
                    </span>
                    <button 
                      onClick={() => handleFilterChange('priceRange', 'all')}
                      className="ml-2 text-green-700 hover:text-green-900 rounded-full px-1"
                    >
                      &times;
                    </button>
                  </div>
                )}
                <button
                  onClick={resetFilters}
                  className="text-sm text-gray-600 hover:text-blue-600 hover:underline px-2"
                >
                  Ryd alle
                </button>
              </div>
            )}

            {/* Products Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-8">
              {filteredProducts.length === 0 ? (
                <div className="col-span-full text-center text-gray-500 text-lg py-16">Ingen produkter matcher dine filtre.</div>
              ) : (
                filteredProducts.map((product) => (
                  <Link
                    href={`/products/${product._id}`}
                    key={product._id}
                    className="group block bg-white/60 backdrop-blur-md border border-blue-100 rounded-3xl shadow-lg overflow-hidden transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)] hover:-translate-y-2 hover:shadow-2xl"
                  >
                    <div className="relative aspect-[4/3] bg-gradient-to-br from-blue-100/40 to-indigo-50/50 flex items-center justify-center overflow-hidden">
                      {product.screenshotUrls?.[0]?.url ? (
                        <img
                          src={product.screenshotUrls[0].url}
                          alt={product.projectName}
                          className="object-cover w-full h-full rounded-2xl border border-blue-100 shadow-sm group-hover:scale-105 transition-transform duration-500 ease-[cubic-bezier(0.4,0,0.2,1)]"
                        />
                      ) : (
                        <div className="flex flex-col items-center justify-center w-full h-full text-blue-300">
                          <FaPuzzlePiece className="text-5xl mb-2" />
                          <span className="text-base">Intet billede</span>
                        </div>
                      )}
                      <div className="absolute top-4 left-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold shadow bg-blue-100 text-blue-800`}>{getProductTypeLabel(product.productType)}</span>
                      </div>
                      
                      {/* Add discount badge */}
                      {product.price > 0 && product.discount && product.discount > 0 && (
                        <div className="absolute top-4 right-4 bg-amber-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-md transform rotate-2 z-10">
                          -{product.discount}%
                        </div>
                      )}
                    </div>
                    <div className="p-6 flex flex-col gap-2">
                      <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-700 transition-colors duration-300 ease-in-out line-clamp-2 min-h-[2.5rem]">{product.projectName}</h3>
                      <div className="flex items-center justify-between mt-1">
                        <span className="font-bold text-blue-600 text-lg">
                          {product.price === 0 || (product.discount && product.discount === 100) ? (
                            <div className="flex items-center">
                              <span className="text-green-500">Gratis</span>
                              {product.discount === 100 && (
                                <span className="text-orange-500 line-through text-sm ml-2">{product.price} DKK</span>
                              )}
                            </div>
                          ) : product.discount && product.discount > 0 ? (
                            <div className="flex items-center">
                              <span>{calculateDiscountedPrice(product.price, product.discount)} DKK</span>
                              <span className="text-orange-500 line-through text-sm ml-2">{product.price} DKK</span>
                            </div>
                          ) : (
                            <span>{product.price} DKK</span>
                          )}
                        </span>
                        <div className="flex items-center relative">
                          <span className="text-blue-400 opacity-0 transform translate-x-4 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)] text-sm font-medium mr-2">Se produkt</span>
                          <FaArrowRight className="text-blue-400 group-hover:translate-x-1.5 group-hover:scale-110 transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]" />
                        </div>
                      </div>
                    </div>
                  </Link>
                ))
              )}
            </div>
          </div>

          {/* Filters Sidebar */}
          <div className="lg:w-1/4 mt-20">
            <div className="bg-white border border-gray-100 rounded-2xl shadow p-8 sticky top-24">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-bold text-gray-900">Filtre</h2>
                <button
                  onClick={resetFilters}
                  className="text-sm text-blue-600 hover:underline font-medium px-2 py-1 rounded"
                >
                  Nulstil
                </button>
              </div>
              {/* Product Type Filter */}
              <div className="mb-7">
                <h3 className="text-xs font-semibold text-gray-600 mb-2 uppercase tracking-wide">Produkttype</h3>
                <div className="flex flex-wrap gap-2">
                  {FilterValues.types.map((type) => (
                    <button
                      key={type}
                      onClick={() => handleFilterChange('type', type)}
                      className={`px-5 py-2 rounded-full text-sm font-semibold transition-all duration-150 border-none focus:outline-none focus:ring-2 focus:ring-blue-300 ${filters.type === type ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800 hover:bg-blue-50'}`}
                    >
                      {type === 'all' ? 'Alle' : getProductTypeLabel(type).props.children[1].props.children}
                    </button>
                  ))}
                </div>
              </div>
              <div className="border-t border-gray-100 my-6"></div>
              {/* Price Range Filter */}
              <div className="mb-2">
                <h3 className="text-xs font-semibold text-gray-600 mb-2 uppercase tracking-wide">Pris</h3>
                <div className="flex flex-wrap gap-2">
                  {FilterValues.priceRanges.map((price) => (
                    <button
                      key={price}
                      onClick={() => handleFilterChange('priceRange', price)}
                      className={`px-5 py-2 rounded-full text-sm font-semibold transition-all duration-150 border-none focus:outline-none focus:ring-2 focus:ring-blue-300 ${filters.priceRange === price ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800 hover:bg-blue-50'}`}
                    >
                      {
                        price === 'all' ? 'Alle' :
                        price === 'free' ? 'Gratis' :
                        price === 'under100' ? 'Under 100 DKK' :
                        price === '100to250' ? '100 - 250 DKK' :
                        'Over 250 DKK'
                      }
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 