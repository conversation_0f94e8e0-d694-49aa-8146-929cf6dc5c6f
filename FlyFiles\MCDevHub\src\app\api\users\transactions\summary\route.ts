import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';

export async function GET() {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Connect to database
    const { db } = await connectToDatabase();
    
    // Check if user is an admin/freelancer
    const adminUser = await db.collection('adminusers').findOne({ 
      discordUserId: discordId 
    });
    
    if (!adminUser) {
      return NextResponse.json({ 
        totalIncome: 0,
        transactionCount: 0
      });
    }
    
    // Get transaction count
    const transactionCount = await db.collection('transactions')
      .countDocuments({ sellerId: discordId });
    
    // Get total income (after 15% fee)
    const transactionsResult = await db.collection('transactions')
      .aggregate([
        { $match: { sellerId: discordId } },
        { $group: { 
          _id: null, 
          total: { $sum: "$amount" } 
        }}
      ])
      .toArray();
    
    const totalAmount = transactionsResult.length > 0 ? transactionsResult[0].total : 0;
    const totalIncome = totalAmount * 0.85; // Apply 15% fee
    
    return NextResponse.json({
      totalIncome,
      transactionCount
    });
    
  } catch (error) {
    console.error('Error fetching transaction summary:', error);
    return NextResponse.json({
      error: 'Failed to fetch transaction summary',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
} 