import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';
import { format } from 'date-fns';
import { da } from 'date-fns/locale';

export async function GET(request: NextRequest) {
  try {
    // Get authentication from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the discordId from query params
    const { searchParams } = new URL(request.url);
    const discordId = searchParams.get('discordId');

    if (!discordId) {
      return NextResponse.json({ error: 'Discord ID is required' }, { status: 400 });
    }
    
    // Extract the Discord ID from the authenticated user session
    const requestingUserDiscordId = session.user.id;
    
    // Only allow users to fetch their own data (if a user has admin permissions, this could be adjusted)
    if (requestingUserDiscordId !== discordId) {
      return NextResponse.json({ error: 'You can only fetch your own user data' }, { status: 403 });
    }

    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // First check if the user exists in adminusers collection
    const adminUser = await db.collection('adminusers').findOne({ discordUserId: discordId });
    
    // If they exist in adminusers, use that data
    if (adminUser) {
      const createdAt = adminUser.createdAt || new Date();
      const formattedDate = format(new Date(createdAt), 'dd.MM.yyyy', { locale: da });
      
      return NextResponse.json({ 
        user: {
          _id: adminUser._id.toString(),
          username: adminUser.username || '',
          createdAt: formattedDate,
          isAdmin: true
        } 
      });
    }
    
    // If not in adminusers, check regular users collection
    const user = await db.collection('users').findOne({ discordUserId: discordId });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Format the creation date
    const createdAt = user.createdAt || new Date();
    const formattedDate = format(new Date(createdAt), 'dd.MM.yyyy', { locale: da });
    
    // Return a cleaned version of the user data (only include necessary fields)
    return NextResponse.json({ 
      user: {
        _id: user._id.toString(),
        username: user.username || '',
        createdAt: formattedDate,
        isAdmin: false
      } 
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 