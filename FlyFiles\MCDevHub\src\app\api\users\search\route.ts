import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '10');
    
    if (!query || query.trim().length < 2) {
      return NextResponse.json({ error: 'Search query must be at least 2 characters' }, { status: 400 });
    }

    // Connect to database
    const { db } = await connectToDatabase();
    
    const searchQuery = query.trim();
    const searchRegex = new RegExp(searchQuery, 'i'); // Case-insensitive search

    // Search in adminusers collection (freelancers/developers)
    const adminUsers = await db.collection('adminusers')
      .find(
        {
          $or: [
            { username: searchRegex },
            { discordUserId: searchQuery } // Exact match for Discord ID
          ]
        },
        {
          projection: {
            username: 1,
            discordUserId: 1,
            avatar: 1,
            avatarUrl: 1,
            admintype: 1,
            isVerified: 1,
            createdAt: 1,
            // Exclude sensitive data
            password: 0,
            allowedcases: 0,
            email: 0
          }
        }
      )
      .limit(Math.min(limit, 20)) // Cap at 20 results
      .toArray();

    // Search in regular users collection
    const regularUsers = await db.collection('users')
      .find(
        {
          $or: [
            { username: searchRegex },
            { discordUserId: searchQuery } // Exact match for Discord ID
          ]
        },
        {
          projection: {
            username: 1,
            discordUserId: 1,
            createdAt: 1,
            // Exclude sensitive data
            auth_id: 0,
            balance: 0,
            notifications: 0,
            favorites: 0
          }
        }
      )
      .limit(Math.min(limit, 20)) // Cap at 20 results
      .toArray();

    // Also search in accounts collection for Discord usernames
    const discordAccounts = await db.collection('accounts')
      .find(
        {
          provider: 'discord',
          username: searchRegex
        },
        {
          projection: {
            username: 1,
            discordId: 1,
            avatar: 1,
            avatarUrl: 1,
            email: 1,
            userId: 1
          }
        }
      )
      .limit(Math.min(limit, 20)) // Cap at 20 results
      .toArray();

    // Combine and format results
    const adminUserResults = adminUsers.map(user => ({
      username: user.username,
      discordUserId: user.discordUserId,
      avatar: user.avatar,
      avatarUrl: user.avatarUrl,
      isFreelancer: user.admintype === 'Freelancer',
      isAdmin: user.admintype === 'Admin',
      isVerified: user.isVerified || false,
      createdAt: user.createdAt,
      type: 'admin'
    }));

    const regularUserResults = regularUsers
      .filter(user => !adminUsers.some(admin => admin.discordUserId === user.discordUserId)) // Avoid duplicates
      .map(user => ({
        username: user.username,
        discordUserId: user.discordUserId,
        isFreelancer: false,
        isAdmin: false,
        isVerified: false,
        createdAt: user.createdAt,
        type: 'user'
      }));

    // Process Discord accounts results
    const discordAccountResults = discordAccounts
      .filter(account =>
        // Avoid duplicates with adminUsers and regularUsers
        !adminUsers.some(admin => admin.discordUserId === account.discordId) &&
        !regularUsers.some(user => user.discordUserId === account.discordId)
      )
      .map(account => ({
        username: account.username,
        discordUserId: account.discordId,
        avatar: account.avatar,
        avatarUrl: account.avatarUrl,
        isFreelancer: false,
        isAdmin: false,
        isVerified: false,
        createdAt: null, // We don't have creation date from accounts
        type: 'discord'
      }));

    const allResults = [...adminUserResults, ...regularUserResults, ...discordAccountResults]
      .slice(0, limit) // Apply final limit
      .sort((a, b) => {
        // Sort by relevance: exact username matches first, then by creation date
        const aExactMatch = a.username?.toLowerCase() === searchQuery.toLowerCase();
        const bExactMatch = b.username?.toLowerCase() === searchQuery.toLowerCase();

        if (aExactMatch && !bExactMatch) return -1;
        if (!aExactMatch && bExactMatch) return 1;

        // Then by user type (freelancers/admins first)
        if (a.isFreelancer && !b.isFreelancer) return -1;
        if (!a.isFreelancer && b.isFreelancer) return 1;

        // Finally by creation date (newest first, null dates last)
        const aDate = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const bDate = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return bDate - aDate;
      });

    return NextResponse.json({
      users: allResults,
      total: allResults.length,
      query: searchQuery
    });
    
  } catch (error) {
    console.error('Error searching users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
