import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import * as notificationUtils from '../../../../../lib/notifications';

// POST /api/user/notifications/create - Create a new notification
export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const data = await request.json();
    const { title, message, type, link } = data;
    
    if (!title || !message) {
      return NextResponse.json({ error: 'Title and message are required' }, { status: 400 });
    }
    
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Create notification
    const result = await notificationUtils.addNotification(discordId, {
      title,
      message,
      ...(type && { type }),
      ...(link && { link })
    });
    
    if (!result.success) {
      return NextResponse.json({ error: result.error || 'Failed to create notification' }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Notification created successfully'
    });
  } catch (error) {
    console.error('Error creating notification:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}