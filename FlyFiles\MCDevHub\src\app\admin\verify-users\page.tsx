'use client';

import { useState, FormEvent } from 'react';
import { FaCheck, FaTimes, FaUserShield, FaExclamationTriangle } from 'react-icons/fa';

export default function VerifyUsersPage() {
  const [discordId, setDiscordId] = useState('');
  const [discordName, setDiscordName] = useState('');
  const [isVerified, setIsVerified] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success?: boolean;
    message: string;
    error?: boolean;
  } | null>(null);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setResult(null);

    try {
      // Get the admin secret from localStorage (in a real app, you'd have a proper admin auth system)
      const adminSecret = localStorage.getItem('admin_secret') || '';

      const response = await fetch('/api/verified', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-secret': adminSecret
        },
        body: JSON.stringify({
          discordId,
          discordName,
          isVerified
        })
      });

      const data = await response.json();

      if (response.ok) {
        setResult({
          success: true,
          message: `User ${discordName} has been ${isVerified ? 'verified' : 'unverified'} successfully.`
        });
        // Reset form if successful
        setDiscordId('');
        setDiscordName('');
        setIsVerified(true);
      } else {
        setResult({
          success: false,
          error: true,
          message: data.error || 'Unknown error occurred'
        });
      }
    } catch (error) {
      setResult({
        success: false,
        error: true,
        message: 'Failed to update user verification status'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetAdminSecret = () => {
    const secret = prompt('Enter admin secret:');
    if (secret) {
      localStorage.setItem('admin_secret', secret);
      alert('Admin secret has been set.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-28 pb-20">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <FaUserShield className="text-blue-600 text-xl" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">Bruger Verifikation</h1>
                <p className="text-gray-500">Administrer verificerede brugere</p>
              </div>
            </div>

            <div className="mb-8">
              <button
                onClick={handleSetAdminSecret}
                className="text-sm text-blue-600 hover:underline flex items-center"
              >
                <FaExclamationTriangle className="mr-1" /> Konfigurer Admin Nøgle
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="discordId" className="block text-sm font-medium text-gray-700 mb-1">
                  Discord ID
                </label>
                <input
                  type="text"
                  id="discordId"
                  value={discordId}
                  onChange={(e) => setDiscordId(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                  placeholder="f.eks. 123456789012345678"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Discord ID kan findes ved at højreklikke på brugeren og vælge "Kopier ID"
                </p>
              </div>

              <div>
                <label htmlFor="discordName" className="block text-sm font-medium text-gray-700 mb-1">
                  Discord Brugernavn
                </label>
                <input
                  type="text"
                  id="discordName"
                  value={discordName}
                  onChange={(e) => setDiscordName(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                  placeholder="f.eks. username"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isVerified"
                  checked={isVerified}
                  onChange={(e) => setIsVerified(e.target.checked)}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="isVerified" className="ml-2 block text-sm text-gray-700">
                  Verificeret
                </label>
              </div>

              <div className="pt-4">
                <button
                  type="submit"
                  disabled={isLoading}
                  className={`w-full bg-blue-600 text-white py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors ${
                    isLoading ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                >
                  {isLoading ? 'Behandler...' : 'Gem Ændringer'}
                </button>
              </div>
            </form>

            {result && (
              <div
                className={`mt-6 p-4 rounded-md ${
                  result.error ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'
                }`}
              >
                <div className="flex items-center">
                  {result.error ? (
                    <FaTimes className="mr-2 text-red-500" />
                  ) : (
                    <FaCheck className="mr-2 text-green-500" />
                  )}
                  <p>{result.message}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 