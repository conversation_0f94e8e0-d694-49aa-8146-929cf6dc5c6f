import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { triggerBadgeCheck } from '@/lib/utils/badgeUtils';

// GET /api/favorites - Get user's favorite products
export async function GET(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Get user's favorites from MongoDB
    const user = await db.collection('users').findOne({ discordUserId: discordId });
    
    if (!user) {
      return NextResponse.json({ favorites: [] });
    }
    
    // Return the favorites array or empty array if not present
    return NextResponse.json({ 
      favorites: user.favorites || [] 
    });
  } catch (error) {
    console.error('Error getting favorites:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/favorites - Add or remove a product from favorites
export async function POST(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Parse the request body
    const body = await request.json();
    const { productId, action } = body;
    
    if (!productId) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }
    
    if (!['add', 'remove'].includes(action)) {
      return NextResponse.json({ error: 'Action must be either "add" or "remove"' }, { status: 400 });
    }
    
    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Get product details to store with the favorite
    const product = await db.collection('products').findOne(
      { _id: productId.length === 24 ? new ObjectId(productId) : productId },
      { projection: { projectName: 1, productType: 1, screenshotUrls: 1 } }
    );
    
    // Create favorite object with product details
    const favorite = {
      productId,
      addedAt: new Date(),
      productName: product?.projectName || 'Unknown Product',
      productType: product?.productType || 'unknown',
      thumbnailUrl: product?.screenshotUrls?.[0]?.url || null
    };
    
    if (action === 'add') {
      // Add to favorites - using upsert to create user document if it doesn't exist
      await db.collection('users').updateOne(
        { discordUserId: discordId },
        { 
          $addToSet: { favorites: favorite },
          $setOnInsert: { 
            discordUserId: discordId,
            createdAt: new Date()
          }
        },
        { upsert: true }
      );
      
      // Trigger badge check after adding to favorites
      triggerBadgeCheck(discordId, 'favorite_add').catch(error => {
        console.error('Error checking badges after favorite add:', error);
      });

      return NextResponse.json({
        success: true,
        message: 'Product added to favorites',
        isFavorite: true
      });
    } else {
      // Remove from favorites
      await db.collection('users').updateOne(
        { discordUserId: discordId },
        { $pull: { favorites: { productId } } }
      );
      
      return NextResponse.json({ 
        success: true, 
        message: 'Product removed from favorites',
        isFavorite: false
      });
    }
  } catch (error) {
    console.error('Error updating favorites:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper endpoint to check if a product is favorited
export async function OPTIONS(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ isFavorite: false, loggedIn: false });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ isFavorite: false, loggedIn: false });
    }
    
    // Get productId from URL
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');
    
    if (!productId) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }
    
    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Check if product is in user's favorites
    const user = await db.collection('users').findOne({
      discordUserId: discordId,
      'favorites.productId': productId
    });
    
    return NextResponse.json({
      isFavorite: !!user,
      loggedIn: true
    });
  } catch (error) {
    console.error('Error checking favorite status:', error);
    return NextResponse.json({ isFavorite: false, loggedIn: false });
  }
} 