import { ObjectId } from 'mongodb';

/**
 * Badge interface for earned badges
 */
export interface IBadge {
  id: string;           // Badge identifier
  earnedAt: Date;       // When the badge was earned
  notified?: boolean;   // Whether user was notified about earning this badge
}

/**
 * Badge definition interface
 */
export interface IBadgeDefinition {
  id: string;
  name: string;         // Danish name
  description: string;  // Danish description
  icon: string;         // Icon identifier or emoji
  category: 'universal' | 'freelancer' | 'regular';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  color: string;        // CSS color class
}

/**
 * Badge criteria result interface
 */
export interface IBadgeCriteriaResult {
  eligible: boolean;
  progress?: number;    // Optional progress indicator (0-100)
  maxProgress?: number; // Maximum value for progress
}

/**
 * User data interface for badge calculations
 */
export interface IUserBadgeData {
  discordUserId: string;
  isFreelancer: boolean;
  createdAt?: Date;
  
  // Purchase data
  totalPurchases?: number;
  purchaseCategories?: string[];
  balancePurchases?: number;
  
  // Sales data (freelancers only)
  totalIncome?: number;
  totalProducts?: number;
  activeProductsByCategory?: Record<string, number>;
  
  // Social data
  isVerified?: boolean;
  hasGithub?: boolean;
  hasYoutube?: boolean;
  hasEmail?: boolean;
  
  // Community data
  totalFavorites?: number;
  productsFavorited?: number;
}

/**
 * All available badges with their definitions
 */
export const BADGE_DEFINITIONS: Record<string, IBadgeDefinition> = {
  // Universal badges
  loyal_customer: {
    id: 'loyal_customer',
    name: 'Tro Kunde',
    description: 'Foretaget 10+ køb på platformen',
    icon: '🛒',
    category: 'universal',
    rarity: 'common',
    color: 'bg-blue-500'
  },
  
  collector: {
    id: 'collector',
    name: 'Samler',
    description: 'Købt produkter fra 3+ forskellige kategorier',
    icon: '📦',
    category: 'universal',
    rarity: 'common',
    color: 'bg-green-500'
  },
  
  early_bird: {
    id: 'early_bird',
    name: 'Tidlig Fugl',
    description: 'En af MCDevHubs tidlige medlemmer',
    icon: '🐦',
    category: 'universal',
    rarity: 'legendary',
    color: 'bg-yellow-500'
  },
  
  verified_creator: {
    id: 'verified_creator',
    name: 'Verificeret Skaber',
    description: 'Opnået verificeret status på platformen',
    icon: '✅',
    category: 'universal',
    rarity: 'rare',
    color: 'bg-blue-600'
  },
  
  social_connector: {
    id: 'social_connector',
    name: 'Social Forbinder',
    description: 'Tilføjet alle sociale links (GitHub, YouTube, email)',
    icon: '🌐',
    category: 'universal',
    rarity: 'common',
    color: 'bg-purple-500'
  },
  
  // Freelancer-exclusive badges
  rising_star: {
    id: 'rising_star',
    name: 'Opstigende Stjerne',
    description: 'Tjent 1.000+ DKK i totale salg',
    icon: '⭐',
    category: 'freelancer',
    rarity: 'rare',
    color: 'bg-yellow-600'
  },
  
  product_pioneer: {
    id: 'product_pioneer',
    name: 'Produkt Pioner',
    description: 'Uploadet 5+ forskellige produkter',
    icon: '🔧',
    category: 'freelancer',
    rarity: 'common',
    color: 'bg-orange-500'
  },
  
  category_master: {
    id: 'category_master',
    name: 'Kategori Mester',
    description: 'Har 5+ aktive produkter i en enkelt kategori',
    icon: '🎯',
    category: 'freelancer',
    rarity: 'rare',
    color: 'bg-red-500'
  },
  
  customer_favorite: {
    id: 'customer_favorite',
    name: 'Kunde Favorit',
    description: 'Produkter favoriseret 50+ gange i alt',
    icon: '❤️',
    category: 'freelancer',
    rarity: 'epic',
    color: 'bg-pink-500'
  },
  
  // Regular user badges
  wishlist_curator: {
    id: 'wishlist_curator',
    name: 'Ønskeliste Kurator',
    description: 'Tilføjet 20+ produkter til favoritter',
    icon: '📋',
    category: 'regular',
    rarity: 'common',
    color: 'bg-indigo-500'
  },
  
  diverse_taste: {
    id: 'diverse_taste',
    name: 'Forskellig Smag',
    description: 'Købt fra alle tilgængelige produktkategorier',
    icon: '🎨',
    category: 'regular',
    rarity: 'epic',
    color: 'bg-teal-500'
  },
  
  balance_master: {
    id: 'balance_master',
    name: 'Balance Mester',
    description: 'Foretaget 5+ køb med platform balance',
    icon: '⚖️',
    category: 'regular',
    rarity: 'rare',
    color: 'bg-cyan-500'
  },
  
  // Special achievement badge
  community_champion: {
    id: 'community_champion',
    name: 'Samfunds Mester',
    description: '50+ køb, 10+ favoritter, verificeret status og alle sociale links',
    icon: '🏆',
    category: 'universal',
    rarity: 'legendary',
    color: 'bg-gradient-to-r from-yellow-400 to-orange-500'
  }
};

/**
 * Product categories used in the system
 */
export const PRODUCT_CATEGORIES = [
  'plugin',
  'script', 
  'service',
  'map',
  'build',
  // Legacy categories
  'skript',
  'resourcepack',
  'other'
];

/**
 * Platform launch date for Early Bird badge
 */
export const PLATFORM_LAUNCH_DATE = new Date('2025-03-23');
export const EARLY_BIRD_CUTOFF_DATE = new Date('2025-09-23');

/**
 * User document interface with badges
 */
export interface IUserWithBadges {
  _id?: ObjectId;
  discordUserId: string;
  badges?: IBadge[];
  createdAt?: Date;
  [key: string]: any; // Allow other properties
}

/**
 * Admin user document interface with badges
 */
export interface IAdminUserWithBadges {
  _id?: ObjectId;
  username: string;
  discordUserId: string;
  admintype?: string;
  badges?: IBadge[];
  createdAt?: Date;
  [key: string]: any; // Allow other properties
}

/**
 * Collection names for badge storage
 */
export const BADGE_COLLECTIONS = {
  USERS: 'users',
  ADMIN_USERS: 'adminusers'
};
