import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { verifyAdminToken } from '@/lib/auth';

// This API route fetches all products uploaded by a specific freelancer
export async function GET(request) {
  try {
    // Verify the admin user
    const admin = await verifyAdminToken();
    if (!admin) {
      return NextResponse.json(
        { message: 'Ikke autoriseret' },
        { status: 401 }
      );
    }

    // Only freelancers can access their products
    if (admin.admintype !== 'Freelancer') {
      return NextResponse.json(
        { message: 'Kun freelancere kan se deres produkter' },
        { status: 403 }
      );
    }

    // Connect to database
    const { db } = await connectToDatabase();
    
    // Fetch products created by this user
    const products = await db.collection('products')
      .find({ createdBy: admin.username })
      .sort({ createdAt: -1 })
      .toArray();
    
    return NextResponse.json(products);
    
  } catch (error) {
    console.error('Error fetching freelancer products:', error);
    return NextResponse.json(
      { message: 'Der opstod en fejl ved hentning af produkter: ' + error.message },
      { status: 500 }
    );
  }
} 