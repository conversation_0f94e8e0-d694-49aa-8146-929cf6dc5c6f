import { verifyAdminToken } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import { uploadImage } from '@/lib/imageUpload';
import { ObjectId } from 'mongodb';
import { NextResponse } from 'next/server';

export const config = {
  api: {
    bodyParser: false,
    maxBodySize: '50mb',
  },
};

export async function POST(request) {
  try {
    // Verify the admin user
    const admin = await verifyAdminToken(request);
    if (!admin || admin.admintype !== 'Freelancer') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    
    // Get the product ID
    const productId = formData.get('productId');
    if (!productId) {
      return NextResponse.json({ message: 'Produkt ID mangler' }, { status: 400 });
    }

    // Connect to the database
    const { db } = await connectToDatabase();
    
    // Find the product
    const product = await db.collection('products').findOne({
      _id: new ObjectId(productId),
      createdBy: admin.username
    });
    
    if (!product) {
      return NextResponse.json({ message: 'Produkt ikke fundet eller du har ikke adgang til det' }, { status: 404 });
    }

    // Retrieve all images from the form data
    const imageEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('image'));
    
    if (imageEntries.length === 0) {
      return NextResponse.json({ message: 'Ingen billeder uploadet' }, { status: 400 });
    }

    // Process each image
    const uploadResults = [];
    const existingScreenshots = product.screenshotUrls || [];
    
    for (const [, imageFile] of imageEntries) {
      try {
        // Upload the image
        const { fileId, url, contentType } = await uploadImage(imageFile);
        
        // Add to results
        uploadResults.push({
          fileId,
          filename: imageFile.name,
          contentType,
          url
        });
      } catch (error) {
        console.error('Image upload error:', error);
        // Create a placeholder for failed uploads
        uploadResults.push({
          fileId: `placeholder_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
          filename: imageFile.name,
          contentType: imageFile.type,
          url: null
        });
      }
    }
    
    // Update the product with the new screenshots
    const updatedScreenshotUrls = [...existingScreenshots, ...uploadResults];
    
    await db.collection('products').updateOne(
      { _id: new ObjectId(productId) },
      { 
        $set: { 
          screenshotUrls: updatedScreenshotUrls,
          updatedAt: new Date() 
        } 
      }
    );
    
    return NextResponse.json({ 
      message: `${uploadResults.length} billeder uploadet succesfuldt`, 
      uploadResults 
    });
    
  } catch (error) {
    console.error('Error in upload-additional-images API route:', error);
    return NextResponse.json({ message: error.message || 'Der opstod en fejl' }, { status: 500 });
  }
} 