'use client';

import React, { useState, useEffect } from 'react';
import { FaArrowLeft, FaWallet, FaSpinner } from 'react-icons/fa';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import CashoutModal from '../../../components/CashoutModal';

export default function CashoutPage() {
  const [balance, setBalance] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    const fetchBalance = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/users/balance');
        
        if (!response.ok) {
          // If the response is 403, it means the user is not an admin
          if (response.status === 403) {
            setBalance(null);
            setLoading(false);
            return;
          }
          
          throw new Error('Failed to fetch balance');
        }
        
        const data = await response.json();
        setBalance(data.balance);
      } catch (err) {
        console.error('Error fetching balance:', err);
        setError('Could not load balance');
      } finally {
        setLoading(false);
      }
    };

    fetchBalance();
  }, []);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-28 pb-20">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Back button */}
          <div className="mb-6">
            <Link href="/profile?tab=transactions" className="inline-flex items-center text-indigo-600 hover:text-indigo-800 transition-colors group">
              <FaArrowLeft className="mr-2 transition-all duration-300 ease-in-out group-hover:-translate-x-1 group-hover:scale-110" />
              <span>Tilbage til transaktioner</span>
            </Link>
          </div>

          {/* Page header */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
              <h1 className="text-xl font-medium text-white flex items-center">
                <FaWallet className="mr-2" /> 
                Udbetal Balance
              </h1>
            </div>
            
            <div className="p-6">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <FaSpinner className="animate-spin h-8 w-8 text-indigo-600 mr-3" />
                  <span className="text-gray-600">Indlæser balance...</span>
                </div>
              ) : error ? (
                <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
                  <p className="text-red-700">{error}</p>
                  <button 
                    onClick={() => window.location.reload()}
                    className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                  >
                    Prøv igen
                  </button>
                </div>
              ) : (
                <div className="text-center">
                  <div className="text-4xl font-bold text-gray-800 mb-2">
                    {balance !== null ? balance.toLocaleString('da-DK') : 0} <span className="text-gray-500 text-2xl">DKK</span>
                  </div>
                  <p className="text-gray-600 mb-8">Din nuværende balance</p>
                  
                  {balance !== null && balance >= 50 ? (
                    <button
                      onClick={handleOpenModal}
                      className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all"
                    >
                      Udbetal balance
                    </button>
                  ) : (
                    <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4 rounded text-left">
                      <h3 className="text-yellow-800 font-medium">Minimum udbetaling ikke opfyldt</h3>
                      <p className="text-yellow-700 mt-1">
                        Du skal have minimum 50 DKK på din balance for at kunne foretage en udbetaling.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Information section */}
          <div className="bg-white rounded-xl shadow-md p-6">
            <h2 className="text-lg font-medium text-gray-800 mb-4">Om udbetaling</h2>
            <ul className="space-y-3 text-gray-600">
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 mr-3 flex-shrink-0 mt-0.5">1</span>
                <span>Minimumsbeløb for udbetaling er 50 DKK.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 mr-3 flex-shrink-0 mt-0.5">2</span>
                <span>Udbetalinger behandles manuelt inden for 1-3 hverdage.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 mr-3 flex-shrink-0 mt-0.5">3</span>
                <span>MobilePay er for øjeblikket den eneste tilgængelige udbetalingsmetode.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-indigo-100 text-indigo-600 mr-3 flex-shrink-0 mt-0.5">4</span>
                <span>Kontakt os direkte via Discord, hvis du har spørgsmål til din udbetaling.</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Cashout Modal */}
      <CashoutModal 
        isOpen={isModalOpen} 
        onClose={handleCloseModal}
        balance={balance || 0}
      />
    </div>
  );
} 