import { ObjectId } from 'mongodb';

/**
 * Verified user model interface
 */
export interface IVerified {
  _id?: ObjectId;
  discordId: string;      // The Discord user ID
  discordName: string;    // The Discord username
  isVerified: boolean;    // Whether the user is verified
  verifiedAt?: Date;      // When the user was verified
  createdAt: Date;        // When the record was created
  updatedAt: Date;        // When the record was last updated
}

/**
 * Collection name for verified users
 */
export const VERIFIED_COLLECTION = 'verified';

/**
 * Creates the verified collection indexes if they don't exist
 * @param db MongoDB database instance
 */
export async function createVerifiedIndexes(db) {
  const collection = db.collection(VERIFIED_COLLECTION);
  
  // Create indexes
  await collection.createIndex({ discordId: 1 }, { unique: true });
  await collection.createIndex({ discordName: 1 });
  await collection.createIndex({ isVerified: 1 });
}

/**
 * Example MongoDB schema for the verified collection
 * {
 *   discordId: "123456789012345678",
 *   discordName: "username#1234",
 *   isVerified: true,
 *   verifiedAt: ISODate("2023-04-15T12:30:45.000Z"),
 *   createdAt: ISODate("2023-04-15T12:30:45.000Z"),
 *   updatedAt: ISODate("2023-04-15T12:30:45.000Z")
 * }
 */ 