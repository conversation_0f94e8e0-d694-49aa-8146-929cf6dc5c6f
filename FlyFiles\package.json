{"name": "flyfiles", "version": "1.0.0", "description": "Danish file sharing platform similar to WeTransfer", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.5", "next-auth": "5.0.0-beta.25", "mongodb": "^6.10.0", "lucide-react": "^0.460.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4"}, "keywords": ["file-sharing", "danish", "nextjs", "typescript", "mongodb", "wetransfer-clone"], "author": "FlyFiles Team", "license": "MIT"}