'use client';

import React, { useState, useEffect } from 'react';
import { FaH<PERSON>t, FaRegHeart } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';

interface FavoriteButtonProps {
  productId: string;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  size?: 'small' | 'medium' | 'large';
}

const FavoriteButton: React.FC<FavoriteButtonProps> = ({ 
  productId, 
  position = 'top-right',
  size = 'medium'
}) => {
  const [isFavorite, setIsFavorite] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const { user } = useAuth();

  // Position classes based on the position prop
  const positionClasses = {
    'top-right': 'top-3 right-3',
    'top-left': 'top-3 left-3',
    'bottom-right': 'bottom-3 right-3',
    'bottom-left': 'bottom-3 left-3',
  };

  // Size classes
  const sizeClasses = {
    small: 'w-7 h-7 text-lg',
    medium: 'w-10 h-10 text-xl',
    large: 'w-12 h-12 text-2xl',
  };

  // Check if the product is already favorited
  useEffect(() => {
    if (!productId) return;
    
    const checkFavoriteStatus = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/favorites?productId=${productId}`, {
          method: 'OPTIONS',
        });
        
        if (response.ok) {
          const data = await response.json();
          setIsFavorite(data.isFavorite);
        }
      } catch (error) {
        console.error('Error checking favorite status:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (user) {
      checkFavoriteStatus();
    } else {
      setIsLoading(false);
    }
  }, [productId, user]);

  // Toggle favorite status
  const toggleFavorite = async () => {
    // If user is not logged in, show login prompt
    if (!user) {
      setShowLoginPrompt(true);
      setTimeout(() => setShowLoginPrompt(false), 3000);
      return;
    }
    
    try {
      const action = isFavorite ? 'remove' : 'add';
      
      const response = await fetch('/api/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          action,
        }),
      });
      
      if (response.ok) {
        setIsFavorite(!isFavorite);
        
        // Animate heart
        const heartElement = document.getElementById(`heart-${productId}`);
        if (heartElement && !isFavorite) {
          heartElement.classList.add('scale-125');
          setTimeout(() => {
            heartElement.classList.remove('scale-125');
          }, 300);
        }
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  return (
    <div className={`absolute ${positionClasses[position]} z-10`}>
      <motion.button
        id={`heart-${productId}`}
        onClick={toggleFavorite}
        className={`${sizeClasses[size]} flex items-center justify-center rounded-full bg-white bg-opacity-80 hover:bg-opacity-100 shadow-md transition-all duration-300 focus:outline-none`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title={isFavorite ? "Fjern fra favoritter" : "Tilføj til favoritter"}
      >
        {isLoading ? (
          <div className="animate-pulse">
            <FaRegHeart className="text-gray-300" />
          </div>
        ) : isFavorite ? (
          <FaHeart className="text-red-500" />
        ) : (
          <FaRegHeart className="text-gray-400 hover:text-red-500 transition-colors" />
        )}
      </motion.button>
      
      <AnimatePresence>
        {showLoginPrompt && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.8 }}
            transition={{ duration: 0.2 }}
            className="absolute mt-2 p-2 bg-white rounded-md shadow-lg text-sm text-gray-700 w-48 right-0"
          >
            Log ind med Discord for at tilføje til favoritter
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default FavoriteButton; 