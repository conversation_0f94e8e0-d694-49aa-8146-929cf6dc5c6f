import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';

export async function POST(request) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID and avatar from the user
    const discordId = session.user.id;

    // Get avatar information from NextAuth session
    const avatarUrl = session.user.image;
    const avatarHash = avatarUrl ? avatarUrl.split('/').pop().split('.')[0] : null;

    // Get the Discord username
    const username = session.user.name;
    
    if (!discordId) {
      console.log('No Discord ID found in user session:', session.user);
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }

    console.log('User session:', session.user);
    
    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Find if user already exists in adminusers collection
    const existingUser = await db.collection('adminusers').findOne({ 
      discordUserId: discordId 
    });
    
    let adminUserResult = null;
    
    if (existingUser) {
      console.log('Updating user:', existingUser.username);
      console.log('Old avatar hash:', existingUser.avatar);
      console.log('New avatar hash:', avatarHash);
      
      // Create update document with available fields
      const updateDoc = { 
        avatar: avatarHash 
      };
      
      // Add avatar URL if available
      if (avatarUrl) {
        updateDoc.avatarUrl = avatarUrl;
        console.log('Setting avatar URL:', avatarUrl);
      }
      
      // Update the existing user with the Discord avatar information
      const updateResult = await db.collection('adminusers').updateOne(
        { discordUserId: discordId },
        { $set: updateDoc }
      );
      
      adminUserResult = {
        success: true,
        message: 'User avatar updated successfully',
        avatarHash,
        avatarUrl,
        discordId,
        username: existingUser.username,
        updated: updateResult.modifiedCount > 0
      };
    } else {
      console.log('User not found in database with discordUserId:', discordId);
      
      // User doesn't exist yet - this could be a first-time login
      adminUserResult = {
        success: false,
        message: 'User not found in database',
        discordId,
        user: session.user
      };
    }
    
    // Now check if the user exists in the users collection (this is a separate collection from adminusers)
    const existingGeneralUser = await db.collection('users').findOne({
      discordUserId: discordId
    });
    
    let userResult = null;
    
    if (!existingGeneralUser) {
      console.log('Adding user to general users collection:', username);
      
      // Create a new user document
      const newUser = {
        discordUserId: discordId,
        username: username,
        avatar: avatarHash,
        avatarUrl: avatarUrl,
        createdAt: new Date(),
        lastLogin: new Date()
      };
      
      // Insert the new user into the users collection
      const insertResult = await db.collection('users').insertOne(newUser);
      
      userResult = {
        success: true,
        message: 'User added to general users collection',
        insertedId: insertResult.insertedId
      };
    } else {
      console.log('User found in general users collection, updating last login');
      
      // Update existing user's last login time and possibly avatar
      const updateDoc = {
        lastLogin: new Date(),
        avatar: avatarHash,
        avatarUrl: avatarUrl
      };
      
      // Check if username has changed
      if (username && username !== existingGeneralUser.username) {
        updateDoc.username = username;
        console.log('Updating username from', existingGeneralUser.username, 'to', username);
      }
      
      const updateResult = await db.collection('users').updateOne(
        { discordUserId: discordId },
        { $set: updateDoc }
      );
      
      userResult = {
        success: true,
        message: 'User updated in general users collection',
        updated: updateResult.modifiedCount > 0
      };
    }
    
    // Return both admin and general user results
    return NextResponse.json({
      adminUser: adminUserResult,
      user: userResult
    });
    
  } catch (error) {
    console.error('Error updating Discord avatar:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 