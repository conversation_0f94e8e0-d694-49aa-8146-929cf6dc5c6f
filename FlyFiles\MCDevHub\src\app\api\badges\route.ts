import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth.js';
import { connectToDatabase } from '@/lib/mongodb.js';
import { BadgeAwardService } from '@/lib/services/badgeAwardService';

/**
 * GET /api/badges - Get user's badges and progress
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }

    // Connect to database
    const { db } = await connectToDatabase();
    const badgeService = new BadgeAwardService(db);

    // Get query parameters
    const url = new URL(request.url);
    const includeProgress = url.searchParams.get('includeProgress') === 'true';

    if (includeProgress) {
      // Get badge progress (for profile page with progress bars)
      const progress = await badgeService.getBadgeProgress(discordId);
      
      return NextResponse.json({
        success: true,
        badges: progress
      });
    } else {
      // Get only earned badges (for display)
      const badges = await badgeService.getUserBadgesWithDefinitions(discordId);

      // Convert array to object format expected by BadgeDisplay component
      const badgeObject: Record<string, any> = {};
      badges.forEach((badge: any) => {
        badgeObject[badge.id] = {
          ...badge,
          earned: true // These are already earned badges
        };
      });

      return NextResponse.json({
        success: true,
        badges: badgeObject
      });
    }

  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/badges - Trigger badge check for current user
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }

    // Connect to database
    const { db } = await connectToDatabase();
    const badgeService = new BadgeAwardService(db);

    // Get request body for action context (optional)
    const body = await request.json().catch(() => ({}));
    const action = body.action || 'manual_check';

    // Check and award new badges
    const newBadges = await badgeService.checkAndAwardBadges(discordId);

    return NextResponse.json({
      success: true,
      message: newBadges.length > 0 ? 
        `Optjent ${newBadges.length} nye badges!` : 
        'Ingen nye badges optjent',
      newBadges: newBadges,
      count: newBadges.length
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
