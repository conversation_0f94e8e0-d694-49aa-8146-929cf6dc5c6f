import { NextResponse } from 'next/server';
import { ObjectId } from 'mongodb';
import { connectToDatabase } from '@/lib/mongodb';

/**
 * API endpoint to check if a file exists in MongoDB GridFS
 */
export async function GET(request, context) {
  try {
    // Get the file ID from the route parameter
    const params = context.params;
    const id = params?.id;

    if (!id) {
      return NextResponse.json(
        { error: 'Invalid file ID' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Find the file in fs.files collection
    try {
      const fileInfo = await db.collection('fs.files').findOne({ 
        _id: new ObjectId(id)
      });

      if (!fileInfo) {
        return NextResponse.json(
          { error: 'File not found in GridFS', id, exists: false },
          { status: 404 }
        );
      }

      // Return file info
      return NextResponse.json({
        exists: true,
        id,
        file: {
          _id: fileInfo._id.toString(),
          filename: fileInfo.filename,
          contentType: fileInfo.metadata?.contentType,
          originalName: fileInfo.metadata?.originalName,
          length: fileInfo.length,
          uploadDate: fileInfo.uploadDate
        }
      });
    } catch (err) {
      return NextResponse.json(
        { error: 'Error checking file', id, exists: false, message: err.message },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error checking file in GridFS:', error);
    return NextResponse.json(
      { error: 'Server error', exists: false },
      { status: 500 }
    );
  }
} 