import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { verifyAdminToken } from '@/lib/auth';
import { ObjectId } from 'mongodb';
import { uploadImage, deleteImage } from '@/lib/imageUpload';

// Set higher body size limit for file uploads (50MB)
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
  },
};

export async function POST(request) {
  try {
    // Verify the admin user
    const admin = await verifyAdminToken();
    if (!admin) {
      return NextResponse.json(
        { message: 'Ikke autoriseret' },
        { status: 401 }
      );
    }

    // Only freelancers can update products
    if (admin.admintype !== 'Freelancer') {
      return NextResponse.json(
        { message: 'Kun freelancere kan opdatere produkter' },
        { status: 403 }
      );
    }

    // Parse the multipart form data
    const formData = await request.formData();
    
    // Extract product ID
    const productId = formData.get('productId');
    
    if (!productId || !ObjectId.isValid(productId)) {
      return NextResponse.json(
        { message: 'Ugyldig produkt-id' },
        { status: 400 }
      );
    }
    
    // Get the image and index
    const image = formData.get('image');
    const imageIndexStr = formData.get('imageIndex');
    const imageIndex = parseInt(imageIndexStr, 10);

    // Validate image and index
    if (!image) {
      return NextResponse.json(
        { message: 'Ingen billede angivet' },
        { status: 400 }
      );
    }
    
    if (isNaN(imageIndex) || imageIndex < 0) {
      return NextResponse.json(
        { message: 'Ugyldig billedindeks' },
        { status: 400 }
      );
    }

    // Connect to database
    const { db } = await connectToDatabase();
    
    // Verify the product exists and belongs to this admin
    const product = await db.collection('products').findOne({
      _id: new ObjectId(productId),
      createdBy: admin.username
    });
    
    if (!product) {
      return NextResponse.json(
        { message: 'Produkt ikke fundet eller du har ikke tilladelse til at redigere det' },
        { status: 404 }
      );
    }
    
    // Check if the image index is valid
    if (!product.screenshotUrls || imageIndex >= product.screenshotUrls.length) {
      return NextResponse.json(
        { message: 'Ugyldigt billedindeks for dette produkt' },
        { status: 400 }
      );
    }
    
    // Get the current image path to replace
    const currentImageData = product.screenshotUrls[imageIndex];
    
    // Try to delete the old image if possible
    if (currentImageData && currentImageData.fileId) {
      try {
        await deleteImage(currentImageData.fileId);
      } catch (deleteError) {
        console.error('Error deleting old image, continuing anyway:', deleteError);
        // We'll continue even if delete fails
      }
    }
    
    // Upload the new image
    const buffer = await image.arrayBuffer();
    const filename = `${productId}_screenshot_${image.name}`;
    
    // Upload to our custom API
    const result = await uploadImage(
      Buffer.from(buffer),
      'screenshots',
      filename,
      image.type
    );
    
    if ('error' in result) {
      console.error('API upload error:', result.error);
      
      // Instead of failing, create a placeholder URL that won't actually load an image
      // but will show in the UI that something went wrong
      console.log('Creating placeholder URL for failed upload in production');
      
      // Use a static placeholder path that would be served from your main domain
      const staticPath = `/static/placeholders/screenshot-${Date.now()}.jpg`;
      
      // Create placeholder image data
      const placeholderData = {
        fileId: `placeholder_${productId}_${image.name}`,
        filename: image.name,
        contentType: image.type,
        url: staticPath,
        isPlaceholder: true,
        uploadError: result.error
      };
      
      // Create a copy of the screenshots array
      const updatedScreenshots = [...product.screenshotUrls];
      
      // Replace the image at the specified index
      updatedScreenshots[imageIndex] = placeholderData;
      
      // Update only the specific screenshot in the product document
      await db.collection('products').updateOne(
        { _id: new ObjectId(productId) },
        { 
          $set: { 
            [`screenshotUrls.${imageIndex}`]: placeholderData,
            updatedAt: new Date()
          } 
        }
      );
      
      // Return success with a warning
      return NextResponse.json({ 
        success: true,
        message: 'Produktbillede opdateret med placeholder (da upload fejlede)',
        warning: `Billedet blev ikke uploadet til serveren: ${result.error}. Et placeholder-billede blev brugt i stedet.`,
        isPlaceholder: true
      });
      
      // In production, return an error
      /*
      return NextResponse.json(
        { 
          success: false,
          message: `Fejl ved upload af billede: ${result.error}`,
          detailedError: result.error
        },
        { status: 500 }
      );
      */
    }
    
    // Create the new image data
    const newImageData = {
      fileId: result.path,
      filename: image.name,
      contentType: image.type,
      url: result.url
    };
    
    // Create a copy of the screenshots array
    const updatedScreenshots = [...product.screenshotUrls];
    
    // Replace the image at the specified index
    updatedScreenshots[imageIndex] = newImageData;
    
    // Update only the specific screenshot in the product document
    await db.collection('products').updateOne(
      { _id: new ObjectId(productId) },
      { 
        $set: { 
          [`screenshotUrls.${imageIndex}`]: newImageData,
          updatedAt: new Date()
        } 
      }
    );
    
    // Return success response
    return NextResponse.json({ 
      success: true,
      message: 'Produktbillede opdateret succesfuldt'
    });
    
  } catch (error) {
    console.error('Error updating product image:', error);
    return NextResponse.json(
      { 
        success: false,
        message: 'Der opstod en fejl under opdatering af produktbillede: ' + error.message 
      },
      { status: 500 }
    );
  }
} 