'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>aUser, <PERSON>a<PERSON>ock, <PERSON>aD<PERSON>rd, FaExclamationTriangle } from 'react-icons/fa';

interface Invitation {
  admintype: string;
  allowedcases: string;
  discordUserId: string;
  description: string;
  createdBy: string;
  expiresAt: string;
}

export default function SignupPage() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [verifying, setVerifying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [invitation, setInvitation] = useState<Invitation | null>(null);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: ''
  });
  const [formErrors, setFormErrors] = useState({
    username: '',
    password: '',
    confirmPassword: ''
  });

  useEffect(() => {
    const verifyToken = async () => {
      try {
        if (!params.token) {
          throw new Error('Ugyldigt token');
        }
        
        const response = await fetch(`/api/admin/verify-invite?token=${params.token}`);
        
        if (!response.ok) {
          const data = await response.json();
          throw new Error(data.message || 'Ugyldigt eller udløbet invitationslink (linket udløber efter 10 minutter)');
        }
        
        const data = await response.json();
        setInvitation(data.invitation);
        setLoading(false); // Add this line to set loading to false after verification
      } catch (error) {
        console.error('Error verifying invitation:', error);
        setError(error.message || 'Kunne ikke verificere invitationslink');
        setLoading(false); // Add this line to set loading to false on error
      }
    };

    // Initial verification
    verifyToken();

    // Set up interval to check expiration
    const interval = setInterval(() => {
      if (invitation) {
        const expirationDate = new Date(invitation.expiresAt);
        const now = new Date();
        if (now > expirationDate) {
          setError('Invitationslinket er udløbet');
          clearInterval(interval);
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [params.token, invitation]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear errors when typing
    setFormErrors(prev => ({
      ...prev,
      [name]: ''
    }));
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = { ...formErrors };
    
    if (!formData.username.trim()) {
      newErrors.username = 'Brugernavn er påkrævet';
      valid = false;
    } else if (formData.username.length < 3) {
      newErrors.username = 'Brugernavn skal være mindst 3 tegn';
      valid = false;
    }
    
    if (!formData.password) {
      newErrors.password = 'Adgangskode er påkrævet';
      valid = false;
    } else if (formData.password.length < 8) {
      newErrors.password = 'Adgangskode skal være mindst 8 tegn';
      valid = false;
    }
    
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Adgangskoder matcher ikke';
      valid = false;
    }
    
    setFormErrors(newErrors);
    return valid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setVerifying(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/create-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: params.token,
          username: formData.username,
          password: formData.password
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || 'Kunne ikke oprette konto');
      }

      // Success, redirect to login
      router.push('/admin/login?registered=true');
    } catch (error) {
      console.error('Error creating account:', error);
      setError(error.message || 'Der opstod en fejl. Prøv igen senere.');
    } finally {
      setVerifying(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-gray-600">Verificerer invitationslink...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full border border-gray-100">
          <div className="flex flex-col items-center space-y-6">
            <div className="relative w-20 h-20">
              <div className="absolute inset-0 bg-red-50 rounded-full blur-sm"></div>
              <div className="relative flex items-center justify-center h-20 w-20 rounded-full bg-red-50">
                <FaExclamationTriangle className="h-10 w-10 text-red-600" />
              </div>
            </div>
            <div className="text-center space-y-3">
              <h1 className="text-3xl font-bold text-gray-900">Ugyldigt Link</h1>
              <p className="text-gray-600 text-sm leading-relaxed max-w-prose">
                {error}
              </p>
            </div>
            <div className="w-full pt-6">
              <Link
                href="/admin/login"
                className="group relative flex items-center justify-center w-full px-6 py-3.5 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)] transform hover:-translate-y-0.5 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <span>Gå til login</span>
                <svg 
                  className="w-5 h-5 ml-2 -mr-1"
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-600 to-indigo-800 flex flex-col items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl p-8 max-w-lg w-full">
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 text-blue-600 mb-4">
            <FaUser className="h-8 w-8" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-1">Opret din konto</h1>
          <p className="text-gray-600 text-sm">Du er blevet inviteret til at deltage som freelancer hos MCDevHub</p>
        </div>
        
        {invitation && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-200">
            <h2 className="text-sm font-medium text-gray-700 mb-2">Din invitation indeholder følgende detaljer:</h2>
            <div className="space-y-2">
              <div className="flex items-center">
                <span className="text-xs font-medium text-gray-500 w-28 cursor-default">Admin Type:</span>
                <span className="text-sm font-medium px-2.5 py-0.5 rounded-full bg-blue-100 text-blue-800 border border-blue-200 cursor-default">
                  {invitation.admintype}
                </span>
              </div>
              <div className="flex items-center">
                <span className="text-xs font-medium text-gray-500 w-28 cursor-default">Tilladte formularer:</span>
                <div className="flex flex-wrap gap-1">
                  {invitation.allowedcases.split(',').map((caseType, index) => (
                    <span key={index} className="text-xs font-medium px-2 py-0.5 rounded-full bg-green-100 text-green-800 border border-green-200 cursor-default">
                      {caseType.trim()}
                    </span>
                  ))}
                </div>
              </div>
              <div className="flex items-center">
                <span className="text-xs font-medium text-gray-500 w-28">Inviteret af:</span>
                <span className="text-sm font-medium text-gray-800 cursor-default">{invitation.createdBy}</span>
              </div>
              <div className="flex items-center">
                <span className="text-xs font-medium text-gray-500 w-28">Udløber:</span>
                <span className="text-sm font-medium text-gray-800 cursor-default">
                  {(() => {
                    const expirationDate = new Date(invitation.expiresAt);
                    const now = new Date();
                    const diff = expirationDate.getTime() - now.getTime();
                    
                    if (diff <= 0) {
                      return 'Udløbet';
                    }
                    
                    const minutes = Math.floor(diff / (1000 * 60));
                    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
                    
                    return `${minutes} minutter og ${seconds} sekunder`;
                  })()}
                </span>
              </div>
              {invitation.description && (
                <div className="pt-2">
                  <span className="text-xs font-medium text-gray-500 block mb-1">Beskrivelse:</span>
                  <p className="text-sm text-gray-700 bg-white p-2 rounded border border-gray-100">
                    {invitation.description}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-100 text-red-700 px-4 py-3 rounded-lg mb-4">
              {error}
            </div>
          )}
          
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
              Brugernavn
            </label>
            <div className="relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaUser className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                name="username"
                id="username"
                className={`block w-full pl-10 py-3 pr-3 border ${formErrors.username ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2`}
                placeholder="Vælg et brugernavn"
                value={formData.username}
                onChange={handleChange}
              />
            </div>
            {formErrors.username && (
              <p className="mt-1 text-sm text-red-600">{formErrors.username}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Adgangskode
            </label>
            <div className="relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaLock className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="password"
                name="password"
                id="password"
                className={`block w-full pl-10 py-3 pr-3 border ${formErrors.password ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2`}
                placeholder="Vælg en stærk adgangskode"
                value={formData.password}
                onChange={handleChange}
              />
            </div>
            {formErrors.password && (
              <p className="mt-1 text-sm text-red-600">{formErrors.password}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
              Bekræft adgangskode
            </label>
            <div className="relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaLock className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="password"
                name="confirmPassword"
                id="confirmPassword"
                className={`block w-full pl-10 py-3 pr-3 border ${formErrors.confirmPassword ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2`}
                placeholder="Gentag adgangskode"
                value={formData.confirmPassword}
                onChange={handleChange}
              />
            </div>
            {formErrors.confirmPassword && (
              <p className="mt-1 text-sm text-red-600">{formErrors.confirmPassword}</p>
            )}
          </div>
          
          <div className="pt-2">
            <button
              type="submit"
              disabled={verifying}
              className={`w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${verifying ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
            >
              {verifying ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Opretter konto...
                </>
              ) : (
                'Opret konto'
              )}
            </button>
          </div>
        </form>
        
        <div className="mt-6 border-t border-gray-200 pt-4">
          <div className="text-sm text-center">
            <p className="text-gray-600">
              Ved at oprette en konto bekræfter du, at du accepterer vores <Link href="/tos" className="text-blue-600 hover:underline">vilkår</Link> og <Link href="/privatlivspolitik" className="text-blue-600 hover:underline">privatlivspolitik</Link>.
            </p>
          </div>
        </div>
      </div>
      
      <div className="mt-4 text-center text-white text-sm">
        <p>Allerede medlem? <Link href="/admin/login" className="font-medium hover:underline">Log ind her</Link></p>
      </div>
    </div>
  );
} 