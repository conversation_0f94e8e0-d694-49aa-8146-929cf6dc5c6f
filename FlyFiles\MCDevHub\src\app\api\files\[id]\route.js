import { NextResponse } from 'next/server';
import { getImageUrl } from '@/lib/imageUtils';

/**
 * API endpoint to retrieve files from our backend storage
 * Access files at /api/files/[fileId]
 * The fileId parameter is now the path in our backend storage
 */
export async function GET(request, context) {
  try {
    // Await the params object
    const params = await context.params;
    const id = params?.id;

    if (!id) {
      return NextResponse.json(
        { error: 'Invalid file ID' },
        { status: 400 }
      );
    }

    // Get the full URL from our backend
    const fileUrl = getImageUrl(id);
    
    // Redirect to the actual file on our backend
    return NextResponse.redirect(fileUrl);
  } catch (error) {
    console.error('Error downloading file:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 