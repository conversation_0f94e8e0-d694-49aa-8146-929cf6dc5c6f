import { NextResponse } from 'next/server';
import { MongoClient, ObjectId } from 'mongodb';

export const runtime = 'nodejs'; // Use Node.js runtime

// MongoDB configuration
if (!process.env.MONGODB_URI) {
  throw new Error('Please add your Mongo URI to .env.local');
}

const uri = process.env.MONGODB_URI;
const options = {};

// Set up MongoDB client with proper connection pooling
let client;
let clientPromise;

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable to preserve
  // the connection across hot reloads
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri, options);
    global._mongoClientPromise = client.connect();
  }
  clientPromise = global._mongoClientPromise;
} else {
  // In production mode, create a new client for each request
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

/**
 * GET handler for retrieving a single submission by ID
 */
export async function GET(request, context) {
  try {
    // Connect to the database
    const client = await clientPromise;
    const db = client.db("codehub");
    
    // Properly await params before accessing it
    const params = await context.params;
    const id = params.id;
    
    if (!id) {
      return NextResponse.json({ 
        success: false, 
        message: 'Missing submission ID' 
      }, { status: 400 });
    }
    
    // Find the submission by ID
    const submission = await db.collection("form_submissions")
      .findOne({ _id: new ObjectId(id) });
    
    // Return 404 if not found
    if (!submission) {
      return NextResponse.json({ 
        success: false, 
        message: 'Submission not found' 
      }, { status: 404 });
    }
    
    // Return the submission data
    return NextResponse.json(submission);
    
  } catch (error) {
    console.error('Error fetching submission:', error);
    
    // Return error response
    return NextResponse.json({ 
      success: false, 
      message: 'Error fetching submission',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 