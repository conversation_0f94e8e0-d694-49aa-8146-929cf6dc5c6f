import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { getUserVerificationStatus, setUserVerification } from '@/lib/services/verified-service';

// GET /api/verified - Check verification status
export async function GET(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Get verification status from MongoDB
    const verificationStatus = await getUserVerificationStatus(discordId);
    
    return NextResponse.json({
      discordId,
      discordName: session.user.name,
      isVerified: verificationStatus?.isVerified || false,
      verifiedAt: verificationStatus?.verifiedAt || null,
    });
  } catch (error) {
    console.error('Error checking verification status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/verified - Set verification status (admin only)
export async function POST(request: NextRequest) {
  try {
    // In a real app, you'd implement admin authentication here
    // For now, we'll just check for a secret key in the request
    const secret = request.headers.get('x-admin-secret');
    const ADMIN_SECRET = process.env.ADMIN_API_SECRET || 'changeme';
    
    if (secret !== ADMIN_SECRET) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Parse the request body
    const body = await request.json();
    const { discordId, discordName, isVerified } = body;
    
    if (!discordId || !discordName) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Update verification status
    const success = await setUserVerification(
      discordId,
      discordName,
      isVerified === true
    );
    
    if (success) {
      return NextResponse.json({ 
        success: true,
        discordId,
        discordName,
        isVerified
      });
    } else {
      return NextResponse.json({ error: 'Failed to update verification status' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error setting verification status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 