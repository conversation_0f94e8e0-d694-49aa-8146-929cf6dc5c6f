import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Connect to database
    const { db } = await connectToDatabase();
    
    // Check if user is an admin/freelancer - try both string and number formats
    let adminUser = await db.collection('adminusers').findOne({
      discordUserId: discordId
    });

    // If not found as string, try as number
    if (!adminUser) {
      adminUser = await db.collection('adminusers').findOne({
        discordUserId: parseInt(discordId)
      });
    }

    if (!adminUser) {
      return NextResponse.json({ error: 'User is not an admin/freelancer' }, { status: 403 });
    }
    
    // Get the user's balance from the database (default to 0 if not found)
    const balanceRecord = await db.collection('user_balances').findOne({ 
      userId: discordId 
    });
    
    // If balance record doesn't exist, create one with 0 balance
    if (!balanceRecord) {
      await db.collection('user_balances').insertOne({
        userId: discordId,
        balance: 0,
        lastUpdated: new Date()
      });
    }
    
    return NextResponse.json({
      balance: balanceRecord?.balance || 0,
      currency: 'DKK',
      lastUpdated: balanceRecord?.lastUpdated || new Date()
    });
    
  } catch (error) {
    console.error('Error fetching user balance:', error);
    return NextResponse.json({
      error: 'Failed to fetch user balance',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
} 