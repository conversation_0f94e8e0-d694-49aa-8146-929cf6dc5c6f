# Admin Dashboard Authentication System

Dette er dokumentationen for admin dashboard authentication systemet implementeret i McDevHub projektet.

## Oversigt

Systemet giver mulighed for at:
- Log ind som admin med brugernavn og password
- Få adgang til admin dashboard baseret på rettigheder
- Se indsendelser filtreret efter de typer, som administratoren har tilladelse til at se
- Logge ud af admin-sessionen

## Teknisk Implementering

### Admin Model

Systemet bruger en MongoDB collection kaldet `Admins` med følgende struktur:

```js
{
  username: String,       // Admin brugernavn
  password: String,       // Admin password (plain text)
  admintype: String,      // Admin rank/type (fx "super", "partner", "content")
  allowedcases: String    // Comma-separated liste over tilladte formulartyper (fx "contact,partner,custom-order")
}
```

### Authentication Flow

1. **Login proces**:
   - Admin indtaster brugernavn og password på `/admin/login` siden
   - API route på `/api/admin/login` validerer credentials mod MongoDB
   - Ved succes oprettes en JWT token med admin information og gemmes i en cookie

2. **Session håndtering**:
   - JWT token indeholder admin information (username, admintype, allowedcases)
   - Middleware kontrollerer token for alle admin routes
   - Uautoriserede brugere omdirigeres til login-siden

3. **Permission system**:
   - Admin kan kun se indsendelser baseret på deres `allowedcases` værdier
   - Admin dashboardet filtrerer indsendelser baseret på disse tilladelser
   - Detaljerede visningssider kontrollerer også tilladelser før visning af indhold

4. **Logout håndtering**:
   - Logout API rydder admin token cookie
   - Brugeren omdirigeres til login siden

## Vigtige Filer

- `/admin/login/page.tsx` - Admin login side
- `/admin/dashboard/page.tsx` - Admin dashboard med filteret visning baseret på tilladelser
- `/admin/submissions/[id]/page.tsx` - Detaljeret visning af indsendelser med tilladelseskontrol
- `/components/LogoutButton.tsx` - Genbrugeligt logout knap komponent
- `/api/admin/login/route.js` - Login API endpoint
- `/api/admin/logout/route.js` - Logout API endpoint
- `/api/admin/me/route.js` - Endpoint for at hente den aktuelle admin information
- `/middleware.js` - Middleware til beskyttelse af admin routes

## Sikkerhedsovervejelser

- JWT token er krypteret med en hemmelig nøgle defineret i `.env.local`
- Token udløber efter 24 timer for at begrænse sessionsvarigheden
- Der anvendes HttpOnly cookies til lagring af token for at forhindre JavaScript-adgang
- Middleware sikrer, at kun autoriserede brugere får adgang til admin routes

## Fremtidige Forbedringer

- Implementering af password hashing i stedet for plain text
- Tilføjelse af to-faktor autentificering
- Logging af admin handlinger for audit trail
- Rate limiting for login forsøg til at forhindre brute force angreb 