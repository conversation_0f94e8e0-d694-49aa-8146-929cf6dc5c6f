import { getProfileDataAccess, filterProfileData, sanitizeProfileInput } from '../profilePermissions';

describe('Profile Permissions', () => {
  describe('getProfileDataAccess', () => {
    it('should give full access to profile owner', () => {
      const access = getProfileDataAccess('user123', 'user123', false);
      
      expect(access.canViewBasicInfo).toBe(true);
      expect(access.canViewPurchases).toBe(true);
      expect(access.canViewFavorites).toBe(true);
      expect(access.canViewNotifications).toBe(true);
      expect(access.canViewPrivateProfile).toBe(true);
      expect(access.canEditProfile).toBe(true);
      expect(access.canViewBadgeProgress).toBe(true);
      expect(access.canViewSocialLinks).toBe(true);
      expect(access.canViewAvailability).toBe(true);
    });

    it('should give limited access to logged-in users viewing others', () => {
      const access = getProfileDataAccess('user123', 'user456', false);
      
      expect(access.canViewBasicInfo).toBe(true);
      expect(access.canViewPurchases).toBe(true);
      expect(access.canViewFavorites).toBe(true);
      expect(access.canViewNotifications).toBe(false);
      expect(access.canViewPrivateProfile).toBe(false);
      expect(access.canEditProfile).toBe(false);
      expect(access.canViewBadgeProgress).toBe(false);
      expect(access.canViewSocialLinks).toBe(true);
      expect(access.canViewAvailability).toBe(true);
    });

    it('should give minimal access to anonymous users', () => {
      const access = getProfileDataAccess(null, 'user456', false);
      
      expect(access.canViewBasicInfo).toBe(true);
      expect(access.canViewPurchases).toBe(false);
      expect(access.canViewFavorites).toBe(false);
      expect(access.canViewNotifications).toBe(false);
      expect(access.canViewPrivateProfile).toBe(false);
      expect(access.canEditProfile).toBe(false);
      expect(access.canViewBadgeProgress).toBe(false);
      expect(access.canViewSocialLinks).toBe(true);
      expect(access.canViewAvailability).toBe(true);
    });

    it('should give elevated access to admins (but not full)', () => {
      const access = getProfileDataAccess('admin123', 'user456', true);
      
      expect(access.canViewBasicInfo).toBe(true);
      expect(access.canViewPurchases).toBe(true);
      expect(access.canViewFavorites).toBe(true);
      expect(access.canViewNotifications).toBe(false); // Still private
      expect(access.canViewPrivateProfile).toBe(false); // Still private
      expect(access.canEditProfile).toBe(false); // Can't edit others
      expect(access.canViewBadgeProgress).toBe(false); // Still private
      expect(access.canViewSocialLinks).toBe(true);
      expect(access.canViewAvailability).toBe(true);
    });
  });

  describe('filterProfileData', () => {
    const mockProfileData = {
      user: {
        username: 'testuser',
        discordUserId: 'user123',
        avatar: 'avatar.png',
        avatarUrl: 'https://example.com/avatar.png',
        bannerImage: 'banner.png',
        description: 'Test description',
        createdAt: '2023-01-01',
        badges: [],
        isFreelancer: true,
        email: '<EMAIL>',
        youtubeUrl: 'https://youtube.com/test',
        githubUsername: 'testuser',
        openForTasks: true
      },
      purchases: [{ id: 1, name: 'Test Purchase' }],
      favorites: [{ id: 1, name: 'Test Favorite' }],
      notifications: [{ id: 1, message: 'Test Notification' }],
      verification: { isVerified: true, verifiedAt: '2023-01-01' }
    };

    it('should filter data for profile owner', () => {
      const access = getProfileDataAccess('user123', 'user123', false);
      const filtered = filterProfileData(mockProfileData, access);
      
      expect(filtered.user.username).toBe('testuser');
      expect(filtered.user.email).toBe('<EMAIL>');
      expect(filtered.purchases).toHaveLength(1);
      expect(filtered.favorites).toHaveLength(1);
      expect(filtered.isOwnProfile).toBe(true);
    });

    it('should filter data for logged-in users', () => {
      const access = getProfileDataAccess('user456', 'user123', false);
      const filtered = filterProfileData(mockProfileData, access);
      
      expect(filtered.user.username).toBe('testuser');
      expect(filtered.user.email).toBe('<EMAIL>'); // Social links visible
      expect(filtered.purchases).toHaveLength(1);
      expect(filtered.favorites).toHaveLength(1);
      expect(filtered.notifications).toBeUndefined(); // Not included
      expect(filtered.isOwnProfile).toBe(false);
    });

    it('should filter data for anonymous users', () => {
      const access = getProfileDataAccess(null, 'user123', false);
      const filtered = filterProfileData(mockProfileData, access);
      
      expect(filtered.user.username).toBe('testuser');
      expect(filtered.user.email).toBe('<EMAIL>'); // Social links still visible
      expect(filtered.purchases).toHaveLength(0); // Empty array
      expect(filtered.favorites).toHaveLength(0); // Empty array
      expect(filtered.notifications).toBeUndefined(); // Not included
      expect(filtered.isOwnProfile).toBe(false);
    });
  });

  describe('sanitizeProfileInput', () => {
    it('should only allow specific fields', () => {
      const input = {
        description: 'New description',
        githubUsername: 'newusername',
        youtubeUrl: 'https://youtube.com/new',
        email: '<EMAIL>',
        openForTasks: true,
        bannerImage: 'new-banner.png',
        // These should be filtered out
        password: 'secret',
        admintype: 'admin',
        discordUserId: 'hacker123'
      };

      const sanitized = sanitizeProfileInput(input);

      expect(sanitized.description).toBe('New description');
      expect(sanitized.githubUsername).toBe('newusername');
      expect(sanitized.youtubeUrl).toBe('https://youtube.com/new');
      expect(sanitized.email).toBe('<EMAIL>');
      expect(sanitized.openForTasks).toBe(true);
      expect(sanitized.bannerImage).toBe('new-banner.png');
      
      // These should not be present
      expect(sanitized.password).toBeUndefined();
      expect(sanitized.admintype).toBeUndefined();
      expect(sanitized.discordUserId).toBeUndefined();
    });

    it('should trim and limit string length', () => {
      const longString = 'a'.repeat(2000);
      const input = {
        description: `  ${longString}  `
      };

      const sanitized = sanitizeProfileInput(input);

      expect(sanitized.description).toBe('a'.repeat(1000)); // Trimmed and limited
    });

    it('should handle boolean values correctly', () => {
      const input = {
        openForTasks: false
      };

      const sanitized = sanitizeProfileInput(input);

      expect(sanitized.openForTasks).toBe(false);
    });
  });
});
