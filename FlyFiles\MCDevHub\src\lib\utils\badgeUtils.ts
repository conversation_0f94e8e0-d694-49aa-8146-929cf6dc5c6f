import { connectToDatabase } from '@/lib/mongodb';
import { BadgeAwardService } from '@/lib/services/badgeAwardService';

/**
 * Utility function to trigger badge checks from other parts of the application
 */
export async function triggerBadgeCheck(discordUserId: string, action?: string): Promise<void> {
  try {
    const { db } = await connectToDatabase();
    const badgeService = new BadgeAwardService(db);
    await badgeService.triggerBadgeCheck(discordUserId, action);
  } catch (error) {
    console.error('Error triggering badge check:', error);
    // Don't throw error to avoid breaking the main functionality
  }
}

/**
 * Utility function to make internal API call to badge check endpoint
 */
export async function triggerBadgeCheckAPI(discordUserId: string, action?: string): Promise<void> {
  try {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    
    await fetch(`${baseUrl}/api/badges/check`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        discordUserId,
        action
      })
    });
  } catch (error) {
    console.error('Error making badge check API call:', error);
    // Don't throw error to avoid breaking the main functionality
  }
}
