import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

export async function GET() {
  try {
    // Connect to the database
    const { db } = await connectToDatabase();
    
    // Fetch all freelancers
    const freelancers = await db.collection('adminusers')
      .find({ 
        admintype: 'Freelancer'
      }, { 
        projection: {
          username: 1,
          discordUserId: 1,
          avatar: 1,
          avatarUrl: 1,
          bannerImage: 1,
          description: 1,
          isVerified: 1,
          verifiedAt: 1,
          createdAt: 1,
          openForTasks: 1
        }
      })
      .sort({ createdAt: -1 })
      .toArray();

    // Count products for each freelancer and check verification status
    const freelancersWithProductCounts = await Promise.all(
      freelancers.map(async (freelancer) => {
        // Count all products
        const productCount = await db.collection('products')
          .countDocuments({ 
            createdBy: freelancer.username
          });
        // Count active products separately
        const activeProductCount = await db.collection('products')
          .countDocuments({ 
            createdBy: freelancer.username,
            status: 'active'
          });
        // Check verified collection for isVerified true
        let isVerified = false;
        if (freelancer.discordUserId) {
          const verifiedDoc = await db.collection('verified').findOne({
            discordId: freelancer.discordUserId,
            isVerified: true
          });
          isVerified = !!verifiedDoc;
        }
        return {
          ...freelancer,
          productCount,
          activeProductCount,
          isVerified
        };
      })
    );
    
    return NextResponse.json({
      freelancers: freelancersWithProductCounts
    });
  } catch (error) {
    console.error('Error fetching freelancers:', error);
    return NextResponse.json(
      { message: 'Der opstod en fejl ved hentning af freelancers' },
      { status: 500 }
    );
  }
} 