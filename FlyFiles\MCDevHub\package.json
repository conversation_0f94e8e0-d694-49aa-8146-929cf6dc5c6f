{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.38.0", "@auth/mongodb-adapter": "^3.8.0", "@emailjs/browser": "^4.4.1", "@vercel/blob": "^1.0.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "cloudinary": "^2.6.0", "cookies-next": "^5.1.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "framer-motion": "^12.5.0", "gridfs-stream": "^1.1.1", "html2pdf.js": "^0.10.3", "jsonwebtoken": "^9.0.2", "mongodb": "^6.15.0", "mongoose": "^8.13.2", "next": "^15.2.2", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.6", "react": "^19.1.0", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-dom": "^19.1.0", "react-draggable": "^4.4.6", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "sanitize-html": "^2.16.0", "sharp": "^0.33.5", "stripe": "^18.1.1", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.17.30", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4", "typescript": "^5.8.3"}}