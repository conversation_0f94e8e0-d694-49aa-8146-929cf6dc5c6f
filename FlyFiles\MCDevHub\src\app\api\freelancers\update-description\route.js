import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { verifyAdminToken } from '@/lib/auth';

export async function POST(request) {
  try {
    // Verify the admin user is a freelancer
    const admin = await verifyAdminToken();
    
    if (!admin || admin.admintype !== 'Freelancer') {
      return NextResponse.json(
        { message: 'Ikke autoriseret - Kun Freelancers kan opdatere deres beskrivelse' },
        { status: 403 }
      );
    }
    
    // Get description from request body
    const { description } = await request.json();
    
    if (!description || description.trim() === '') {
      return NextResponse.json(
        { message: 'Beskrivelse kan ikke være tom' },
        { status: 400 }
      );
    }
    
    // Limit description length to prevent abuse
    if (description.length > 2000) {
      return NextResponse.json(
        { message: 'Beskrivelse må maksimalt være 2000 tegn' },
        { status: 400 }
      );
    }
    
    // Connect to the database
    const { db } = await connectToDatabase();
    
    // Update the admin user's description
    await db.collection('adminusers').updateOne(
      { username: admin.username },
      { 
        $set: { 
          description: description,
          updatedAt: new Date()
        } 
      }
    );
    
    return NextResponse.json({
      message: 'Beskrivelse opdateret'
    });
  } catch (error) {
    console.error('Error updating description:', error);
    return NextResponse.json(
      { message: 'Der opstod en fejl ved opdatering af beskrivelse' },
      { status: 500 }
    );
  }
} 