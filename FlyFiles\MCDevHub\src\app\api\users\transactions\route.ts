import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Connect to database
    const { db } = await connectToDatabase();
    
    // Check if user is an admin/freelancer
    const adminUser = await db.collection('adminusers').findOne({ 
      discordUserId: discordId 
    });
    
    if (!adminUser) {
      return NextResponse.json({ error: 'User is not an admin/freelancer' }, { status: 403 });
    }
    
    // Get pagination parameters
    const limit = Number(request.nextUrl.searchParams.get('limit')) || 20;
    const page = Number(request.nextUrl.searchParams.get('page')) || 1;
    const skip = (page - 1) * limit;
    
    // Get transactions for the user
    const transactions = await db.collection('transactions')
      .find({ sellerId: discordId })
      .sort({ createdAt: -1 }) // newest first
      .skip(skip)
      .limit(limit)
      .toArray();
    
    // Get total count for pagination
    const totalCount = await db.collection('transactions')
      .countDocuments({ sellerId: discordId });
    
    // Get buyer usernames for better display (batch query to reduce DB calls)
    const buyerIds = [...new Set(transactions.map(t => t.buyerId))];
    const buyers = await db.collection('user_profiles')
      .find({ userId: { $in: buyerIds } })
      .toArray()
      .then(profiles => 
        Object.fromEntries(profiles.map(p => [p.userId, p.username || 'Unknown User']))
      );
    
    // Enrich transactions with buyer usernames
    const enrichedTransactions = transactions.map(t => ({
      ...t,
      buyerName: buyers[t.buyerId] || t.buyerName || 'Unknown User'
    }));
    
    return NextResponse.json({
      transactions: enrichedTransactions,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
    
  } catch (error) {
    console.error('Error fetching transactions:', error);
    return NextResponse.json({
      error: 'Failed to fetch transactions',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
} 