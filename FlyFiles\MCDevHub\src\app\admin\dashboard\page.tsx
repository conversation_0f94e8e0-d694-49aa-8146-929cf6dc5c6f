'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import LogoutButton from '../../components/LogoutButton';
import { FaDiscord } from 'react-icons/fa';

interface Submission {
  _id: string;
  formType: string;
  name?: string;
  email?: string;
  subject?: string;
  discordUsername?: string;
  submittedAt: string;
  isRead?: boolean;
  readBy?: string;
  readAt?: string;
  notes?: string;
}

interface AdminUser {
  username: string;
  admintype: string;
  allowedcases: string;
}

export default function AdminDashboard() {
  const router = useRouter();
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [admin, setAdmin] = useState<AdminUser | null>(null);
  const [allowedTypes, setAllowedTypes] = useState<string[]>([]);
  const [filter, setFilter] = useState<string>('all');
  const [showReadFilter, setShowReadFilter] = useState<string>('all');
  const [updatingId, setUpdatingId] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  useEffect(() => {
    // Get admin info from cookie
    const getAdminInfo = async () => {
      try {
        // Fetch admin info from server
        const response = await fetch('/api/admin/me');
        
        if (!response.ok) {
          if (response.status === 401) {
            // Unauthorized, redirect to login
            router.push('/admin/login');
            return;
          }
          throw new Error('Kunne ikke hente admin information');
        }
        
        const data = await response.json();
        setAdmin(data.user);
        
        // Parse allowed case types
        if (data.user.allowedcases) {
          const types = data.user.allowedcases.split(',').map((type: string) => type.trim());
          setAllowedTypes(types);
        }
      } catch (error) {
        console.error('Error fetching admin info:', error);
        setError('Kunne ikke hente admin information');
        // If we can't verify admin status, redirect to login
        router.push('/admin/login');
      }
    };

    getAdminInfo();
  }, [router]);

  useEffect(() => {
    if (admin) {
      fetchSubmissions(false);
    }
  }, [admin]);

  const fetchSubmissions = async (isButtonClick = true) => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/submissions');
      if (!response.ok) {
        throw new Error('Failed to fetch submissions');
      }
      let data = await response.json();
      
      // Filter submissions based on admin permissions
      if (allowedTypes.length > 0) {
        data = data.filter((submission: Submission) => 
          allowedTypes.includes(submission.formType)
        );
      }
      
      setSubmissions(data);
    } catch (error) {
      console.error('Error fetching submissions:', error);
      setError('Failed to load submissions');
    } finally {
      setLoading(false);
      
      // If this is not a button click (e.g., initial load), 
      // reset refreshing state immediately
      if (!isButtonClick) {
        setRefreshing(false);
      }
      // For button clicks, the timeout in the click handler will handle it
    }
  };

  const getFormTypeLabel = (type: string) => {
    switch (type) {
      case 'partner':
        return 'Partner Ansøgning';
      case 'contact':
        return 'Kontakt Formular';
      case 'custom-order':
        return 'Custom Order';
      default:
        return type;
    }
  };

  const getFormTypeColor = (type: string) => {
    switch (type) {
      case 'partner':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'contact':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'custom-order':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Apply filters to submissions
  const filteredSubmissions = submissions.filter(submission => {
    // Filter by submission type
    const matchesTypeFilter = filter === 'all' || submission.formType === filter;
    
    // Filter by read status
    const matchesReadFilter = 
      showReadFilter === 'all' || 
      (showReadFilter === 'read' && submission.isRead) || 
      (showReadFilter === 'unread' && !submission.isRead);
    
    return matchesTypeFilter && matchesReadFilter;
  });

  const handleMarkAsRead = async (submissionId: string) => {
    try {
      setUpdatingId(submissionId);
      
      const response = await fetch(`/api/submissions/${submissionId}/update`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isRead: true
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update submission');
      }
      
      const data = await response.json();
      
      // Update submission in the local state
      setSubmissions(prev => prev.map(sub => 
        sub._id === submissionId ? { 
          ...sub, 
          isRead: true, 
          readBy: admin?.username,
          readAt: new Date().toISOString()
        } : sub
      ));
      
    } catch (error) {
      console.error('Error marking as read:', error);
    } finally {
      setUpdatingId(null);
    }
  };

  // Helper function to extract admin name from notes
  const extractAdminNameFromNotes = (notes: string) => {
    if (!notes) return 'Unknown';
    
    // Look for the pattern "- username||discordId" at the end of notes
    const signatureRegex = /- ([^|]+)\|\|([^|\s]+)$/;
    const match = notes.match(signatureRegex);
    
    if (match && match[1]) {
      const username = match[1];
      const discordId = match[2] || '';
      
      if (discordId) {
        return `<a href="https://discord.com/users/${discordId}" target="_blank" class="text-blue-600">${username}</a>`;
      }
      return username;
    }
    
    // If no match found, try a simpler pattern without discord ID
    const simpleRegex = /- ([^\n]+)$/;
    const simpleMatch = notes.match(simpleRegex);
    
    if (simpleMatch && simpleMatch[1]) {
      return simpleMatch[1];
    }
    
    return 'Unknown';
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Dashboard Header */}
        <div className="bg-gradient-to-r from-blue-800 to-indigo-900 rounded-3xl shadow-2xl p-8 mb-8">
          <div className="flex flex-col md:flex-row md:items-center justify-between space-y-6 md:space-y-0">
            <div className="space-y-2">
              <h1 className="text-3xl font-extrabold text-white">Freelance Panel</h1>
              <div className="flex items-center space-x-3">
                <p className="text-blue-200 font-medium">Velkommen, {admin?.username}</p>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-blue-100/20 text-blue-100 cursor-default">
                  {admin?.admintype}
                </span>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              {admin?.username === 'MyckasP' && (
                <Link 
                  href="/admin/users"
                  className="flex items-center justify-center px-5 py-2.5 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 hover:shadow-lg"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  Brugerhåndtering
                </Link>
              )}
              
              {admin?.admintype === 'Freelancer' && (
                <>
                  <Link
                    href="/admin/products/my-products"
                    className="flex items-center justify-center px-5 py-2.5 bg-blue-500/90 backdrop-blur-sm border border-blue-400/30 rounded-xl text-white hover:bg-blue-600 transition-all duration-300 hover:shadow-lg"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                    </svg>
                    Mine Produkter
                  </Link>
                  <Link
                    href="/admin/products/upload"
                    className="flex items-center justify-center px-5 py-2.5 bg-green-500/90 backdrop-blur-sm border border-green-400/30 rounded-xl text-white hover:bg-green-600 transition-all duration-300 hover:shadow-lg"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                    </svg>
                    Upload Produkt
                  </Link>
                </>
              )}
              
              <LogoutButton />
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-2xl shadow-md border border-gray-200 overflow-hidden mb-8">
          <div className="px-6 py-5 sm:px-8 border-b border-gray-100">
            <h2 className="text-lg font-bold text-gray-900 flex items-center">
              <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
              Filtrer Indsendelser
            </h2>
          </div>
          
          <div className="px-6 py-5 sm:px-8 grid grid-cols-1 md:grid-cols-12 gap-6 items-end">
            <div className="md:col-span-4">
              <label htmlFor="type-filter" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                Formular Type
              </label>
              <div className="relative">
                <select
                  id="type-filter"
                  value={filter}
                  onChange={(e) => setFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2.5 text-base border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg shadow-sm"
                >
                  <option value="all">Alle Typer</option>
                  {allowedTypes.includes('partner') && (
                    <option value="partner">Partner Ansøgning</option>
                  )}
                  {allowedTypes.includes('contact') && (
                    <option value="contact">Kontakt Formular</option>
                  )}
                  {allowedTypes.includes('custom-order') && (
                    <option value="custom-order">Custom Order</option>
                  )}
                </select>
              </div>
            </div>
            
            <div className="md:col-span-4">
              <label htmlFor="read-filter" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                Status
              </label>
              <div className="relative">
                <select
                  id="read-filter"
                  value={showReadFilter}
                  onChange={(e) => setShowReadFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2.5 text-base border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg shadow-sm"
                >
                  <option value="all">Alle Indsendelser</option>
                  <option value="read">Læste Indsendelser</option>
                  <option value="unread">Ulæste Indsendelser</option>
                </select>
              </div>
            </div>
            
            <div className="md:col-span-4 flex justify-end">
              <button
                onClick={() => {
                  const button = document.getElementById('refresh-button') as HTMLButtonElement;
                  if (button) {
                    button.disabled = true;
                    button.innerHTML = `
                      <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Opdaterer...
                    `;
                    fetchSubmissions(true).then(() => {
                      // Use the same timeout for both the button and refreshing state
                      // for synchronized UI updates
                      setTimeout(() => {
                        setRefreshing(false);
                        button.disabled = false;
                        button.innerHTML = `
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                          </svg>
                          Opdater
                        `;
                      }, 800); // A slightly shorter delay that works well for both
                    });
                  }
                }}
                id="refresh-button"
                className="inline-flex items-center px-5 py-2.5 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-400 disabled:cursor-default"
                onFocus={(e) => e.target.blur()}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Opdater
              </button>
            </div>
          </div>
          
          <div className="px-6 py-3 sm:px-8 bg-gray-50 border-t border-gray-100">
            <div className="flex justify-between items-center text-sm">
              <div className="flex items-center text-gray-500">
                <svg className="w-4 h-4 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <span className="font-medium">
                  Total: <span className="font-bold text-blue-600">{submissions.length}</span> indsendelser
                </span>
              </div>
              <div className="text-gray-500">
                <span className="font-medium">
                  Filtreret: <span className="font-bold text-blue-600">{filteredSubmissions.length}</span> indsendelser
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Submission Table */}
        <div className="bg-white rounded-2xl shadow-md border border-gray-200 overflow-hidden">
          <div className="px-6 py-5 sm:px-8 border-b border-gray-100">
            <h2 className="text-lg font-bold text-gray-900 flex items-center">
              <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              Indsendelser
            </h2>
            {allowedTypes.length > 0 && (
              <p className="mt-2 text-sm text-gray-500 flex items-center">
                <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Viser kun {allowedTypes.map(type => getFormTypeLabel(type)).join(', ')}
              </p>
            )}
          </div>
          
          {loading ? (
            <div className="flex flex-col items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
              <p className="text-gray-500">Indlæser indsendelser...</p>
            </div>
          ) : refreshing ? (
            <div className="flex flex-col items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
              <p className="text-gray-500">Opdaterer indsendelser...</p>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-red-600 font-medium text-lg">{error}</p>
              <p className="text-gray-500 mt-2">Prøv at genindlæse siden eller kontakt systemadministratoren.</p>
            </div>
          ) : filteredSubmissions.length === 0 ? (
            <div className="p-8 text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
                <svg className="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                </svg>
              </div>
              <p className="text-gray-600 font-medium text-lg">Ingen indsendelser fundet</p>
              <p className="text-gray-500 mt-2">Prøv at ændre dine filterindstillinger for at se flere resultater.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th scope="col" className="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Navn
                    </th>
                    <th scope="col" className="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email / Discord
                    </th>
                    <th scope="col" className="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dato
                    </th>
                    <th scope="col" className="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Handlinger
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {filteredSubmissions.map((submission) => (
                    <tr 
                      key={submission._id}
                      className={`${submission.isRead ? 'bg-gray-50' : 'bg-white hover:bg-blue-50'} transition-colors duration-150`}
                    >
                      <td className="px-6 py-4">
                        {submission.isRead ? (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border border-green-200 bg-green-100 text-green-800 cursor-default">
                            <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Læst af {submission.readBy}
                          </span>
                        ) : (
                          <button
                            onClick={() => handleMarkAsRead(submission._id)}
                            disabled={updatingId === submission._id}
                            className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border border-amber-200 bg-amber-100 text-amber-800 hover:bg-amber-200 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-amber-500"
                          >
                            {updatingId === submission._id ? (
                              <>
                                <svg className="animate-spin -ml-0.5 mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Markerer...
                              </>
                            ) : (
                              <>
                                <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Ulæst
                              </>
                            )}
                          </button>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2.5 py-1 rounded-full text-xs font-medium border ${getFormTypeColor(submission.formType)}`}>
                          {getFormTypeLabel(submission.formType)}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-gray-900">{submission.name || 'N/A'}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex flex-col">
                          <a href={`mailto:${submission.email}`} className="text-sm text-blue-600 hover:text-blue-800">
                            {submission.email || 'N/A'}
                          </a>
                          {submission.discordUsername && (
                            <div className="text-xs text-[#5865F2] mt-1 flex items-center">
                              <FaDiscord className="w-3.5 h-3.5 mr-1 text-[#5865F2]" />
                              {submission.discordUsername}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex flex-col">
                          <div className="text-sm text-gray-500 cursor-default">
                            {new Date(submission.submittedAt).toLocaleDateString('da-DK', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                            })}
                            <span className="text-gray-400 ml-1">
                              {new Date(submission.submittedAt).toLocaleTimeString('da-DK', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                          </div>
                          {submission.notes && (
                            <div className="text-xs text-blue-600 mt-1 flex items-center">
                              <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              <span>Noter fra </span>
                              <span 
                                className="ml-1"
                                dangerouslySetInnerHTML={{ __html: extractAdminNameFromNotes(submission.notes) }}
                              />
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <Link 
                          href={`/admin/submissions/${submission._id}`}
                          className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500"
                        >
                          <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          Se detaljer
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          
          {!loading && !error && filteredSubmissions.length > 0 && (
            <div className="px-6 py-4 sm:px-8 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
              <div className="text-sm text-gray-500">
                Viser {filteredSubmissions.length} ud af {submissions.length} indsendelser
              </div>
              <button
                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-gray-900 transition-colors"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
                Tilbage til toppen
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 