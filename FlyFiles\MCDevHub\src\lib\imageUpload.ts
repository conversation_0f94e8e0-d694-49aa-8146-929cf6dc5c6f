/**
 * Image upload service for MCDevHub
 * Handles uploading images to Cloudinary
 */

import { uploadToCloudinary, deleteFromCloudinary } from './cloudinary';

/**
 * Upload a file to Cloudinary
 * @param file File data as buffer or Blob
 * @param folder Optional folder name for organization (e.g., 'screenshots', 'avatars')
 * @param filename Optional filename, will be generated if not provided
 * @param fileContentType MIME type of the file
 * @returns Object containing URL and path if successful, or error
 */
export async function uploadImage(
  file: Buffer | Blob, 
  folder: string = '', 
  filename: string = '', 
  fileContentType: string = ''
): Promise<{ url: string; path: string } | { error: string }> {
  try {
    // Convert Blob to <PERSON>uffer if needed
    let buffer: Buffer;
    if (file instanceof Blob) {
      const arrayBuffer = await file.arrayBuffer();
      buffer = Buffer.from(arrayBuffer);
    } else {
      buffer = file;
    }

    // Create filename if not provided
    const finalFilename = filename || `image-${Date.now()}${getExtensionFromContentType(fileContentType)}`;
    
    // Upload to Cloudinary
    console.log(`Uploading image to Cloudinary: folder=${folder}, filename=${finalFilename}`);
    const result = await uploadToCloudinary(buffer, folder, finalFilename, fileContentType);
    
    // Check for errors
    if ('error' in result) {
      throw new Error(result.error);
    }
    
    // Return success result
    return {
      url: result.url,
      path: result.path
    };
  } catch (error) {
    console.error('Error uploading image to Cloudinary:', error);
    
    // Provide more specific error message based on the error
    let errorMessage = 'Unknown error occurred during upload';
    
    if (error instanceof Error) {
      // If error message contains specific error patterns, provide more helpful messages
      if (error.message.includes('NetworkError') || error.message.includes('network error')) {
        errorMessage = 'Network connection error. Please check your internet connection and try again.';
      } else if (error.message.includes('Invalid credentials')) {
        errorMessage = 'Invalid Cloudinary credentials. Please check your API key and secret.';
      } else if (error.message.includes('resource_limit_exceeded')) {
        errorMessage = 'Resource limit exceeded on your Cloudinary account.';
      } else {
        // Use the original error message if it's specific
        errorMessage = error.message;
      }
    }
    
    return { error: errorMessage };
  }
}

/**
 * Upload multiple files to Cloudinary
 * @param files Array of file data as buffer or Blob
 * @param folder Optional folder name for organization
 * @param filenames Optional array of filenames
 * @param fileContentTypes Optional array of content types
 * @returns Object containing array of URLs and paths if successful, or error
 */
export async function uploadMultipleImages(
  files: (Buffer | Blob)[], 
  folder: string = '', 
  filenames: string[] = [], 
  fileContentTypes: string[] = []
): Promise<{ files: { url: string; path: string }[] } | { error: string }> {
  try {
    const results = [];
    
    // Process each file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileContentType = fileContentTypes[i] || '';
      const fileName = filenames[i] || `image-${Date.now()}-${i}${getExtensionFromContentType(fileContentType)}`;
      
      // Upload each file to Cloudinary
      const result = await uploadImage(file, folder, fileName, fileContentType);
      
      if ('error' in result) {
        throw new Error(`Error uploading file ${i + 1}: ${result.error}`);
      }
      
      results.push(result);
    }
    
    // Return success result
    return {
      files: results
    };
  } catch (error) {
    console.error('Error uploading multiple images to Cloudinary:', error);
    // Detailed error message
    const errorMessage = error instanceof Error 
      ? `${error.name}: ${error.message}` 
      : 'Unknown error';
    return { error: errorMessage };
  }
}

/**
 * Helper function to get file extension from content type
 */
function getExtensionFromContentType(contentType: string): string {
  const mapping: { [key: string]: string } = {
    'image/jpeg': '.jpg',
    'image/jpg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
    'image/svg+xml': '.svg',
  };
  
  return mapping[contentType] || '';
}

/**
 * Delete an image from Cloudinary
 * @param publicId Cloudinary public ID to delete
 * @returns Success or error message
 */
export async function deleteImage(
  publicId: string
): Promise<{ success: boolean } | { error: string }> {
  try {
    // If the publicId is a full URL, extract just the public ID portion
    if (publicId.startsWith('http')) {
      // Extract the public ID from the URL
      // URLs are typically in the format: https://res.cloudinary.com/cloud-name/image/upload/v1234567890/folder/filename
      const urlParts = publicId.split('/');
      const uploadIndex = urlParts.indexOf('upload');
      if (uploadIndex !== -1 && uploadIndex < urlParts.length - 1) {
        publicId = urlParts.slice(uploadIndex + 2).join('/');
      } else {
        throw new Error('Could not parse Cloudinary public ID from URL');
      }
    }
    
    // Send the delete request to Cloudinary
    console.log(`Deleting image from Cloudinary: publicId=${publicId}`);
    const result = await deleteFromCloudinary(publicId);
    
    // Check for errors
    if ('error' in result) {
      throw new Error(result.error);
    }
    
    // Return success
    return { success: true };
  } catch (error) {
    console.error('Error deleting image from Cloudinary:', error);
    const errorMessage = error instanceof Error 
      ? `${error.name}: ${error.message}` 
      : 'Unknown error';
    return { error: errorMessage };
  }
} 