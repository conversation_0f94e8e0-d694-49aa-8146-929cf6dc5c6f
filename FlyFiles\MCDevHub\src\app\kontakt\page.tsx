'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { FaDiscord } from 'react-icons/fa';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    discordUsername: ''
  });
  const [submitted, setSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Set page title
  useEffect(() => {
    // Set initial title
    document.title = "Kontakt Os | MCDevHub";
    
    // Maintain title with an interval in case it gets overwritten
    const titleInterval = setInterval(() => {
      if (document.title !== "Kontakt Os | MCDevHub") {
        document.title = "Kontakt Os | MCDevHub";
      }
    }, 500);
    
    // Cleanup
    return () => clearInterval(titleInterval);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    
    try {
      const response = await fetch('/api/submit-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formType: 'contact',
          ...formData,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || data.message || 'Failed to submit form');
      }
      setSubmitted(true);
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
        discordUsername: ''
      });
    } catch (error) {
      console.error('Error submitting form:', error);
      setError(error instanceof Error ? error.message : 'Der opstod en fejl. Prøv igen senere eller kontakt os via Discord.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <section className="bg-blue-600 text-white py-10 sm:py-12 md:py-16">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-3 sm:mb-4">Kontakt Os</h1>
          <p className="text-lg sm:text-xl text-blue-100 max-w-md sm:max-w-lg md:max-w-2xl mx-auto">
            Har du spørgsmål eller brug for hjælp? Vi er her for at hjælpe dig.
          </p>
        </div>
      </section>
      
      {/* Contact Form & Info */}
      <section className="py-10 sm:py-12 md:py-16">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-5xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8">
              {/* Contact Information */}
              <div className="lg:col-span-1">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 sm:p-8 border border-blue-200 shadow-sm">
                  <h2 className="text-xl sm:text-2xl font-bold mb-6 sm:mb-8 text-blue-900 flex items-center space-x-3">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span>Kontaktinformation</span>
                  </h2>
                  
                  <div className="space-y-6 sm:space-y-8">
                    {/* Email Card */}
                    <div className="bg-white p-4 sm:p-5 rounded-lg border border-blue-100 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-50 rounded-full">
                          <svg className="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-base sm:text-lg font-semibold text-blue-800 mb-1">Email</h3>
                          <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700 transition-colors">
                            <EMAIL>
                          </a>
                        </div>
                      </div>
                    </div>

                    {/* Discord Card */}
                    <div className="bg-white p-4 sm:p-5 rounded-lg border border-blue-100 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-50 rounded-full">
                          <svg className="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-base sm:text-lg font-semibold text-blue-800 mb-1">Discord</h3>
                          <p className="text-gray-600 text-sm sm:text-base mb-2">
                            Join vores discord for direkte support
                          </p>
                          <a 
                            href="https://discord.mcdevhub.dk" 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 transition-colors text-sm sm:text-base"
                          >
                            <FaDiscord className="w-4 h-4 mr-2" />
                            Join Discord
                          </a>
                        </div>
                      </div>
                    </div>

                    {/* Response Time Card */}
                    <div className="bg-white p-4 sm:p-5 rounded-lg border border-blue-100 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-50 rounded-full">
                          <svg className="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-base sm:text-lg font-semibold text-blue-800 mb-1">Svartid</h3>
                          <p className="text-gray-600 text-sm sm:text-base">
                            Vi svarer typisk inden for 24 timer på hverdage
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Contact Form */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                  {submitted ? (
                    <div className="p-6 sm:p-8 text-center">
                      <div className="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                        <svg className="w-6 h-6 sm:w-8 sm:h-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <h3 className="text-xl sm:text-2xl font-bold text-gray-800 mb-3 sm:mb-4">Tak for din henvendelse!</h3>
                      <p className="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base">
                        Vi har modtaget din besked og vil svare så hurtigt som muligt.
                      </p>
                      <button
                        onClick={() => setSubmitted(false)}
                        className="bg-blue-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-md font-medium hover:bg-blue-700 transition-colors text-sm sm:text-base"
                      >
                        Send ny besked
                      </button>
                    </div>
                  ) : (
                    <form onSubmit={handleSubmit} className="p-6 sm:p-8">
                      <h2 className="text-xl sm:text-2xl font-bold mb-6 sm:mb-8 text-gray-800">Send Os en Besked</h2>
                      
                      {error && (
                        <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm sm:text-base">
                          {error}
                        </div>
                      )}
                      
                      <div className="space-y-4 sm:space-y-6">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                          <div className="space-y-1">
                            <label htmlFor="name" className="block text-xs sm:text-sm font-medium text-gray-700">Navn</label>
                            <input
                              type="text"
                              id="name"
                              name="name"
                              value={formData.name}
                              onChange={handleChange}
                              required
                              className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base"
                              placeholder="Indtast dit navn"
                            />
                          </div>
                          
                          <div className="space-y-1">
                            <label htmlFor="email" className="block text-xs sm:text-sm font-medium text-gray-700">Email</label>
                            <input
                              type="email"
                              id="email"
                              name="email"
                              value={formData.email}
                              onChange={handleChange}
                              required
                              className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base"
                              placeholder="Indtast din email"
                            />
                          </div>
                        </div>
                        
                        <div className="space-y-1">
                          <label htmlFor="discordUsername" className="block text-xs sm:text-sm font-medium text-gray-700">Discord Brugernavn</label>
                          <input
                            type="text"
                            id="discordUsername"
                            name="discordUsername"
                            value={formData.discordUsername}
                            onChange={handleChange}
                            className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base"
                            placeholder="Indtast dit Discord navn"
                          />
                        </div>
                        
                        <div className="space-y-1">
                          <label htmlFor="subject" className="block text-xs sm:text-sm font-medium text-gray-700">Emne</label>
                          <input
                            type="text"
                            id="subject"
                            name="subject"
                            value={formData.subject}
                            onChange={handleChange}
                            required
                            className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base"
                            placeholder="Hvad handler din besked om?"
                          />
                        </div>
                        
                        <div className="space-y-1">
                          <label htmlFor="message" className="block text-xs sm:text-sm font-medium text-gray-700">Besked</label>
                          <textarea
                            id="message"
                            name="message"
                            value={formData.message}
                            onChange={handleChange}
                            required
                            rows={6}
                            className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base"
                            placeholder="Skriv din besked her..."
                          ></textarea>
                        </div>
                        
                        <div className="pt-4 sm:pt-6 border-t border-gray-200">
                          <div className="flex flex-col space-y-4">
                            <div className="flex items-start space-x-3">
                              <input
                                type="checkbox"
                                id="tosConfirmation"
                                required
                                title="Bekræft at du accepterer vores vilkår og privatlivspolitik"
                                className="mt-1 h-4 w-4 sm:h-5 sm:w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500 transition-colors duration-300 ease-in-out cursor-pointer"
                              />
                              <label htmlFor="tosConfirmation" className="text-xs sm:text-sm text-gray-700 leading-5">
                                Ved at indsende denne formular accepterer jeg MCDevHub's {' '}
                                <Link href="/tos" className="text-blue-600 hover:underline font-medium transition-all duration-300 ease-in-out" target="_blank">
                                  brugsvilkår
                                </Link>{' '}
                                og{' '}
                                <Link href="/privatlivspolitik" className="text-blue-600 hover:underline font-medium transition-all duration-300 ease-in-out" target="_blank">
                                  privatlivspolitik
                                </Link>.
                              </label>
                            </div>
                            
                            <button
                              type="submit"
                              disabled={isSubmitting}
                              className={`w-full ${isSubmitting ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'} text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 ease-in-out transform hover:scale-[1.02] active:scale-100 flex items-center justify-center space-x-2 cursor-pointer text-sm sm:text-base`}
                            >
                              {isSubmitting ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-3 h-4 w-4 sm:h-5 sm:w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  <span>Sender...</span>
                                </>
                              ) : (
                                <>
                                  <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                  </svg>
                                  <span>Send Besked</span>
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    </form>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* FAQ Section */}
      <section className="py-10 sm:py-12 md:py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6">
          <h2 className="text-2xl sm:text-3xl font-bold mb-8 sm:mb-12 text-center text-gray-800">Ofte Stillede Spørgsmål</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto">
            <div className="bg-white rounded-xl shadow-lg p-6 sm:p-8 hover:shadow-xl transition-shadow duration-300">
              <div className="bg-blue-100 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mb-4 sm:mb-6">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 text-gray-800">Leveringstid</h3>
              <p className="text-gray-600 leading-relaxed text-sm sm:text-base">
                Leveringstiden varierer afhængigt af projektets kompleksitet. Vi giver dig en estimeret leveringstid, når vi sender dig et tilbud efter at have gennemgået dine krav.
              </p>
            </div>
            
            <div className="bg-white rounded-xl shadow-lg p-6 sm:p-8 hover:shadow-xl transition-shadow duration-300">
              <div className="bg-blue-100 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mb-4 sm:mb-6">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
              <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 text-gray-800">Opdateringer</h3>
              <p className="text-gray-600 leading-relaxed text-sm sm:text-base">
                Vi tilbyder opdatering af eksisterende plugins, både dem vi har udviklet og i nogle tilfælde også plugins udviklet af andre.
              </p>
            </div>
            
            <div className="bg-white rounded-xl shadow-lg p-6 sm:p-8 hover:shadow-xl transition-shadow duration-300">
              <div className="bg-blue-100 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mb-4 sm:mb-6">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H7a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 text-gray-800">Betaling</h3>
              <p className="text-gray-600 leading-relaxed text-sm sm:text-base">
                Vi accepterer betalinger via MobilePay. Vi sender dig betalingsinstruktioner sammen med dit tilbud.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}