'use client';

import { createContext, useContext } from 'react';
import { useSession } from 'next-auth/react';
import { Session } from 'next-auth';

// Define the user type based on NextAuth session
interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  isAdmin?: boolean;
  adminType?: string;
  allowedCases?: string;
}

type AuthContextType = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAdmin: boolean;
  checkAdminStatus: () => Promise<boolean>;
  refreshAuth: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  isLoading: true,
  isAdmin: false,
  checkAdminStatus: async () => false,
  refreshAuth: async () => {},
});

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const { data: session, status } = useSession();

  const user: User | null = session?.user ? {
    id: session.user.id || '',
    name: session.user.name,
    email: session.user.email,
    image: session.user.image,
    isAdmin: session.user.isAdmin,
    adminType: session.user.adminType,
    allowedCases: session.user.allowedCases,
  } : null;

  const isLoading = status === 'loading';
  const isAdmin = user?.isAdmin || false;

  // Check if the current user is an admin
  const checkAdminStatus = async (): Promise<boolean> => {
    if (!user) {
      return false;
    }

    try {
      // Add a timestamp to avoid cache issues
      const response = await fetch(`/api/auth/check-admin?t=${Date.now()}`);
      const data = await response.json();
      return data.isAdmin;
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  };

  const refreshAuth = async () => {
    // With NextAuth, session refresh is handled automatically
    // This function is kept for backward compatibility
    return;
  };

  return (
    <AuthContext.Provider value={{
      user,
      session,
      isLoading,
      isAdmin,
      checkAdminStatus,
      refreshAuth
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
