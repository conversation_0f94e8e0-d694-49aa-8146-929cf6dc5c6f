'use client';

import React, { useState, useEffect, useCallback, useRef, useMemo, Suspense } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FaUser, FaShoppingBag, FaHeart, FaExternalLinkAlt, FaDiscord, FaCheckCircle, FaCode, FaCamera, FaClock, FaShieldAlt, FaGithub, FaYoutube, FaCheck, FaUserCircle, FaExclamationCircle, FaEnvelope, FaBell, FaSpinner, FaExclamationTriangle, FaWallet, FaExchangeAlt, FaCalendar, FaCube, FaMoneyBillWave, FaFileDownload, FaPlus } from 'react-icons/fa';
import { useAuth } from '@/context/AuthContext';
import { getImageUrl } from '@/lib/imageUtils';
import { SketchPicker } from 'react-color'; // You may need to install react-color
import { formatDistanceToNow, format } from 'date-fns';
import { da } from 'date-fns/locale';
import { useSearchParams, useRouter } from 'next/navigation';
import BalanceDisplay from '@/components/BalanceDisplay';
import TransactionsTable from '@/components/TransactionsTable';
import DepositModal from '@/components/DepositModal';
import BadgeDisplay from '@/components/BadgeDisplay';

// Create a separate component for handling URL search params
function TabParamHandler({ setActiveTab, isAdmin }: { setActiveTab: (tab: TabType) => void, isAdmin: boolean }) {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    
    if (tabParam) {
      // Make sure the tab is valid before setting it
      if (tabParam === 'purchases' || 
          tabParam === 'favorites' || 
          tabParam === 'notifications' || 
          (tabParam === 'products' && isAdmin) || 
          (tabParam === 'profile' && isAdmin) ||
          (tabParam === 'transactions' && isAdmin)) {
        setActiveTab(tabParam as TabType);
        
        // Update the cache
        globalCache.activeTab = tabParam as TabType;
        updateSessionCache(globalCache);
        
        // If tab is purchases, clean the URL to just /profile without the query param
        if (tabParam === 'purchases') {
          router.replace('/profile', { scroll: false });
        }
      }
    } else {
      // If no tab parameter, default to purchases tab
      setActiveTab('purchases');
      globalCache.activeTab = 'purchases';
      updateSessionCache(globalCache);
    }
  }, [searchParams, isAdmin, setActiveTab, router]);
  
  return null;
}

// Tab options for the profile page
type TabType = 'purchases' | 'favorites' | 'notifications' | 'products' | 'profile' | 'transactions';

// Verification status interface
interface VerificationStatus {
  isVerified: boolean;
  verifiedAt: string | null;
}

// Product interface
interface Product {
  _id: string;
  projectName: string;
  productType: string;
  price: number;
  createdBy: string;
  discordUserId?: string;
  screenshotUrls: Array<{
    fileId: string;
    filename: string;
    contentType: string;
    url?: string;
  }>;
  status: string;
}

// Get cache from sessionStorage (if available) for faster loading between page navigations
const getInitialCache = () => {
  // Only run in browser
  if (typeof window !== 'undefined') {
    try {
      const cachedData = sessionStorage.getItem('profileCache');
      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        return parsed;
      }
    } catch (e) {
      console.error('Error reading from sessionStorage:', e);
    }
  }
  return {
    verificationStatus: null,
    products: [],
    hasProducts: false,
    lastFetch: 0,
    activeTab: 'purchases' // Default tab
  };
};

// Global cache with controlled size (don't store giant objects)
const globalCache = getInitialCache();

// Function to update the session storage cache with size limit
const updateSessionCache = (cache) => {
  if (typeof window !== 'undefined') {
    try {
      // Limit the size of cached products to prevent memory growth
      if (cache.products && Array.isArray(cache.products)) {
        // Create a trimmed version with only essential product data
        const trimmedProducts = cache.products.map(product => ({
          _id: product._id,
          projectName: product.projectName,
          productType: product.productType,
          price: product.price,
          status: product.status,
          // Only keep first screenshot URL for thumbnail
          screenshotUrls: product.screenshotUrls ? 
            [product.screenshotUrls[0]].filter(Boolean) : []
        }));
        
        // Save the trimmed version in the cache
        const trimmedCache = {
          ...cache,
          products: trimmedProducts
        };
        sessionStorage.setItem('profileCache', JSON.stringify(trimmedCache));
      } else {
        sessionStorage.setItem('profileCache', JSON.stringify(cache));
      }
    } catch (e) {
      console.error('Error writing to sessionStorage:', e);
    }
  }
};

// Function to get product type label
const getProductTypeLabel = (type: string) => {
  switch (type) {
    case 'map': return 'Map';
    case 'plugin': return 'Plugin';
    case 'resourcepack': return 'Resourcepack';
    case 'skript': return 'Skript';
    case 'other': return 'Andet';
    default: return type;
  }
};

// Function to get product type color 
const getProductTypeColor = (type: string) => {
  switch (type) {
    case 'map': return 'bg-green-500';
    case 'plugin': return 'bg-blue-500';
    case 'resourcepack': return 'bg-yellow-500';
    case 'skript': return 'bg-purple-500';
    case 'other': return 'bg-gray-500';
    default: return 'bg-gray-500';
  }
};

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState<TabType>(globalCache.activeTab || 'purchases');
  const [verificationStatus, setVerificationStatus] = useState<VerificationStatus | null>(globalCache.verificationStatus);
  const [hasProducts, setHasProducts] = useState<boolean>(globalCache.hasProducts);
  const [products, setProducts] = useState<Product[]>(globalCache.products);
  const [isLoadingVerification, setIsLoadingVerification] = useState<boolean>(false);
  const [isLoadingProducts, setIsLoadingProducts] = useState<boolean>(false);
  const [bannerUploading, setBannerUploading] = useState(false);
  const [bannerError, setBannerError] = useState<string | null>(null);
  // Add MongoDB user ID state
  const [mongoDbUserId, setMongoDbUserId] = useState<string>('');
  const [userCreatedAt, setUserCreatedAt] = useState<string>('');
  const [loadingUserId, setLoadingUserId] = useState<boolean>(true);
  
  // Add state for user purchases
  const [purchases, setPurchases] = useState<any[]>([]);
  const [isLoadingPurchases, setIsLoadingPurchases] = useState(false);
  
  // Add state for favorites
  const [favorites, setFavorites] = useState<any[]>([]);
  const [isLoadingFavorites, setIsLoadingFavorites] = useState(false);
  const favoritesFetchingRef = useRef<boolean>(false);
  
  // Add state for purchase search - MOVED FROM CONDITIONAL POSITION
  const [purchaseSearchQuery, setPurchaseSearchQuery] = useState<string>('');
  const [filteredGroupedPurchases, setFilteredGroupedPurchases] = useState<Record<string, any>>({});
  const [groupedPurchases, setGroupedPurchases] = useState<Record<string, any>>({});
  
  // Add state for deposit modal
  const [isDepositModalOpen, setIsDepositModalOpen] = useState<boolean>(false);

  // Add state for badge checking
  const [isCheckingBadges, setIsCheckingBadges] = useState<boolean>(false);
  const [lastBadgeCheckTime, setLastBadgeCheckTime] = useState<number>(0);

  // References to track fetch status
  const verifyFetchingRef = useRef<boolean>(false);
  const productsFetchingRef = useRef<boolean>(false);
  const purchasesFetchingRef = useRef<boolean>(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const { user, isAdmin } = useAuth();
  const router = useRouter();
  
  // Search filter function for purchases
  const filterPurchases = useCallback((query: string, purchases: any[]) => {
    if (!query.trim()) return purchases;
    
    const lowercaseQuery = query.toLowerCase().trim();
    return purchases.filter(purchase => 
      purchase.productName?.toLowerCase().includes(lowercaseQuery) ||
      purchase.productType?.toLowerCase().includes(lowercaseQuery) ||
      purchase.seller?.toLowerCase().includes(lowercaseQuery) ||
      (purchase.amount && String(purchase.amount).includes(lowercaseQuery))
    );
  }, []);
  
  // Group purchases by month/year
  useEffect(() => {
    if (purchases.length === 0) return;
    
    const grouped: Record<string, typeof purchases> = purchases.reduce((groups, purchase) => {
      const date = new Date(purchase.purchaseDate);
      const monthYear = `${date.toLocaleString('da-DK', { month: 'long', year: 'numeric' })}`;
      
      if (!groups[monthYear]) {
        groups[monthYear] = [];
      }
      groups[monthYear].push(purchase);
      return groups;
    }, {} as Record<string, typeof purchases>);
    
    setGroupedPurchases(grouped);
    
    // Initialize filtered purchases with all purchases
    if (!purchaseSearchQuery) {
      setFilteredGroupedPurchases(grouped);
    }
  }, [purchases, purchaseSearchQuery]);

  // Update filtered purchases when search query changes
  useEffect(() => {
    if (!purchaseSearchQuery.trim()) {
      setFilteredGroupedPurchases(groupedPurchases);
      return;
    }
    
    // Create a new object with filtered purchases
    const filtered: Record<string, any[]> = {};
    
    Object.entries(groupedPurchases).forEach(([monthYear, monthPurchases]) => {
      const filteredMonthPurchases = filterPurchases(purchaseSearchQuery, monthPurchases as any[]);
      if (filteredMonthPurchases.length > 0) {
        filtered[monthYear] = filteredMonthPurchases;
      }
    });
    
    setFilteredGroupedPurchases(filtered);
  }, [purchaseSearchQuery, groupedPurchases, filterPurchases]);
  
  // Function to generate and download PDF receipt
  const generateReceiptPdf = async (purchase: any) => {
    // Try to determine product type if it's missing
    let productType = purchase.productType;
    if (!productType || productType === "-") {
      try {
        // Attempt to fetch product details if we have the productId
        if (purchase.productId) {
          const response = await fetch(`/api/products/${purchase.productId}`);
          if (response.ok) {
            const productData = await response.json();
            if (productData.product && productData.product.productType) {
              productType = productData.product.productType;
            }
          }
        }
        
        // If we still don't have the type, try to infer it from the name
        if (!productType && purchase.productName) {
          const name = purchase.productName.toLowerCase();
          if (name.includes('map') || name.includes('kort') || name.includes('spawn')) {
            productType = 'map';
          } else if (name.includes('plugin') || name.includes('plugins')) {
            productType = 'plugin';
          } else if (name.includes('resourcepack') || name.includes('resource pack') || name.includes('texture')) {
            productType = 'resourcepack';
          } else if (name.includes('skript') || name.includes('script')) {
            productType = 'skript';
          } else if (name.includes('server') || name.includes('setup')) {
            productType = 'other';
          }
        }
      } catch (error) {
        console.error('Error determining product type:', error);
      }
    }
    
    // Format the purchase date
    const purchaseDate = new Date(purchase.purchaseDate).toLocaleDateString('da-DK', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
    
    // Format the random receipt number based on the id
    const receiptNumber = purchase._id.substring(0, 8).toUpperCase();
    
    // Create receipt HTML content with a more authentic receipt design
    const receiptContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Kvittering - ${purchase.productName}</title>
        <style>
          body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            color: #333;
          }
          .receipt-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            padding: 10px;
            border: 1px solid #ddd;
          }
          .receipt {
            padding: 10px;
            border: 1px dashed #ccc;
          }
          .receipt-header {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #ccc;
          }
          .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          .receipt-title {
            font-size: 14px;
            margin: 5px 0;
            text-transform: uppercase;
            font-weight: bold;
          }
          .receipt-subtitle {
            font-size: 12px;
            color: #666;
          }
          .info-section {
            margin-bottom: 15px;
            font-size: 12px;
          }
          .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
          }
          .info-label {
            font-weight: bold;
          }
          .divider {
            border-top: 1px dashed #ccc;
            margin: 10px 0;
          }
          .item-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 12px;
          }
          .item-table th, .item-table td {
            text-align: left;
            padding: 5px;
          }
          .item-table th {
            border-bottom: 1px solid #ddd;
          }
          .item-row td {
            border-bottom: 1px dotted #eee;
          }
          .total-row {
            font-weight: bold;
            border-top: 1px dashed #ccc;
            border-bottom: 1px dashed #ccc;
          }
          .total-section {
            margin: 10px 0;
            font-size: 14px;
          }
          .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
          }
          .grand-total {
            font-weight: bold;
            font-size: 16px;
            margin-top: 5px;
            padding-top: 5px;
            border-top: 1px solid #333;
          }
          .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 11px;
            color: #666;
          }
          .barcode {
            text-align: center;
            margin: 10px 0;
            font-family: 'Libre Barcode 39', cursive;
            font-size: 40px;
            letter-spacing: -1px;
          }
          .store-details {
            text-align: center;
            font-size: 10px;
            color: #666;
            margin-bottom: 10px;
          }
          .timestamp {
            text-align: right;
            font-size: 10px;
            color: #999;
            margin-top: 5px;
          }
          .receipt-edge {
            position: relative;
            height: 20px;
            margin-bottom: -1px;
            margin-top: -1px;
            background-image: 
              linear-gradient(45deg, white 25%, transparent 25%),
              linear-gradient(-45deg, white 25%, transparent 25%);
            background-size: 10px 10px;
            background-color: #f9f9f9;
            z-index: 5;
          }
        </style>
      </head>
      <body>
        <div class="receipt-container">
          <div class="receipt">
            <div class="receipt-header">
              <div class="logo" style="color: #2563eb;">MCDevHub</div>
              <div class="receipt-title">Kvittering</div>
              <div class="receipt-subtitle">Danmarks førende Minecraft løsninger</div>
              <div class="store-details">
                MCDevHub.dk · <EMAIL>
              </div>
            </div>
            
            <div class="info-section">
              <div class="info-row">
                <span class="info-label">Kvittering nr.:</span>
                <span>${receiptNumber}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Dato:</span>
                <span>${purchaseDate}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Kunde:</span>
                <span>${user?.name || 'Kunde'}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Email:</span>
                <span>${user?.email || ''}</span>
              </div>
            </div>
            
            <div class="divider"></div>
            
            <table class="item-table">
              <thead>
                <tr>
                  <th>Produkt</th>
                  <th>Type</th>
                  <th style="text-align:right">Pris</th>
                </tr>
              </thead>
              <tbody>
                <tr class="item-row">
                  <td>${purchase.productName}</td>
                  <td>${productType ? getProductTypeLabel(productType) : 'Andet'}</td>
                  <td style="text-align:right">${purchase.amount} DKK</td>
                </tr>
              </tbody>
            </table>
            
            <div class="total-section">
              <div class="total-line">
                <span>Subtotal:</span>
                <span>${purchase.amount} DKK</span>
              </div>
              <div class="total-line">
                <span>Moms (0%):</span>
                <span>0.00 DKK</span>
              </div>
              <div class="total-line grand-total">
                <span>Total:</span>
                <span>${purchase.amount} DKK</span>
              </div>
            </div>
            
            <div class="divider"></div>
            
            <div class="info-row">
              <span class="info-label">Betalingsmetode:</span>
              <span>${purchase.paymentMethod || 'Balance'}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">Ordre ID:</span>
              <span>${purchase._id}</span>
            </div>
            
            <div class="barcode">*${receiptNumber}*</div>
            
            <div class="footer">
              <p>Tak for dit køb hos MCDevHub!</p>
              <p>https://mcdevhub.dk</p>
              <p>Dette er en digital kvittering og er gyldig uden underskrift.</p>
            </div>
            
            <div class="timestamp">
              Udstedt: ${new Date().toLocaleString('da-DK')}
            </div>
          </div>
          <div class="receipt-edge"></div>
        </div>
      </body>
      </html>
    `;
    
    // Generate PDF using html2pdf.js (we need to dynamically import it)
    try {
      // First try - attempt to use html2pdf
      try {
        // Using dynamic import with type casting to avoid TypeScript errors
        const html2pdfModule = await import('html2pdf.js') as any;
        const html2pdf = html2pdfModule.default || html2pdfModule;
        
        if (html2pdf) {
          // Create a temporary container for the HTML
          const element = document.createElement('div');
          element.innerHTML = receiptContent;
          document.body.appendChild(element);
          
          // Generate PDF
          const options = {
            margin: 0,
            filename: `kvittering-${receiptNumber}.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 2 },
            jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
          };
          
          html2pdf()
            .from(element)
            .set(options)
            .save()
            .then(() => {
              document.body.removeChild(element);
            });
          
          return;
        }
      } catch (error) {
        console.error('Error generating PDF with html2pdf:', error);
      }
      
      // Fallback - download as HTML file
      console.warn('Falling back to HTML receipt download');
      const blob = new Blob([receiptContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      
      // Create an invisible link and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.download = `kvittering-${receiptNumber}.html`;
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      // Show notification to user that they need to install html2pdf
      alert('For at generere en PDF-fil skal du installere html2pdf.js biblioteket. Indtil da vil kvitteringen blive downloadet som en HTML-fil.');
    } catch (error) {
      console.error('Error generating receipt:', error);
      alert('Der opstod en fejl ved generering af kvittering. Prøv igen senere.');
    }
  };
  
  const [description, setDescription] = useState('');
  const [loadingDescription, setLoadingDescription] = useState(false);
  const [descriptionSuccess, setDescriptionSuccess] = useState(false);
  const [descriptionError, setDescriptionError] = useState('');
  const [adminUserData, setAdminUserData] = useState<{ 
    username?: string; 
    admintype?: string; 
    description?: string;
    githubUsername?: string;
    youtubeUrl?: string;
    email?: string;
    discordUserId?: string;
  } | null>(null);
  const [isDescriptionLoaded, setIsDescriptionLoaded] = useState(false);
  const [isAutosaving, setIsAutosaving] = useState(false);
  const [autosaveIndicator, setAutosaveIndicator] = useState(false);
  const [saveConfirmation, setSaveConfirmation] = useState(false);
  // New state to track if admin access has been confirmed
  const [hasAdminAccessBeenConfirmed, setHasAdminAccessBeenConfirmed] = useState(false);

  // Availability state management
  const [openForTasks, setOpenForTasks] = useState(false);
  const [loadingAvailability, setLoadingAvailability] = useState(false);
  const [availabilityError, setAvailabilityError] = useState('');
  const [isAvailabilityLoaded, setIsAvailabilityLoaded] = useState(false);

  // Track description history for undo functionality
  const [descriptionHistory, setDescriptionHistory] = useState<string[]>([]);
  const [currentHistoryIndex, setCurrentHistoryIndex] = useState(-1);
  
  // Add to history whenever description changes significantly
  const addToHistory = useCallback((newDescription: string) => {
    if (descriptionHistory.length === 0 || descriptionHistory[currentHistoryIndex] !== newDescription) {
      // Add new state to history, truncating any future history if we're not at the end
      const newHistory = [...descriptionHistory.slice(0, currentHistoryIndex + 1), newDescription];
      
      // Limit history size to 50 entries
      if (newHistory.length > 50) {
        newHistory.shift();
      }
      
      setDescriptionHistory(newHistory);
      setCurrentHistoryIndex(newHistory.length - 1);
    }
  }, [descriptionHistory, currentHistoryIndex]);
  
  // Function to undo changes (go back in history)
  const undoChanges = useCallback(() => {
    if (currentHistoryIndex > 0) {
      const newIndex = currentHistoryIndex - 1;
      setCurrentHistoryIndex(newIndex);
      setDescription(descriptionHistory[newIndex]);
    }
  }, [descriptionHistory, currentHistoryIndex]);
  
  // Function to redo changes (go forward in history)
  const redoChanges = useCallback(() => {
    if (currentHistoryIndex < descriptionHistory.length - 1) {
      const newIndex = currentHistoryIndex + 1;
      setCurrentHistoryIndex(newIndex);
      setDescription(descriptionHistory[newIndex]);
    }
  }, [descriptionHistory, currentHistoryIndex]);

  // Always show loading on mount, even with cache
  const [initialLoading, setInitialLoading] = useState(true);
  useEffect(() => {
    // Show spinner for at least 400ms for a consistent UX
    const timeout = setTimeout(() => setInitialLoading(false), 100);
    return () => clearTimeout(timeout);
  }, []);

  // Check for products when component mounts to ensure tab shows immediately
  useEffect(() => {
    // If we have products in the cache, make sure hasProducts is set to true
    if (globalCache.products && globalCache.products.length > 0) {
      console.log('Using cached products on mount, setting hasProducts flag');
      setHasProducts(true);
    }
  }, []);

  // Immediately fetch verification status (high priority)
  const fetchVerificationStatus = useCallback(async () => {
    if (!user || verifyFetchingRef.current) return;
    
    // Use cache if available and recent (5 minutes)
    const currentTime = Date.now();
    const cacheAge = currentTime - globalCache.lastFetch;
    if (cacheAge < 5 * 60 * 1000 && globalCache.verificationStatus !== null) {
      setVerificationStatus(globalCache.verificationStatus);
      return;
    }
    
    verifyFetchingRef.current = true;
    setIsLoadingVerification(true);
    
    try {
      const response = await fetch('/api/verified');
      if (response.ok) {
        const data = await response.json();
        const verificationData = {
          isVerified: data.isVerified,
          verifiedAt: data.verifiedAt
        };
        
        // Update state and cache
        setVerificationStatus(verificationData);
        globalCache.verificationStatus = verificationData;
        globalCache.lastFetch = Date.now();
        updateSessionCache(globalCache);
      }
    } catch (error) {
      console.error('Error fetching verification status:', error);
    } finally {
      verifyFetchingRef.current = false;
      setIsLoadingVerification(false);
    }
  }, [user]);

  // Fetch products data (lower priority)
  const fetchUserProducts = useCallback(async () => {
    if (!user || productsFetchingRef.current) return;
    
    // First, check the cache and use it immediately if available
    // This ensures we show something right away even while we're fetching fresh data
    if (globalCache.products && globalCache.products.length > 0) {
      setProducts(globalCache.products);
      setHasProducts(true);
    }
    
    // Use cache if available and recent (5 minutes) and don't fetch again
    const currentTime = Date.now();
    const cacheAge = currentTime - globalCache.lastFetch;
    if (cacheAge < 5 * 60 * 1000 && globalCache.products.length > 0) {
      return;
    }
    
    productsFetchingRef.current = true;
    setIsLoadingProducts(true);
    
    const discordId = user.id;
    if (!discordId) {
      productsFetchingRef.current = false;
      setIsLoadingProducts(false);
      return;
    }
    
    try {
      const response = await fetch(`/api/products/user?discordId=${discordId}`);
      if (response.ok) {
        const data = await response.json();
        
        // Memory optimization: Clear old large objects before assigning new ones
        // This helps garbage collection clean up old references
        globalCache.products = [];
        
        // Update state and cache
        setProducts(data);
        const hasProductsValue = data.length > 0;
        setHasProducts(hasProductsValue);
        
        globalCache.products = data;
        globalCache.hasProducts = hasProductsValue;
        globalCache.lastFetch = Date.now();
        updateSessionCache(globalCache);
      }
    } catch (error) {
      console.error('Error fetching user products:', error);
    } finally {
      productsFetchingRef.current = false;
      setIsLoadingProducts(false);
    }
  }, [user]);

  // Add a function to fetch the user's MongoDB ID after the fetchVerificationStatus function
  const fetchUserMongoId = useCallback(async () => {
    if (!user) return;
    
    setLoadingUserId(true);
    
    try {
      // Get the Discord ID from the user metadata
      const discordId = user.id;
      
      if (!discordId) {
        setLoadingUserId(false);
        return;
      }
      
      // Fetch the user data from the API
      const response = await fetch(`/api/users/get-user-by-discord?discordId=${discordId}`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.user) {
          if (data.user._id) {
            setMongoDbUserId(data.user._id);
          }
          if (data.user.createdAt) {
            setUserCreatedAt(data.user.createdAt);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching user MongoDB ID:', error);
    } finally {
      setLoadingUserId(false);
    }
  }, [user]);

  // Priority-based data loading - verification first, then products
  useEffect(() => {
    if (!user) return;
    
    // Reset timeout for safety
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Set safety timeout to clear any stuck loading state after 10 seconds
    timeoutRef.current = setTimeout(() => {
      setIsLoadingVerification(false);
      setIsLoadingProducts(false);
      verifyFetchingRef.current = false;
      productsFetchingRef.current = false;
    }, 10000);
    
    // Fetch verification status immediately (high priority)
    fetchVerificationStatus();
    
    // Fetch products immediately too - don't delay anymore
    fetchUserProducts();
    
    // Fetch user MongoDB ID
    fetchUserMongoId();
    
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [user, fetchVerificationStatus, fetchUserProducts, fetchUserMongoId]);

  // Cleanup function to prevent memory leaks
  useEffect(() => {
    return () => {
      // Clear references when component unmounts
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      
      // Clear large arrays from memory when navigating away
      if (globalCache.products && globalCache.products.length > 10) {
        // Keep only essential data for next visit
        const essentialProducts = globalCache.products.slice(0, 10).map(product => ({
          _id: product._id,
          projectName: product.projectName,
          productType: product.productType,
          price: product.price,
          status: product.status,
          // Only keep first screenshot URL for thumbnail
          screenshotUrls: product.screenshotUrls ? 
            [product.screenshotUrls[0]].filter(Boolean) : []
        }));
        globalCache.products = essentialProducts;
        updateSessionCache(globalCache);
      }
    };
  }, []);

  // Handle tab changes
  const handleTabChange = (tab: TabType) => {
    // Don't allow switching to products, profile, or transactions tab if user is not an admin
    if ((tab === 'products' || tab === 'profile' || tab === 'transactions') && !isAdmin) {
      return;
    }
    
    setActiveTab(tab);
    
    // Save current tab to sessionStorage (persists during page refresh)
    globalCache.activeTab = tab;
    updateSessionCache(globalCache);
    
    // Update the URL with the tab parameter (without reloading)
    // If tab is purchases, keep URL clean at /profile
    if (tab === 'purchases') {
      router.replace('/profile', { scroll: false });
    } else {
      router.replace(`?tab=${tab}`, { scroll: false });
    }
    
    // If switching to the products tab and we don't have products yet, refetch
    if (tab === 'products' && products.length === 0 && !isLoadingProducts && !productsFetchingRef.current) {
      // Force a new fetch even if the cache timer hasn't expired
      console.log('Tab changed to products, triggering fetch');
      fetchUserProducts();
    }
  };

  // Banner customization state
  const [bannerColor, setBannerColor] = useState<string>('#2563eb'); // default blue
  const [showBannerColorPicker, setShowBannerColorPicker] = useState(false);
  const [bannerTexts, setBannerTexts] = useState<Array<{
    id: string;
    text: string;
    color: string;
    x: number;
    y: number;
    editing: boolean;
    showColor: boolean;
    bold?: boolean;
  }>>([]);
  const [activeTextId, setActiveTextId] = useState<string | null>(null);
  const [menuOpen, setMenuOpen] = useState(false); // true if any picker or text edit is open
  const bannerRef = useRef<HTMLDivElement>(null);
  const dragData = useRef<{ id: string; offsetX: number; offsetY: number } | null>(null);
  
  // Add state for transaction summary data
  const [totalIncome, setTotalIncome] = useState(0);
  const [transactionCount, setTransactionCount] = useState(0);
  const [feePercentage] = useState(15); // Keep this as a state variable in case it needs to be dynamic in the future
  
  // Add a function to fetch transaction summary data
  const fetchTransactionSummary = useCallback(async () => {
    if (!user) return;
    
    try {
      const response = await fetch('/api/users/transactions/summary');
      
      if (response.ok) {
        const data = await response.json();
        setTotalIncome(data.totalIncome || 0);
        setTransactionCount(data.transactionCount || 0);
      }
    } catch (error) {
      console.error('Error fetching transaction summary:', error);
    }
  }, [user]);
  
  // Call fetchTransactionSummary when the component mounts or when the activeTab changes to 'transactions'
  useEffect(() => {
    if (activeTab === 'transactions' && user) {
      fetchTransactionSummary();
    }
  }, [activeTab, user, fetchTransactionSummary]);
  
  // Add text to banner (does NOT trigger upload)
  const handleAddText = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    const id = Math.random().toString(36).substr(2, 9);
    setBannerTexts([
      ...bannerTexts,
      { id, text: 'Din tekst', color: '#fff', x: 100, y: 30, editing: true, showColor: false }
    ]);
    setActiveTextId(id);
    setMenuOpen(true);
  };

  // Remove text from banner
  const handleRemoveText = (id: string) => {
    setBannerTexts(bannerTexts => bannerTexts.filter(t => t.id !== id));
    if (activeTextId === id) setActiveTextId(null);
  };

  // Toggle text color picker
  const toggleTextColorPicker = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setBannerTexts(bannerTexts => bannerTexts.map(t => t.id === id ? { ...t, showColor: !t.showColor, editing: false } : { ...t, showColor: false }));
    setActiveTextId(id);
    setMenuOpen(v => !v);
  };

  // Mouse drag events for moving text (only if not editing or picking color)
  const handleTextMouseDown = (id: string, e: React.MouseEvent) => {
    const textObj = bannerTexts.find(t => t.id === id);
    if (!textObj || textObj.editing || textObj.showColor) return;
    e.stopPropagation();
    const rect = bannerRef.current?.getBoundingClientRect();
    if (!rect) return;
    dragData.current = {
      id,
      offsetX: e.clientX - rect.left - (textObj?.x ?? 0),
      offsetY: e.clientY - rect.top - (textObj?.y ?? 0),
    };
    document.addEventListener('mousemove', handleTextMouseMove);
    document.addEventListener('mouseup', handleTextMouseUp);
  };

  const handleTextMouseMove = (e: MouseEvent) => {
    if (!dragData.current) return;
    const { id, offsetX, offsetY } = dragData.current;
    const rect = bannerRef.current?.getBoundingClientRect();
    if (!rect) return;
    const x = Math.max(0, Math.min(e.clientX - rect.left - offsetX, rect.width - 60));
    const y = Math.max(0, Math.min(e.clientY - rect.top - offsetY, rect.height - 30));
    setBannerTexts(bannerTexts => bannerTexts.map(t => t.id === id ? { ...t, x, y } : t));
  };

  const handleTextMouseUp = () => {
    dragData.current = null;
    document.removeEventListener('mousemove', handleTextMouseMove);
    document.removeEventListener('mouseup', handleTextMouseUp);
  };

  // Banner color picker toggle
  const handleBannerColorPicker = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowBannerColorPicker(v => !v);
    setMenuOpen(v => !v);
  };

  // Banner click for upload (only if no menu is open)
  const handleBannerClick = (e: React.MouseEvent) => {
    if (menuOpen) return;
    document.getElementById('banner-upload')?.click();
  };

  const handleBannerChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setBannerUploading(true);
    setBannerError(null);
    try {
      const data = new FormData();
      data.append('file', file);
      data.append('upload_preset', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET!);
      data.append('cloud_name', process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME!);
      const res = await fetch(`https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload`, {
        method: 'POST',
        body: data,
      });
      const json = await res.json();
      if (!json.secure_url) throw new Error('Upload failed');
      const updateRes = await fetch('/api/freelancers/update-banner', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ bannerUrl: json.secure_url }),
      });
      if (!updateRes.ok) throw new Error('Failed to update banner');
      // Optionally: reload or refetch user/profile data here
    } catch (err: any) {
      setBannerError(err.message || 'Fejl ved upload');
    } finally {
      setBannerUploading(false);
    }
  };

  // Track if description has been loaded
  const [descriptionLoaded, setDescriptionLoaded] = useState(false);
  useEffect(() => {
    if (description) {
      setDescriptionLoaded(true);
      
      // Initialize history with initial description
      if (descriptionHistory.length === 0) {
        setDescriptionHistory([description]);
        setCurrentHistoryIndex(0);
      }
    }
  }, [description, descriptionHistory.length]);

  // Fetch admin user data if the user is an admin
  useEffect(() => {
    const fetchAdminData = async () => {
      if (!isAdmin) return;
      
      try {
        // First try to get description directly from freelancers endpoint
        try {
          const descResponse = await fetch('/api/freelancers/get-description');
          if (descResponse.ok) {
            const descData = await descResponse.json();
            
            if (descData.description !== undefined) {
              setDescription(descData.description);
            }
          }
        } catch (descError) {
          // Silently continue if this fails
        }
        
        // Now fetch the admin data by Discord ID (no admin login required)
        const response = await fetch('/api/user/admin-data');
        
        if (response.ok) {
          const data = await response.json();
          
          // Set admin user data
          setAdminUserData(data.user);
          
          // Mark admin access as confirmed
          setHasAdminAccessBeenConfirmed(true);
          
          // If we didn't get the description earlier, try to get it from admin data
          if (!descriptionLoaded && data.user && data.user.description) {
            setDescription(data.user.description);
          }
        } else if (response.status === 404) {
          // User not found in adminusers collection, but they might be an admin
          // Set default empty data that will be populated when they update social links
          setAdminUserData({
            username: user?.name || '',
            admintype: '',
            description: '',
            discordUserId: user?.id || '',
            email: user?.email || '',
            githubUsername: '',
            youtubeUrl: ''
          });
          
          // Still mark admin access as confirmed since they passed the isAdmin check
          setHasAdminAccessBeenConfirmed(true);
        }
        
        // Mark description as loaded
        setIsDescriptionLoaded(true);
      } catch (error) {
        console.error('Error fetching admin data:', error);
        setIsDescriptionLoaded(true);
      }
    };
    
    fetchAdminData();
  }, [isAdmin, descriptionLoaded, user]);

  // Autosave functionality with debounce
  useEffect(() => {
    // Skip if description isn't loaded yet or if we're already saving
    if (!isDescriptionLoaded || loadingDescription || !description) return;
    
    // Skip if this is just the initial load
    if (descriptionHistory.length <= 1) return;
    
    const timer = setTimeout(() => {
      // Set autosaving indicator
      setIsAutosaving(true);
      setAutosaveIndicator(true);
      setSaveConfirmation(false);
      
      // Save to database using our new endpoint
      fetch('/api/user/update-description', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ description }),
      })
      .then(response => {
        if (response.ok) {
          return response.json();
        } else {
          throw new Error('Failed to save description');
        }
      })
      .then(data => {
        // Update admin user data with the new description
        setAdminUserData(prev => {
          if (!prev) return null;
          return { ...prev, description };
        });
        
        // Flash the autosave indicator for 1 second
        setTimeout(() => {
          setAutosaveIndicator(false);
          // Show save confirmation after autosave indicator disappears
          setSaveConfirmation(true);
          // Hide confirmation after 2 seconds
          setTimeout(() => {
            setSaveConfirmation(false);
          }, 2000);
        }, 1000);
      })
      .catch(error => {
        console.error('Error saving description:', error);
        setDescriptionError('Kunne ikke gemme beskrivelsen');
        // Hide error after 3 seconds
        setTimeout(() => {
          setDescriptionError('');
        }, 3000);
      })
      .finally(() => {
        setIsAutosaving(false);
      });
    }, 1000);
    
    // Clean up timer on component unmount or when description changes again
    return () => clearTimeout(timer);
  }, [description, isDescriptionLoaded, loadingDescription, descriptionHistory.length]);

  // Handle description update - now for manual saves only
  // This function is no longer needed since we have autosave

  // Fetch availability status
  const fetchAvailabilityStatus = useCallback(async () => {
    if (!user || !isAdmin) return;

    setLoadingAvailability(true);
    setAvailabilityError('');

    try {
      const response = await fetch('/api/user/update-availability');

      if (response.ok) {
        const data = await response.json();
        setOpenForTasks(data.openForTasks || false);
        setIsAvailabilityLoaded(true);
      } else if (response.status === 404) {
        // User not found, default to false
        setOpenForTasks(false);
        setIsAvailabilityLoaded(true);
      } else {
        throw new Error('Failed to fetch availability status');
      }
    } catch (error) {
      console.error('Error fetching availability status:', error);
      setAvailabilityError('Kunne ikke hente tilgængeligheds status');
      setOpenForTasks(false); // Default to false on error
      setIsAvailabilityLoaded(true);
    } finally {
      setLoadingAvailability(false);
    }
  }, [user, isAdmin]);

  // Update availability status
  const updateAvailabilityStatus = async (newStatus: boolean) => {
    if (!user || !isAdmin) return;

    setLoadingAvailability(true);
    setAvailabilityError('');

    try {
      const response = await fetch('/api/user/update-availability', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ openForTasks: newStatus }),
      });

      if (response.ok) {
        const data = await response.json();
        setOpenForTasks(data.openForTasks);
      } else {
        throw new Error('Failed to update availability status');
      }
    } catch (error) {
      console.error('Error updating availability status:', error);
      setAvailabilityError('Kunne ikke opdatere tilgængeligheds status');
      // Revert the toggle state on error
      setOpenForTasks(!newStatus);
    } finally {
      setLoadingAvailability(false);
    }
  };

  // Fetch availability status when admin access is confirmed
  useEffect(() => {
    if (isAdmin && hasAdminAccessBeenConfirmed && !isAvailabilityLoaded) {
      fetchAvailabilityStatus();
    }
  }, [isAdmin, hasAdminAccessBeenConfirmed, isAvailabilityLoaded, fetchAvailabilityStatus]);

  // Auto-trigger badge check when profile tab is active (with cooldown)
  useEffect(() => {
    const checkBadges = async () => {
      if (activeTab === 'profile' && user && !isCheckingBadges) {
        const currentTime = Date.now();
        const cooldownPeriod = 60 * 1000; // 1 minute in milliseconds

        // Check if enough time has passed since last badge check
        if (currentTime - lastBadgeCheckTime < cooldownPeriod) {
          console.log('Badge check skipped - cooldown period active. Next check available in:',
            Math.ceil((cooldownPeriod - (currentTime - lastBadgeCheckTime)) / 1000), 'seconds');
          return;
        }

        setIsCheckingBadges(true);
        setLastBadgeCheckTime(currentTime);

        try {
          const response = await fetch('/api/badges', { method: 'POST' });
          const data = await response.json();

          // Silently refresh the badge display without showing alerts
          if (data.success) {
            // Force a refresh of the BadgeDisplay component by triggering a re-render
            // This will happen automatically when the component re-mounts
            console.log('Badge check completed:', data.message);
          }
        } catch (error) {
          console.error('Error checking badges:', error);
          // Silently fail - don't show error to user for auto-check
        } finally {
          setIsCheckingBadges(false);
        }
      }
    };

    checkBadges();
  }, [activeTab, user, isCheckingBadges, lastBadgeCheckTime]);

  // Add these state variables for social link editing
  const [isEditingGithub, setIsEditingGithub] = useState(false);
  const [isEditingYoutube, setIsEditingYoutube] = useState(false);
  const [isEditingEmail, setIsEditingEmail] = useState(false);
  const [githubInput, setGithubInput] = useState('');
  const [youtubeInput, setYoutubeInput] = useState('');
  const [emailInput, setEmailInput] = useState('');
  const [socialLinkSuccess, setSocialLinkSuccess] = useState<string | null>(null);
  const [socialLinkError, setSocialLinkError] = useState<string | null>(null);

  // Initialize social link inputs when admin data is loaded
  useEffect(() => {
    if (adminUserData) {
      setGithubInput(adminUserData.githubUsername || '');
      setYoutubeInput(adminUserData.youtubeUrl || '');
      setEmailInput(adminUserData.email || user?.email || '');
    }
  }, [adminUserData, user]);

  // Function to update social links
  const updateSocialLink = async (type: 'github' | 'youtube' | 'email', value: string) => {
    setSocialLinkSuccess(null);
    setSocialLinkError(null);
    
    try {
      const response = await fetch('/api/user/update-social-links', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          type, 
          value 
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update social link');
      }
      
      // Update admin user data locally
      setAdminUserData(prev => {
        if (!prev) return null;
        return { 
          ...prev, 
          ...(type === 'github' ? { githubUsername: value } : {}),
          ...(type === 'youtube' ? { youtubeUrl: value } : {}),
          ...(type === 'email' ? { email: value } : {})
        };
      });
      
      setSocialLinkSuccess(`${type === 'github' ? 'GitHub' : type === 'youtube' ? 'YouTube' : 'Email'} opdateret`);
      setTimeout(() => setSocialLinkSuccess(null), 3000);
      
      // Close edit mode
      if (type === 'github') {
        setIsEditingGithub(false);
      } else if (type === 'youtube') {
        setIsEditingYoutube(false);
      } else {
        setIsEditingEmail(false);
      }
    } catch (error) {
      console.error(`Error updating ${type}:`, error);
      setSocialLinkError(`Kunne ikke opdatere ${type === 'github' ? 'GitHub' : type === 'youtube' ? 'YouTube' : 'Email'}`);
      setTimeout(() => setSocialLinkError(null), 3000);
    }
  };

  // New state for notifications
  const [notifications, setNotifications] = useState<Array<{
    _id: string;
    title: string;
    message: string;
    date: string;
    read?: boolean;
    readAt?: string;
    link?: string;
  }>>([]);
  const [loadingNotifications, setLoadingNotifications] = useState(false);
  const [notificationsError, setNotificationsError] = useState<string | null>(null);

  // New state for notification filters
  const [activeFilter, setActiveFilter] = useState<'all' | 'unread'>('all');
  const [isMarkingAllAsRead, setIsMarkingAllAsRead] = useState(false);
  const [isClearingNotifications, setIsClearingNotifications] = useState(false);

  // Group notifications by date
  const groupedNotifications = useMemo(() => {
    if (!notifications || !Array.isArray(notifications) || notifications.length === 0) return null;
    
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      const lastWeek = new Date(today);
      lastWeek.setDate(lastWeek.getDate() - 7);
      
      // Filter notifications based on the active filter
      // Add defensive check for notification validity 
      const filteredNotifications = activeFilter === 'unread' 
        ? notifications.filter(notification => 
            notification && 
            typeof notification === 'object' && 
            notification._id && 
            !notification.read)
        : notifications.filter(notification => 
            notification && 
            typeof notification === 'object' && 
            notification._id);
      
      // Group by time period
      return {
        today: filteredNotifications.filter(notification => {
          try {
            const notifDate = new Date(notification.date);
            return notifDate >= today;
          } catch (error) {
            console.error("Error parsing date for notification:", notification);
            return false;
          }
        }),
        yesterday: filteredNotifications.filter(notification => {
          try {
            const notifDate = new Date(notification.date);
            return notifDate >= yesterday && notifDate < today;
          } catch (error) {
            console.error("Error parsing date for notification:", notification);
            return false;
          }
        }),
        thisWeek: filteredNotifications.filter(notification => {
          try {
            const notifDate = new Date(notification.date);
            return notifDate >= lastWeek && notifDate < yesterday;
          } catch (error) {
            console.error("Error parsing date for notification:", notification);
            return false;
          }
        }),
        older: filteredNotifications.filter(notification => {
          try {
            const notifDate = new Date(notification.date);
            return notifDate < lastWeek;
          } catch (error) {
            console.error("Error parsing date for notification:", notification);
            return false;
          }
        })
      };
    } catch (error) {
      console.error("Error grouping notifications:", error);
      return null;
    }
  }, [notifications, activeFilter]);

  // Fetch notifications
  const fetchNotifications = async (retryCount = 0) => {
    if (activeTab !== 'notifications' && retryCount === 0) return;
    
    try {
      setLoadingNotifications(true);
      setNotificationsError(null);
      
      console.log('Profile page: Fetching notifications...');
      const response = await fetch('/api/user/notifications', {
        // Adding cache: 'no-store' to prevent caching issues
        cache: 'no-store',
        // Setting a longer timeout
        signal: AbortSignal.timeout(15000) // 15 seconds timeout
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch notifications: ${response.status} ${errorText}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log(`Profile page: Successfully fetched ${data.notifications.length} notifications`);
        
        // Ensure notifications have all required fields
        const validatedNotifications = (data.notifications || []).filter(n => n && n._id && n.title && n.date);
        
        if (validatedNotifications.length === 0 && data.notifications.length > 0) {
          console.warn("All notifications were filtered out due to missing required fields");
        }
        
        setNotifications(validatedNotifications);
      } else {
        throw new Error(data.error || 'Failed to fetch notifications');
      }
    } catch (error) {
      console.error('Error fetching notifications in profile page:', error);
      
      // If it's a timeout or network error and we haven't retried too many times, retry
      if ((error instanceof Error && 
          (error.name === 'AbortError' || 
            error.message.includes('network') || 
            error.message.includes('timeout'))) && 
          retryCount < 2) {
        
        console.log(`Profile page: Retrying notification fetch (attempt ${retryCount + 1})...`);
        
        // Wait a bit before retrying (exponential backoff)
        const backoffTime = Math.pow(2, retryCount) * 1000;
        setTimeout(() => {
          fetchNotifications(retryCount + 1);
        }, backoffTime);
        
        return;
      }
      
      // If retries failed or max retries reached, set error and provide fallback test notifications
      setNotificationsError(error instanceof Error ? error.message : 'Something went wrong');
      
      // Add fallback test notifications if we're getting persistent errors
      if (retryCount >= 2) {
        console.log("Using fallback test notifications after repeated failures");
        setNotifications([
          {
            _id: "test1",
            title: "Ny produkt tilgængelig: Survival Spawn Pack",
            message: "En ny spawn pakke der passer perfekt til survival servere er nu tilgængelig i butikken.",
            date: new Date().toISOString(),
            read: false,
            link: "/products"
          },
          {
            _id: "test2", 
            title: "Velkommen til MC DevHub",
            message: "Tak for din tilmelding! Vi er glade for at have dig med i vores fællesskab.",
            date: new Date(Date.now() - 86400000).toISOString(), // Yesterday
            read: true
          }
        ]);
        // Clear the error since we're providing fallback content
        setNotificationsError(null);
      }
    } finally {
      setLoadingNotifications(false);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (isMarkingAllAsRead || !notifications.some(n => !n.read)) return;
    
    try {
      setIsMarkingAllAsRead(true);
      
      console.log('Profile page: Marking all notifications as read...');
      const response = await fetch('/api/user/notifications/mark-all-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        // Setting a longer timeout
        signal: AbortSignal.timeout(10000) // 10 seconds timeout
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to mark all as read: ${response.status} ${errorText}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log(`Profile page: Successfully marked ${data.modifiedCount} notifications as read`);
        
        // Update local state
        setNotifications(prevNotifications => 
          prevNotifications.map(notification => ({
            ...notification,
            read: true,
            readAt: new Date().toISOString()
          }))
        );
        
        // Dispatch custom event
        const event = new CustomEvent('allNotificationsRead');
        window.dispatchEvent(event);
      } else {
        throw new Error(data.error || 'Failed to mark all notifications as read');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      
      // Show a simple error message to the user
      alert('Could not mark all notifications as read. Please try again later.');
    } finally {
      setIsMarkingAllAsRead(false);
    }
  };

  // Clear all notifications
  const clearAllNotifications = async () => {
    if (isClearingNotifications || notifications.length === 0) return;

    // Confirm with user
    if (!confirm('Er du sikker på at du vil slette alle notifikationer? Dette kan ikke fortrydes.')) {
      return;
    }

    try {
      setIsClearingNotifications(true);

      console.log('Profile page: Clearing all notifications...');
      const response = await fetch('/api/user/notifications/clear-all', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(10000) // 10 seconds timeout
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to clear all notifications: ${response.status} ${errorText}`);
      }

      const data = await response.json();

      if (data.success) {
        console.log(`Profile page: Successfully cleared ${data.deletedCount} notifications`);

        // Update local state
        setNotifications([]);

        // Dispatch custom event
        const event = new CustomEvent('allNotificationsCleared');
        window.dispatchEvent(event);
      } else {
        throw new Error(data.error || 'Failed to clear all notifications');
      }
    } catch (error) {
      console.error('Error clearing all notifications:', error);

      // Show a simple error message to the user
      alert('Could not clear all notifications. Please try again later.');
    } finally {
      setIsClearingNotifications(false);
    }
  };

  // Format relative time - for headers
  const formatRelativeDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: da });
    } catch (error) {
      return dateString;
    }
  };

  // Mark notification as read
  const markAsRead = async (notificationId: string) => {
    console.log('Marking notification as read:', notificationId);
    
    try {
      const response = await fetch('/api/user/notifications/mark-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notificationId }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        console.log('Successfully marked notification as read:', data);
        
        // Update local state
        setNotifications(prevNotifications => 
          prevNotifications.map(notification => 
            notification._id === notificationId 
              ? { ...notification, read: true, readAt: new Date().toISOString() } 
              : notification
          )
        );
        
        // Dispatch custom event
        const event = new CustomEvent('notificationRead', { 
          detail: { notificationId } 
        });
        window.dispatchEvent(event);
      } else {
        console.error('Failed to mark notification as read:', data.error);
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
    
    // Show a toast or notification to the user
    // You could add a toast notification here if you have a toast system
  };

  // Handler for "all notifications read" custom event
  const handleAllNotificationsRead = () => {
    console.log('Profile page received allNotificationsRead event');
    
    // Update all notifications to read
    setNotifications(prevNotifications => 
      prevNotifications.map(notification => ({
        ...notification,
        read: true,
        readAt: new Date().toISOString()
      }))
    );
  };

  // Listen for notification read events from other components
  useEffect(() => {
    const handleNotificationReadEvent = (event: CustomEvent) => {
      const { notificationId } = event.detail;
      console.log('Profile page received notificationRead event:', notificationId);
      
      // Only update if we have loaded notifications
      if (notifications.length > 0) {
        setNotifications(prevNotifications => 
          prevNotifications.map(notification => 
            notification._id === notificationId 
              ? { ...notification, read: true, readAt: new Date().toISOString() } 
              : notification
          )
        );
      }
    };
    
    // Handle event when all notifications are marked as read
    const handleAllNotificationsReadEvent = () => {
      console.log('Profile page received allNotificationsRead event');
      if (notifications.length > 0) {
        setNotifications(prevNotifications => 
          prevNotifications.map(notification => ({
            ...notification,
            read: true,
            readAt: new Date().toISOString()
          }))
        );
      }
    };
    
    // Add event listeners
    window.addEventListener('notificationRead', handleNotificationReadEvent as EventListener);
    window.addEventListener('allNotificationsRead', handleAllNotificationsReadEvent as EventListener);
    
    return () => {
      // Remove event listeners
      window.removeEventListener('notificationRead', handleNotificationReadEvent as EventListener);
      window.removeEventListener('allNotificationsRead', handleAllNotificationsReadEvent as EventListener);
    };
  }, [notifications]);

  // Fetch notifications on mount and add allNotificationsRead event listener
  useEffect(() => {
    // Fetch with a more robust error handling
    const fetchAndHandleErrors = async () => {
      try {
        await fetchNotifications();
      } catch (err) {
        console.error("Error in initial notification fetch:", err);
        setNotificationsError(err instanceof Error ? err.message : "Failed to load notifications");
        setLoadingNotifications(false);
      }
    };
    
    fetchAndHandleErrors();
  }, []);

  // Separate effect to refresh when tab changes
  useEffect(() => {
    if (activeTab === 'notifications') {
      // Don't fetch again if we're already loading or have notifications
      if (!loadingNotifications && (notifications.length === 0 || notificationsError)) {
        fetchNotifications();
      }
    }
  }, [activeTab, loadingNotifications, notifications.length, notificationsError]);

  // Format date helper
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      
      // If date is less than 24 hours ago, show relative time
      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
      
      if (diffInHours < 24) {
        return formatDistanceToNow(date, { addSuffix: true, locale: da });
      }
      
      // Otherwise show date in format "10. jan 2023 13:45"
      return format(date, "d. MMM yyyy HH:mm", { locale: da });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // Fetch user purchases
  const fetchUserPurchases = useCallback(async () => {
    if (!user || purchasesFetchingRef.current) return;
    
    purchasesFetchingRef.current = true;
    setIsLoadingPurchases(true);
    
    try {
      const response = await fetch('/api/users/purchases');
      if (response.ok) {
        const data = await response.json();
        setPurchases(data.purchases || []);
      }
    } catch (error) {
      console.error('Error fetching user purchases:', error);
    } finally {
      purchasesFetchingRef.current = false;
      setIsLoadingPurchases(false);
    }
  }, [user]);

  // Load purchases when tab is 'purchases' or on initial render
  useEffect(() => {
    if (user && activeTab === 'purchases') {
      fetchUserPurchases();
    }
  }, [activeTab, user, fetchUserPurchases]);

  // Fetch user favorites
  const fetchFavorites = useCallback(async () => {
    if (!user || favoritesFetchingRef.current) return;
    
    favoritesFetchingRef.current = true;
    setIsLoadingFavorites(true);
    
    try {
      const response = await fetch('/api/favorites');
      
      if (response.ok) {
        const data = await response.json();
        setFavorites(data.favorites || []);
      } else {
        console.error('Failed to fetch favorites');
      }
    } catch (error) {
      console.error('Error fetching favorites:', error);
    } finally {
      setIsLoadingFavorites(false);
      favoritesFetchingRef.current = false;
    }
  }, [user]);

  // Call fetchFavorites when the component mounts or when the activeTab changes to 'favorites'
  useEffect(() => {
    if (activeTab === 'favorites' && user && !favoritesFetchingRef.current) {
      fetchFavorites();
    }
  }, [activeTab, user, fetchFavorites]);

  // Add loading state for profile page (must be above login check)
  if (initialLoading || isLoadingVerification || isLoadingProducts) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90 backdrop-blur-sm">
        <div className="flex flex-col items-center max-w-sm mx-auto p-8 rounded-xl bg-white shadow-lg border border-gray-100">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mb-6" style={{ animationDuration: '0.4s' }}></div>
          <div className="text-blue-700 text-lg font-medium">Indlæser profil...</div>
          <p className="text-gray-500 text-sm mt-2 text-center">Vent venligst mens vi henter din profil</p>
        </div>
      </div>
    );
  }

  // If not logged in, show login prompt
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 pt-28 pb-20">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <FaUser className="text-blue-600 text-3xl" />
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-4">Log ind for at se din profil</h1>
            <p className="text-gray-600 mb-8">Du skal være logget ind for at se dine køb og favoritter.</p>
            <button 
              onClick={async () => {
                try {
                  const { signIn } = await import('next-auth/react');
                  await signIn('discord', { callbackUrl: '/' });
                } catch (error) {
                  console.error("Error during login:", error);
                }
              }}
              className="relative group bg-gradient-to-r from-blue-500 to-blue-700 text-white font-medium rounded-full px-6 py-3 hover:shadow-lg transition-all duration-300 hover:scale-105"
            >
              <span className="flex items-center justify-center">
                <FaDiscord className="mr-2" />
                Log ind med Discord
              </span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render purchases content function
  const renderPurchasesContent = () => {
    if (isLoadingPurchases) {
      return (
        <div className="bg-gradient-to-br from-blue-50 to-white rounded-2xl shadow-lg p-8 flex flex-col items-center justify-center transform transition-all hover:scale-[1.01] duration-300">
          <div className="animate-spin rounded-full h-12 w-12 border-[3px] border-blue-600 border-t-blue-100 mb-4"></div>
          <p className="text-gray-600">Indlæser dine køb...</p>
        </div>
      );
    }

    if (purchases.length === 0) {
      return (
        <div className="bg-gradient-to-br from-blue-50 to-white rounded-2xl shadow-lg p-8 text-center transform transition-all hover:scale-[1.01] duration-300">
          <div className="w-20 h-20 bg-blue-100/50 rounded-2xl flex items-center justify-center mx-auto mb-6 backdrop-blur-sm">
            <FaShoppingBag className="text-blue-600 text-2xl" />
          </div>
          <h2 className="text-2xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
            Ingen køb endnu
          </h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Du har ikke købt nogen produkter endnu. Udforsk vores udvalg og find noget der passer til dine behov.
          </p>
          <Link 
            href="/products" 
            className="inline-flex items-center px-6 py-3 rounded-xl font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all transform hover:-translate-y-0.5 shadow-lg hover:shadow-xl"
          >
            <span>Udforsk produkter</span>
            <FaExternalLinkAlt className="ml-3 h-5 w-5" />
          </Link>
        </div>
      );
    }

    // Calculate total spent on purchases
    const totalSpent = purchases.reduce((sum, purchase) => sum + (purchase.amount || 0), 0);

    // Check if there are any filtered results when searching
    const hasFilteredResults = Object.keys(filteredGroupedPurchases).length > 0;

    return (
      <div className="space-y-8">
        {/* Purchase Summary Section */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 px-6 py-5">
            <h2 className="text-xl font-bold text-white flex items-center">
              <FaShoppingBag className="mr-3" /> 
          Dine Køb
        </h2>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Total purchases card */}
              <div className="bg-gradient-to-br from-blue-50 to-white rounded-xl p-5 border border-blue-100 shadow-sm">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-blue-800 text-sm font-medium">Antal køb</p>
                    <p className="text-3xl font-bold text-gray-800 mt-1">{purchases.length}</p>
                  </div>
                  <div className="bg-blue-100 p-3 rounded-lg">
                    <FaShoppingBag className="text-blue-600 text-xl" />
                  </div>
                </div>
              </div>
              
              {/* Total spent card */}
              <div className="bg-gradient-to-br from-green-50 to-white rounded-xl p-5 border border-green-100 shadow-sm">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-green-800 text-sm font-medium">Total brugt</p>
                    <p className="text-3xl font-bold text-gray-800 mt-1">{totalSpent} DKK</p>
                  </div>
                  <div className="bg-green-100 p-3 rounded-lg">
                    <FaWallet className="text-green-600 text-xl" />
                  </div>
                </div>
              </div>
              
              {/* Latest purchase card */}
              <div className="bg-gradient-to-br from-purple-50 to-white rounded-xl p-5 border border-purple-100 shadow-sm">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-purple-800 text-sm font-medium">Seneste køb</p>
                    <p className="text-xl font-bold text-gray-800 mt-1 truncate max-w-[180px]">
                      {purchases[0]?.productName || 'Ingen køb'}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      {purchases[0]?.purchaseDate ? new Date(purchases[0].purchaseDate).toLocaleDateString('da-DK') : ''}
                    </p>
                  </div>
                  <div className="bg-purple-100 p-3 rounded-lg">
                    <FaClock className="text-purple-600 text-xl" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Filter and Search Section */}
        <div className="bg-white rounded-xl shadow-sm p-4 flex flex-col sm:flex-row justify-between items-center gap-4 border border-gray-100">
          <div className="relative flex-grow max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Søg i dine køb..."
              value={purchaseSearchQuery}
              onChange={(e) => setPurchaseSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full focus:ring-blue-500 focus:border-blue-500"
            />
            {purchaseSearchQuery && (
              <button 
                onClick={() => setPurchaseSearchQuery('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          
          <div className="flex items-center space-x-3 self-end sm:self-auto">
            <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 flex items-center hover:bg-gray-50">
              <svg className="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
              </svg>
              Filter
            </button>
            
            <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 flex items-center hover:bg-gray-50">
              <svg className="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
              </svg>
              Sortér
            </button>
          </div>
        </div>
        
        {/* Search results message */}
        {purchaseSearchQuery && (
          <div className="bg-blue-50 border border-blue-100 rounded-lg px-4 py-3">
            {hasFilteredResults ? (
              <p className="text-blue-700">
                Viser {Object.values(filteredGroupedPurchases).reduce((sum, arr) => sum + arr.length, 0)} resultater for "{purchaseSearchQuery}"
              </p>
            ) : (
              <p className="text-gray-600">Ingen resultater fundet for "{purchaseSearchQuery}"</p>
            )}
          </div>
        )}
        
        {/* Purchases List */}
        <div className="space-y-6">
          {hasFilteredResults ? (
            Object.entries(filteredGroupedPurchases).map(([monthYear, monthPurchases]) => (
              <div key={monthYear} className="space-y-3">
                <h3 className="text-lg font-medium text-gray-700 pl-2 border-l-4 border-blue-500">
                  {monthYear}
                </h3>
                
                <div className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
                  {monthPurchases.map((purchase, index) => (
                    <div key={purchase._id} className="group">
                      <div className={`p-5 hover:bg-blue-50 transition-colors flex flex-col sm:flex-row sm:items-center gap-4 ${
                        index !== monthPurchases.length - 1 ? 'border-b border-gray-100' : ''
                      }`}>
                        {/* Product Image or Icon */}
                        <div className="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                          {purchase.thumbnailUrl ? (
                            <img 
                              src={purchase.thumbnailUrl} 
                              alt={purchase.productName}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="text-gray-400">
                              <FaCode className="text-2xl" />
                            </div>
                          )}
                        </div>
                        
                        {/* Product Details */}
              <div className="flex-grow">
                          <h4 className="font-semibold text-lg text-gray-800 group-hover:text-blue-700 transition-colors">
                            {purchase.productName}
                          </h4>
                          <div className="mt-1 flex flex-wrap items-center gap-x-6 gap-y-1 text-sm text-gray-600">
                            <span className="flex items-center">
                              <FaCalendar className="mr-1.5 text-gray-400" />
                              {new Date(purchase.purchaseDate).toLocaleDateString('da-DK', {
                                day: 'numeric',
                                month: 'short',
                                year: 'numeric'
                              })}
                  </span>
                            
                            {purchase.productType && (
                              <span className="flex items-center">
                                <FaCube className="mr-1.5 text-gray-400" />
                                {getProductTypeLabel(purchase.productType)}
                              </span>
                            )}
                            
                            {purchase.seller && (
                              <span className="flex items-center">
                                <FaUser className="mr-1.5 text-gray-400" />
                                {purchase.seller}
                              </span>
                            )}
                            
                            <span className="flex items-center font-medium text-green-600">
                              <FaMoneyBillWave className="mr-1.5" />
                    {purchase.amount} DKK
                  </span>
                </div>
              </div>
                        
                        {/* Action Buttons */}
                        <div className="flex items-center space-x-2 mt-3 sm:mt-0">
                          <Link 
                            href={`/products/${purchase.productId}`}
                            className="px-3 py-1.5 text-blue-700 bg-blue-50 hover:bg-blue-100 rounded-lg text-sm font-medium flex items-center transition-colors"
                          >
                            <span>Se produkt</span>
                            <FaExternalLinkAlt className="ml-1.5 h-3 w-3" />
              </Link>
                          
                          <button 
                            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
                            title="Download kvittering"
                            onClick={async () => await generateReceiptPdf(purchase)}
                          >
                            <FaFileDownload className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
            </div>
          ))}
        </div>
              </div>
            ))
          ) : !purchaseSearchQuery ? (
            Object.entries(groupedPurchases).map(([monthYear, monthPurchases]) => (
              <div key={monthYear} className="space-y-3">
                <h3 className="text-lg font-medium text-gray-700 pl-2 border-l-4 border-blue-500">
                  {monthYear}
                </h3>
                
                <div className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
                  {monthPurchases.map((purchase, index) => (
                    <div key={purchase._id} className="group">
                      <div className={`p-5 hover:bg-blue-50 transition-colors flex flex-col sm:flex-row sm:items-center gap-4 ${
                        index !== monthPurchases.length - 1 ? 'border-b border-gray-100' : ''
                      }`}>
                        {/* Product Image or Icon */}
                        <div className="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                          {purchase.thumbnailUrl ? (
                            <img 
                              src={purchase.thumbnailUrl} 
                              alt={purchase.productName}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="text-gray-400">
                              <FaCode className="text-2xl" />
                            </div>
                          )}
                        </div>
                        
                        {/* Product Details */}
                        <div className="flex-grow">
                          <h4 className="font-semibold text-lg text-gray-800 group-hover:text-blue-700 transition-colors">
                            {purchase.productName}
                          </h4>
                          <div className="mt-1 flex flex-wrap items-center gap-x-6 gap-y-1 text-sm text-gray-600">
                            <span className="flex items-center">
                              <FaCalendar className="mr-1.5 text-gray-400" />
                              {new Date(purchase.purchaseDate).toLocaleDateString('da-DK', {
                                day: 'numeric',
                                month: 'short',
                                year: 'numeric'
                              })}
                            </span>
                            
                            {purchase.productType && (
                              <span className="flex items-center">
                                <FaCube className="mr-1.5 text-gray-400" />
                                {getProductTypeLabel(purchase.productType)}
                              </span>
                            )}
                            
                            {purchase.seller && (
                              <span className="flex items-center">
                                <FaUser className="mr-1.5 text-gray-400" />
                                {purchase.seller}
                              </span>
                            )}
                            
                            <span className="flex items-center font-medium text-green-600">
                              <FaMoneyBillWave className="mr-1.5" />
                              {purchase.amount} DKK
                            </span>
                          </div>
                        </div>
                        
                        {/* Action Buttons */}
                        <div className="flex items-center space-x-2 mt-3 sm:mt-0">
                          <Link 
                            href={`/products/${purchase.productId}`}
                            className="px-3 py-1.5 text-blue-700 bg-blue-50 hover:bg-blue-100 rounded-lg text-sm font-medium flex items-center transition-colors"
                          >
                            <span>Se produkt</span>
                            <FaExternalLinkAlt className="ml-1.5 h-3 w-3" />
                          </Link>
                          
                          <button 
                            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
                            title="Download kvittering"
                            onClick={async () => await generateReceiptPdf(purchase)}
                          >
                            <FaFileDownload className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          ) : (
            <div className="bg-white rounded-xl shadow-lg p-8 text-center">
              <FaExclamationCircle className="mx-auto text-gray-400 text-4xl mb-4" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">Ingen resultater fundet</h3>
              <p className="text-gray-500 mb-4">Prøv med andre søgeord eller tjek stavningen.</p>
              <button 
                onClick={() => setPurchaseSearchQuery('')}
                className="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg font-medium hover:bg-blue-100 transition-colors"
              >
                Ryd søgning
              </button>
            </div>
          )}
        </div>
        
        {/* Pagination Controls - Show if there are many purchases */}
        {purchases.length > 10 && !purchaseSearchQuery && (
          <div className="flex justify-center mt-6">
            <nav className="flex items-center space-x-2">
              <button className="px-3 py-1 border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">
                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </button>
              <button className="px-3 py-1 border border-blue-500 bg-blue-50 rounded-md text-blue-600 font-medium">1</button>
              <button className="px-3 py-1 border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">2</button>
              <button className="px-3 py-1 border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">
                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
        )}
      </div>
    );
  };

  // Add to the renderTabContent function
  const renderTabContent = () => {
    switch (activeTab) {
      case 'purchases':
        return renderPurchasesContent();
      case 'favorites':
        return renderFavoritesContent();
      case 'notifications':
        return renderNotificationsContent();
      case 'products':
        return renderProductsContent();
      case 'profile':
        return renderProfileContent();
      case 'transactions':
        return renderTransactionsContent();
      default:
        return renderPurchasesContent();
    }
  };

  // Add a new function to render favorites content
  const renderFavoritesContent = () => {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800">Dine Favoritter</h2>
          <button 
            onClick={fetchFavorites} 
            className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
            disabled={isLoadingFavorites}
          >
            {isLoadingFavorites && <FaSpinner className="animate-spin mr-1" />}
          </button>
        </div>

        {isLoadingFavorites ? (
          <div className="flex justify-center items-center h-40">
            <div className="flex flex-col items-center">
              <FaSpinner className="animate-spin text-blue-500 text-2xl mb-2" />
              <p className="text-gray-500">Indlæser favoritter...</p>
            </div>
          </div>
        ) : favorites.length === 0 ? (
          // Favorites Tab - Modern card design with red theme
          <div className="bg-gradient-to-br from-red-50 to-white rounded-2xl shadow-lg p-8 text-center transform transition-all hover:scale-[1.02] duration-300">
            <div className="w-20 h-20 bg-red-100/50 rounded-2xl flex items-center justify-center mx-auto mb-6 backdrop-blur-sm">
              <FaHeart className="text-red-500 text-2xl" />
            </div>
            <h2 className="text-2xl font-bold mb-3 bg-gradient-to-r from-red-500 to-red-700 bg-clip-text text-transparent">
              Ingen favoritter endnu
            </h2>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Du har ikke tilføjet nogen produkter til dine favoritter endnu. Gem de produkter du er interesseret i for senere.
            </p>
            <Link 
              href="/products" 
              className="inline-flex items-center px-6 py-3 rounded-xl font-semibold text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 transition-all transform hover:-translate-y-0.5 shadow-lg hover:shadow-xl"
            >
              <span>Udforsk produkter</span>
              <FaExternalLinkAlt className="ml-3 h-5 w-5" />
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {favorites.map((favorite, index) => (
              <Link 
                href={`/products/${favorite.productId}`}
                key={index} 
                className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow group"
              >
                <div className="relative h-36 bg-gray-100">
                  {favorite.thumbnailUrl ? (
                    <img 
                      src={favorite.thumbnailUrl} 
                      alt={favorite.productName}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <FaShoppingBag className="text-gray-300 text-4xl" />
                    </div>
                  )}
                  <div className="absolute top-0 right-0 m-2 px-2 py-1 text-xs font-medium bg-black bg-opacity-70 text-white rounded">
                    {getProductTypeLabel(favorite.productType)}
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                    {favorite.productName}
                  </h3>
                  <div className="mt-2 text-sm text-gray-500">
                    Tilføjet: {formatDate(favorite.addedAt)}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Notification Card Component
  const NotificationCard = ({ notification, onMarkAsRead }: {
    notification: {
      _id: string;
      title: string;
      message: string;
      date: string;
      read?: boolean;
      readAt?: string;
      link?: string;
      type?: string;
    },
    onMarkAsRead: (id: string) => void
  }) => {
    const handleClick = () => {
      if (!notification.read) {
        onMarkAsRead(notification._id);
      }

      // If there's a link, navigate to it
      if (notification.link) {
        window.open(notification.link, '_blank');
      }
    };

    const getNotificationIcon = (type?: string) => {
      switch (type) {
        case 'purchase':
          return <FaShoppingBag className="text-green-500" />;
        case 'product':
          return <FaCube className="text-blue-500" />;
        case 'system':
          return <FaExclamationCircle className="text-orange-500" />;
        case 'badge':
          return <FaShieldAlt className="text-purple-500" />;
        default:
          return <FaBell className="text-blue-500" />;
      }
    };

    return (
      <div
        className={`relative p-4 rounded-xl border transition-all duration-200 cursor-pointer hover:shadow-md ${
          notification.read
            ? 'bg-gray-50 border-gray-200 hover:bg-gray-100'
            : 'bg-blue-50 border-blue-200 hover:bg-blue-100'
        }`}
        onClick={handleClick}
      >
        {/* Unread indicator */}
        {!notification.read && (
          <div className="absolute top-4 left-2 w-2 h-2 bg-blue-500 rounded-full"></div>
        )}

        <div className="flex items-start space-x-3 ml-2">
          {/* Icon */}
          <div className="flex-shrink-0 mt-1">
            {getNotificationIcon(notification.type)}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className={`text-sm font-medium ${
                  notification.read ? 'text-gray-800' : 'text-blue-900'
                }`}>
                  {notification.title}
                </h4>
                <p className={`text-sm mt-1 ${
                  notification.read ? 'text-gray-600' : 'text-blue-700'
                }`}>
                  {notification.message}
                </p>

                {/* Link indicator */}
                {notification.link && (
                  <div className="mt-2">
                    <span className="inline-flex items-center text-xs text-blue-600 hover:text-blue-800 font-medium">
                      <FaExternalLinkAlt className="mr-1 h-3 w-3" />
                      Se mere
                    </span>
                  </div>
                )}
              </div>

              {/* Timestamp */}
              <div className="flex-shrink-0 ml-4">
                <span className={`text-xs ${
                  notification.read ? 'text-gray-500' : 'text-blue-600'
                }`}>
                  {formatDistanceToNow(new Date(notification.date), {
                    addSuffix: true,
                    locale: da
                  })}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Mark as read button for unread notifications */}
        {!notification.read && (
          <button
            className="absolute top-2 right-2 p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-200 rounded-full transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onMarkAsRead(notification._id);
            }}
            title="Marker som læst"
          >
            <FaCheck className="h-3 w-3" />
          </button>
        )}
      </div>
    );
  };

  // Add missing render functions to fix the linter errors
  const renderNotificationsContent = () => {
    // Calculate notification statistics
    const unreadCount = notifications.filter(n => !n.read).length;
    const totalCount = notifications.length;

    return (
      <div className="space-y-6">
        {/* Notification Summary Section */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 px-6 py-5">
            <h2 className="text-xl font-bold text-white flex items-center">
              <FaBell className="mr-3" />
              Notifikationer
            </h2>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Total notifications card */}
              <div className="bg-gradient-to-br from-blue-50 to-white rounded-xl p-5 border border-blue-100 shadow-sm">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-blue-800 text-sm font-medium">Alle notifikationer</p>
                    <p className="text-3xl font-bold text-gray-800 mt-1">{totalCount}</p>
                  </div>
                  <div className="bg-blue-100 p-3 rounded-lg">
                    <FaBell className="text-blue-600 text-xl" />
                  </div>
                </div>
              </div>

              {/* Unread notifications card */}
              <div className="bg-gradient-to-br from-orange-50 to-white rounded-xl p-5 border border-orange-100 shadow-sm">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-orange-800 text-sm font-medium">Ulæste</p>
                    <p className="text-3xl font-bold text-gray-800 mt-1">{unreadCount}</p>
                  </div>
                  <div className="bg-orange-100 p-3 rounded-lg">
                    <FaExclamationCircle className="text-orange-600 text-xl" />
                  </div>
                </div>
              </div>

              {/* Latest notification card */}
              <div className="bg-gradient-to-br from-green-50 to-white rounded-xl p-5 border border-green-100 shadow-sm">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-green-800 text-sm font-medium">Seneste</p>
                    <p className="text-lg font-bold text-gray-800 mt-1 truncate max-w-[180px]">
                      {notifications[0]?.title || 'Ingen notifikationer'}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      {notifications[0]?.date ? formatDate(notifications[0].date) : ''}
                    </p>
                  </div>
                  <div className="bg-green-100 p-3 rounded-lg">
                    <FaClock className="text-green-600 text-xl" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Notifications List Section */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
          {/* Header with filters and actions */}
          <div className="border-b border-gray-200">
            <div className="px-6 py-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">
                  Dine Notifikationer
                </h3>
                <p className="text-sm text-gray-500 mt-1">
                  Hold dig opdateret med de seneste nyheder og påmindelser
                </p>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2 flex-wrap">
                <div className="relative inline-block">
                  <button
                    className="text-sm bg-gray-100 hover:bg-gray-200 px-3 py-1.5 rounded-lg text-gray-700 font-medium transition-colors flex items-center"
                    onClick={() => setActiveFilter(prev => prev === 'all' ? 'unread' : 'all')}
                  >
                    <span>{activeFilter === 'all' ? 'Vis alle' : 'Vis ulæste'}</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>

                {notifications.some(n => !n.read) && (
                  <button
                    className={`text-sm px-3 py-1.5 rounded-lg font-medium transition-colors flex items-center
                      ${isMarkingAllAsRead
                        ? 'bg-gray-100 text-gray-400'
                        : 'bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700'
                      }`}
                    onClick={markAllAsRead}
                    disabled={isMarkingAllAsRead}
                  >
                    {isMarkingAllAsRead ? (
                      <>
                        <div className="animate-spin h-3 w-3 border-2 border-blue-600 border-t-transparent rounded-full mr-2"></div>
                        <span>Markerer...</span>
                      </>
                    ) : (
                      <>
                        <FaCheck className="mr-1.5 h-3 w-3" />
                        <span className="hidden sm:inline">Marker alle som læst</span>
                        <span className="sm:hidden">Marker alle</span>
                      </>
                    )}
                  </button>
                )}

                <button
                  className="text-sm px-3 py-1.5 rounded-lg font-medium transition-colors flex items-center bg-gray-100 hover:bg-gray-200 text-gray-700"
                  onClick={() => fetchNotifications()}
                  disabled={loadingNotifications}
                  title="Opdater notifikationer"
                >
                  {loadingNotifications ? (
                    <div className="animate-spin h-3 w-3 border-2 border-gray-600 border-t-transparent rounded-full"></div>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  )}
                </button>

                {notifications.length > 0 && (
                  <button
                    className={`text-sm px-3 py-1.5 rounded-lg font-medium transition-colors flex items-center
                      ${isClearingNotifications
                        ? 'bg-gray-100 text-gray-400'
                        : 'bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700'
                      }`}
                    onClick={clearAllNotifications}
                    disabled={isClearingNotifications}
                  >
                    {isClearingNotifications ? (
                      <>
                        <div className="animate-spin h-3 w-3 border-2 border-red-600 border-t-transparent rounded-full mr-2"></div>
                        <span>Sletter...</span>
                      </>
                    ) : (
                      <>
                        <FaExclamationTriangle className="mr-1.5 h-3 w-3" />
                        <span className="hidden sm:inline">Slet alle</span>
                        <span className="sm:hidden">Slet</span>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Content area with notifications */}
          <div className="p-6">
            {loadingNotifications ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="w-16 h-16 relative mb-4">
                  <div className="absolute top-0 left-0 w-full h-full border-4 border-blue-200 rounded-full animate-ping opacity-75"></div>
                  <div className="relative w-full h-full flex items-center justify-center">
                    <FaBell className="text-blue-500 text-3xl animate-pulse" />
                  </div>
                </div>
                <p className="text-gray-500 text-lg">Indlæser dine notifikationer</p>
                <p className="text-gray-400 text-sm mt-1">Vent venligst et øjeblik...</p>
              </div>
            ) : notificationsError ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                  <FaExclamationTriangle className="text-red-500 text-2xl" />
                </div>
                <p className="text-red-600 text-lg font-medium">Fejl ved indlæsning</p>
                <p className="text-gray-500 text-sm mt-1 text-center max-w-md">
                  {notificationsError}
                </p>
                <button
                  onClick={() => fetchNotifications()}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Prøv igen
                </button>
              </div>
            ) : notifications.length === 0 ? (
              <div className="bg-gradient-to-br from-blue-50 to-white rounded-2xl shadow-lg p-8 text-center transform transition-all hover:scale-[1.01] duration-300">
                <div className="w-20 h-20 bg-blue-100/50 rounded-2xl flex items-center justify-center mx-auto mb-6 backdrop-blur-sm">
                  <FaBell className="text-blue-600 text-2xl" />
                </div>
                <h2 className="text-2xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                  Ingen notifikationer endnu
                </h2>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Du har ikke modtaget nogen notifikationer endnu. Vi sender dig besked når der er nyt at se, såsom nye produkter, opdateringer eller vigtige meddelelser.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <button
                    onClick={() => fetchNotifications()}
                    className="inline-flex items-center px-4 py-2 rounded-lg font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors"
                    disabled={loadingNotifications}
                  >
                    {loadingNotifications ? (
                      <>
                        <div className="animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full mr-2"></div>
                        <span>Opdaterer...</span>
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        <span>Opdater</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Render grouped notifications */}
                {groupedNotifications && (
                  <>
                    {/* Today's notifications */}
                    {groupedNotifications.today && groupedNotifications.today.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                          <FaClock className="mr-2 text-blue-500" />
                          I dag
                        </h4>
                        <div className="space-y-3">
                          {groupedNotifications.today.map((notification) => (
                            <NotificationCard
                              key={notification._id}
                              notification={notification}
                              onMarkAsRead={markAsRead}
                            />
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Yesterday's notifications */}
                    {groupedNotifications.yesterday && groupedNotifications.yesterday.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                          <FaClock className="mr-2 text-gray-500" />
                          I går
                        </h4>
                        <div className="space-y-3">
                          {groupedNotifications.yesterday.map((notification) => (
                            <NotificationCard
                              key={notification._id}
                              notification={notification}
                              onMarkAsRead={markAsRead}
                            />
                          ))}
                        </div>
                      </div>
                    )}

                    {/* This week's notifications */}
                    {groupedNotifications.thisWeek && groupedNotifications.thisWeek.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                          <FaCalendar className="mr-2 text-gray-500" />
                          Denne uge
                        </h4>
                        <div className="space-y-3">
                          {groupedNotifications.thisWeek.map((notification) => (
                            <NotificationCard
                              key={notification._id}
                              notification={notification}
                              onMarkAsRead={markAsRead}
                            />
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Older notifications */}
                    {groupedNotifications.older && groupedNotifications.older.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                          <FaCalendar className="mr-2 text-gray-400" />
                          Ældre
                        </h4>
                        <div className="space-y-3">
                          {groupedNotifications.older.map((notification) => (
                            <NotificationCard
                              key={notification._id}
                              notification={notification}
                              onMarkAsRead={markAsRead}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderProductsContent = () => {
    // Keeping the original products tab content
    return (
      <div>
        {isLoadingProducts && products.length === 0 ? (
          <div className="bg-gradient-to-br from-blue-50 to-white rounded-2xl shadow-lg p-8 flex flex-col items-center justify-center transform transition-all hover:scale-[1.02] duration-300">
            <div className="animate-spin rounded-full h-12 w-12 border-[3px] border-blue-600 border-t-blue-100 mb-4"></div>
            <p className="text-gray-600">Indlæser dine produkter...</p>
          </div>
        ) : products.length > 0 ? (
          <div className="bg-gradient-to-br from-white to-blue-50 rounded-2xl shadow-lg p-8">
            <h2 className="text-xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              Dine Publicerede Produkter
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {products.map((product) => (
                <Link 
                  key={product._id} 
                  href={`/products/${product._id}`}
                  className="group overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                >
                  <div className="relative bg-gray-50 h-48">
                    {product.screenshotUrls && product.screenshotUrls.length > 0 ? (
                      <div className="rounded-2xl overflow-hidden h-full bg-gray-50">
                        <img
                          src={product.screenshotUrls[0].url || getImageUrl(product.screenshotUrls[0].fileId)}
                          alt={product.projectName}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-blue-50 text-blue-600">
                        <FaCode className="text-3xl" />
                      </div>
                    )}
                    {product.price === 0 && (
                      <div className="absolute top-3 left-3 z-10">
                        <img 
                          src="/images/Gratis.png" 
                          alt="Gratis" 
                          className="h-10 w-auto"
                          loading="lazy"
                        />
                      </div>
                    )}
                    <div className="absolute bottom-3 right-3">
                      <div className={`text-white text-sm font-semibold px-3 py-1.5 rounded-full ${getProductTypeColor(product.productType)} shadow-md`}>
                        {getProductTypeLabel(product.productType)}
                      </div>
                    </div>
                  </div>
                  <div className="p-5 bg-white">
                    <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {product.projectName}
                    </h3>
                  </div>
                </Link>
              ))}
            </div>
            
            <div className="mt-8 text-center">
              <Link 
                href="/admin/products/my-products" 
                className="inline-flex items-center px-6 py-3 rounded-xl font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all transform hover:-translate-y-0.5 shadow-lg hover:shadow-xl"
              >
                <span>Administrer Alle Dine Produkter</span>
                <FaExternalLinkAlt className="ml-3 h-5 w-5" />
              </Link>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow p-8 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaCode className="text-blue-600 text-xl" />
            </div>
            <h2 className="text-xl font-semibold mb-2">Ingen produkter endnu</h2>
            <p className="text-gray-600 mb-6">Du har ikke oprettet nogen produkter endnu.</p>
            <Link 
              href="/admin/products/upload" 
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <span>Upload Et Produkt</span>
              <FaExternalLinkAlt className="ml-2 h-4 w-4" />
            </Link>
          </div>
        )}
      </div>
    );
  };

  const renderProfileContent = () => {
    // Only show profile content for admin users
    if (!isAdmin) {
      return null; // Return null for non-admin users
    }
    
    return (
      // Profile Tab - For admin users only - REDESIGNED
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all hover:scale-[1.01] duration-300">
        {/* Header with profile stats */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-8 py-6 text-white">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h2 className="text-2xl font-bold flex items-center">
                Udvikler Profil
                {adminUserData?.admintype && (
                  <span className="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-200 text-blue-800">
                    {adminUserData.admintype}
                  </span>
                )}
              </h2>
              <p className="text-blue-100 mt-1">
                Administrer din offentlige udvikler profil og synlighed på MCDevHub
              </p>
            </div>

            <Link 
              href={`/developers/${adminUserData?.username || ''}`} 
              target="_blank"
              className="inline-flex items-center px-4 py-2 bg-white text-blue-700 rounded-lg font-medium shadow-sm hover:bg-blue-50 transition-colors"
            >
              <span>Se offentlig profil</span>
              <FaExternalLinkAlt className="ml-2 h-3 w-3" />
            </Link>
          </div>
        </div>
        
        {/* Content in sections */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left column - Profile basics */}
            <div className="lg:col-span-2 space-y-6">
              {/* About Me Section */}
              <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-5">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">Om Mig</h3>
                    <p className="text-sm text-gray-500">
                      Denne beskrivelse vises på din offentlige udvikler profil
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {autosaveIndicator && (
                      <div className="text-blue-600 text-xs px-2 py-1 rounded-full flex items-center cursor-default">
                        <div className="animate-spin mr-1 h-3 w-3 border-b-2 border-blue-600 rounded-full"></div>
                        Gemmer...
                      </div>
                    )}
                    {saveConfirmation && (
                      <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full flex items-center cursor-default">
                        <FaCheck className="mr-1" />
                        Gemt!
                      </div>
                    )}
                  </div>
                </div>

                {/* Text editor with undo/redo controls */}
                <div className="relative mb-2">
                  {!isDescriptionLoaded && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10 rounded-lg">
                      <div className="animate-pulse flex space-x-2">
                        <div className="h-2 w-2 rounded-full bg-blue-400"></div>
                        <div className="h-2 w-2 rounded-full bg-blue-400"></div>
                        <div className="h-2 w-2 rounded-full bg-blue-400"></div>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2 mb-2">
                    <button 
                      type="button"
                      onClick={undoChanges}
                      disabled={currentHistoryIndex <= 0}
                      className={`p-1.5 rounded ${currentHistoryIndex > 0 ? 'text-gray-700 hover:bg-gray-100' : 'text-gray-300 cursor-default'}`}
                      title="Fortryd"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                    
                    <button 
                      type="button"
                      onClick={redoChanges}
                      disabled={currentHistoryIndex >= descriptionHistory.length - 1}
                      className={`p-1.5 rounded ${currentHistoryIndex < descriptionHistory.length - 1 ? 'text-gray-700 hover:bg-gray-100' : 'text-gray-300 cursor-default'}`}
                      title="Gentag"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                    
                    <div className="text-xs text-gray-500 ml-2">
                      {description.length} / 2000 tegn
                    </div>
                  </div>
                  
                  <textarea
                    id="description"
                    rows={6}
                    value={description}
                    onChange={(e) => {
                      setDescription(e.target.value);
                      // Add to history when typing
                      addToHistory(e.target.value);
                    }}
                    className={`block w-full rounded-lg shadow-sm py-3 px-4 focus:ring-blue-500 focus:border-blue-500 sm:text-sm resize-y border 
                      ${adminUserData && !adminUserData.description ? 'border-dashed border-gray-300 bg-gray-50' : 'border-gray-300'}
                      ${adminUserData?.description ? 'bg-white' : 'bg-gray-50'}`}
                    placeholder={!isDescriptionLoaded ? "Indlæser..." : "Fortæl om dig selv, dine kompetencer og hvad du kan tilbyde..."}
                    maxLength={2000}
                    disabled={!isDescriptionLoaded || loadingDescription}
                  ></textarea>
                </div>
                
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-3 text-sm text-yellow-800 rounded">
                  <p className="font-medium">Tips til en god beskrivelse:</p>
                  <ul className="list-disc ml-5 mt-1 space-y-1">
                    <li>Beskriv dine primære kompetencer og erfaring</li>
                    <li>Fremhæv dine bedste projekter eller specialer</li>
                    <li>Fortæl om dine priser eller hvad kunder kan forvente</li>
                    <li>Inkluder kontaktoplysninger hvis relevant</li>
                  </ul>
                </div>
              </div>
              
              {/* Skills & Specialties Section */}
              <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-5">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Færdigheder & Specialer</h3>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Primære Færdigheder</label>
                    <div className="flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        Java
                        <button type="button" className="ml-1 text-blue-600 hover:text-blue-800">
                          ×
                        </button>
                      </span>
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        Skript
                        <button type="button" className="ml-1 text-green-600 hover:text-green-800">
                          ×
                        </button>
                      </span>
                      <button type="button" className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200">
                        + Tilføj færdighed
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Specialer</label>
                    <div className="flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                        Prison servere
                        <button type="button" className="ml-1 text-purple-600 hover:text-purple-800">
                          ×
                        </button>
                      </span>
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-amber-100 text-amber-800">
                        Minigames
                        <button type="button" className="ml-1 text-amber-600 hover:text-amber-800">
                          ×
                        </button>
                      </span>
                      <button type="button" className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200">
                        + Tilføj speciale
                      </button>
                    </div>
                  </div>
                </div>
                
                <div className="text-gray-500 text-sm">
                  <p>Færdigheder og specialer er under udvikling og vil snart være tilgængelige.</p>
                </div>
              </div>
            </div>
            
            {/* Right column - Status and social links */}
            <div className="space-y-6">
              {/* Profile Status Card */}
              <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-5 py-4 border-b border-gray-200">
                  <h3 className="font-semibold text-gray-800">Profil Status</h3>
                </div>
                <div className="p-5 space-y-4">
                  {/* Verification Status */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${verificationStatus?.isVerified ? 'bg-green-100' : 'bg-gray-100'}`}>
                        {verificationStatus?.isVerified ? (
                          <FaCheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <FaClock className="h-5 w-5 text-gray-500" />
                        )}
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-gray-900">Verificering</h4>
                        <p className="text-xs text-gray-500">
                          {verificationStatus?.isVerified 
                            ? 'Din profil er verificeret' 
                            : 'Afventer verificering'}
                        </p>
                      </div>
                    </div>
                    {verificationStatus?.isVerified && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Godkendt
                      </span>
                    )}
                  </div>
                  
                  {/* Profile Completion */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                        <FaUserCircle className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-gray-900">Profilfuldførelse</h4>
                        <p className="text-xs text-gray-500">75% komplet</p>
                      </div>
                    </div>
                    <div className="w-16">
                      <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div className="h-full bg-blue-600 rounded-full" style={{ width: '75%' }}></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Trust Score */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center">
                        <FaShieldAlt className="h-5 w-5 text-amber-600" />
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-gray-900">Trust Score</h4>
                        <p className="text-xs text-gray-500">Baseret på aktivitet og historik</p>
                      </div>
                    </div>
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <svg 
                          key={star} 
                          className={`h-4 w-4 ${star <= 4 ? 'text-amber-500' : 'text-gray-300'}`} 
                          fill="currentColor" 
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Social Links Card */}
              <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-5 py-4 border-b border-gray-200">
                  <h3 className="font-semibold text-gray-800">Sociale Links</h3>
                </div>
                <div className="p-5 space-y-4">
                  {/* Success/Error Messages */}
                  {socialLinkSuccess && (
                    <div className="flex items-center bg-green-50 text-green-700 p-2 rounded text-sm mb-3">
                      <FaCheck className="mr-2" />
                      {socialLinkSuccess}
                    </div>
                  )}
                  {socialLinkError && (
                    <div className="flex items-center bg-red-50 text-red-700 p-2 rounded text-sm mb-3">
                      <FaExclamationCircle className="mr-2" />
                      {socialLinkError}
                    </div>
                  )}
                  
                  {/* Discord */}
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#5865F2] flex items-center justify-center">
                      <FaDiscord className="h-4 w-4 text-white" />
                    </div>
                    <div className="ml-3 flex-1">
                      <h4 className="text-sm font-medium text-gray-900 flex items-center">
                        Discord 
                        <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded">
                          Tilsluttet
                        </span>
                      </h4>
                      <p className="text-xs text-gray-500">{user?.name}</p>
                    </div>
                  </div>
                  
                  {/* GitHub */}
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center">
                      <FaGithub className="h-4 w-4 text-white" />
                    </div>
                    <div className="ml-3 flex-1">
                      {isEditingGithub ? (
                        <div className="mt-1">
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                            <input
                              type="text"
                              value={githubInput}
                              onChange={(e) => setGithubInput(e.target.value)}
                              placeholder="GitHub brugernavn"
                              className="w-full border border-gray-300 rounded-md px-3 py-1.5 text-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                            <div className="flex mt-2 sm:mt-0">
                              <button 
                                onClick={() => updateSocialLink('github', githubInput)}
                                className="bg-blue-600 text-white px-4 py-1.5 text-sm font-medium rounded-md hover:bg-blue-700"
                              >
                                Gem
                              </button>
                              <button
                                onClick={() => {
                                  setIsEditingGithub(false);
                                  setGithubInput(adminUserData?.githubUsername || '');
                                }}
                                className="ml-2 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md"
                              >
                                Annuller
                              </button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <>
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-gray-900">GitHub</h4>
                            <button 
                              type="button"
                              className="text-sm text-blue-600 hover:text-blue-800"
                              onClick={() => setIsEditingGithub(true)}
                            >
                              {adminUserData?.githubUsername ? 'Rediger' : 'Tilføj'}
                            </button>
                          </div>
                          {adminUserData?.githubUsername ? (
                            <p className="text-xs text-gray-500 flex items-center">
                              <a 
                                href={`https://github.com/${adminUserData.githubUsername}`} 
                                target="_blank"
                                rel="noopener noreferrer"
                                className="hover:text-blue-600 hover:underline flex items-center"
                              >
                                {adminUserData.githubUsername}
                                <FaExternalLinkAlt className="ml-1 h-2.5 w-2.5" />
                              </a>
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Tilknyt dit GitHub-brugernavn</p>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                  
                  {/* YouTube */}
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-red-600 flex items-center justify-center">
                      <FaYoutube className="h-4 w-4 text-white" />
                    </div>
                    <div className="ml-3 flex-1">
                      {isEditingYoutube ? (
                        <div className="mt-1">
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                            <input
                              type="text"
                              value={youtubeInput}
                              onChange={(e) => setYoutubeInput(e.target.value)}
                              placeholder="YouTube URL (https://youtube.com/c/...)"
                              className="w-full border border-gray-300 rounded-md px-3 py-1.5 text-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                            <div className="flex mt-2 sm:mt-0">
                              <button 
                                onClick={() => updateSocialLink('youtube', youtubeInput)}
                                className="bg-blue-600 text-white px-4 py-1.5 text-sm font-medium rounded-md hover:bg-blue-700"
                              >
                                Gem
                              </button>
                              <button
                                onClick={() => {
                                  setIsEditingYoutube(false);
                                  setYoutubeInput(adminUserData?.youtubeUrl || '');
                                }}
                                className="ml-2 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md"
                              >
                                Annuller
                              </button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <>
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-gray-900">YouTube</h4>
                            <button 
                              type="button"
                              className="text-sm text-blue-600 hover:text-blue-800"
                              onClick={() => setIsEditingYoutube(true)}
                            >
                              {adminUserData?.youtubeUrl ? 'Rediger' : 'Tilføj'}
                            </button>
                          </div>
                          {adminUserData?.youtubeUrl ? (
                            <p className="text-xs text-gray-500 flex items-center">
                              <a 
                                href={adminUserData.youtubeUrl} 
                                target="_blank"
                                rel="noopener noreferrer"
                                className="hover:text-blue-600 hover:underline flex items-center"
                              >
                                {adminUserData.youtubeUrl.replace(/https?:\/\/(www\.)?youtube\.com\/(channel|user|c)\//, '')}
                                <FaExternalLinkAlt className="ml-1 h-2.5 w-2.5" />
                              </a>
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Link til din YouTube-kanal</p>
                          )}
                        </>
                      )}
                    </div>
                  </div>

                  {/* Email */}
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                      <FaEnvelope className="h-4 w-4 text-white" />
                    </div>
                    <div className="ml-3 flex-1">
                      {isEditingEmail ? (
                        <div className="mt-1">
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                            <input
                              type="email"
                              value={emailInput}
                              onChange={(e) => setEmailInput(e.target.value)}
                              placeholder="Din email adresse"
                              className="w-full border border-gray-300 rounded-md px-3 py-1.5 text-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                            <div className="flex mt-2 sm:mt-0">
                              <button 
                                onClick={() => updateSocialLink('email', emailInput)}
                                className="bg-blue-600 text-white px-4 py-1.5 text-sm font-medium rounded-md hover:bg-blue-700"
                              >
                                Gem
                              </button>
                              <button
                                onClick={() => {
                                  setIsEditingEmail(false);
                                  setEmailInput(adminUserData?.email || user?.email || '');
                                }}
                                className="ml-2 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md"
                              >
                                Annuller
                              </button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <>
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-gray-900">Email</h4>
                            <button 
                              type="button" 
                              className="text-sm text-blue-600 hover:text-blue-800"
                              onClick={() => setIsEditingEmail(true)}
                            >
                              Rediger
                            </button>
                          </div>
                          <p className="text-xs text-gray-500">
                            {adminUserData?.email || user?.email || 'Ingen email tilknyttet'}
                          </p>
                          <p className="text-xs text-gray-500 mt-1 flex items-center">
                            <FaCheck className="text-green-500 mr-1 h-3 w-3" />
                            Din email vises offentligt på din profil
                          </p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Availability Status */}
              <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-5 py-4 border-b border-gray-200">
                  <h3 className="font-semibold text-gray-800">Tilgængelighed</h3>
                </div>
                <div className="p-5">
                  <label
                    className={`flex items-center justify-between ${loadingAvailability ? 'cursor-default opacity-50' : 'cursor-pointer'}`}
                    onClick={() => {
                      if (!loadingAvailability) {
                        updateAvailabilityStatus(!openForTasks);
                      }
                    }}
                  >
                    <span className="text-sm font-medium text-gray-900">Åben for nye opgaver</span>
                    <div className="relative">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={openForTasks}
                        onChange={() => {}} // Controlled by onClick above
                        disabled={loadingAvailability}
                      />
                      <div className={`w-12 h-6 ${openForTasks ? 'bg-blue-600' : 'bg-gray-200'} rounded-full transition-colors duration-200 border border-gray-300 ${loadingAvailability ? 'opacity-50' : ''}`}></div>
                      <div className={`absolute left-1 top-1 w-4 h-4 bg-white rounded-full shadow-sm transition-transform duration-200 border border-gray-300 ${openForTasks ? 'translate-x-6' : 'translate-x-0'} ${loadingAvailability ? 'opacity-50' : ''}`}></div>
                      {loadingAvailability && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                        </div>
                      )}
                    </div>
                  </label>
                  <p className="mt-2 text-xs text-gray-500">Når aktiveret, vil din profil vise at du er åben for nye projekter og opgaver.</p>
                  {availabilityError && (
                    <p className="mt-2 text-xs text-red-600">{availabilityError}</p>
                  )}
                </div>
              </div>

            </div>
          </div>

          {/* Badges Section - Full Width */}
          <div className="mt-8 bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-6 border-b border-gray-200">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <h3 className="text-xl font-bold text-gray-800 flex items-center">
                    <FaShieldAlt className="mr-3 text-blue-600" />
                    Mine Badges
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Dine optjente badges og fremskridt på platformen
                  </p>
                </div>
                {isCheckingBadges && (
                  <div className="flex items-center text-blue-600 bg-blue-100 px-4 py-2 rounded-lg">
                    <FaSpinner className="animate-spin mr-2" />
                    <span className="text-sm font-medium">Tjekker badges...</span>
                  </div>
                )}
              </div>
            </div>
            <div className="p-8">
              <BadgeDisplay
                showProgress={true}
                showUnearned={true}
                className=""
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderTransactionsContent = () => {
    return (
      <div className="space-y-8">
        {/* Page Header Section */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
            <h2 className="text-xl font-medium text-white flex items-center">
              <FaExchangeAlt className="mr-3" /> 
              Transaktioner
            </h2>
          </div>
          
          <div className="p-6">
            <p className="text-gray-600">
              Her kan du se alle dine transaktioner og administrere din udviklerkonto.
            </p>
          </div>
        </div>
        
        {/* Two Column Layout for desktop */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Left Column - Developer Account Card */}
          <div className="md:col-span-1">
            <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 h-full">
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
                <h3 className="text-lg font-medium text-white flex items-center">
                  <FaWallet className="mr-2" /> 
                  Udviklerkonto
                </h3>
              </div>
              
              <div className="p-4 flex flex-col items-center">
                {/* Credit Card */}
                <div className="w-full max-w-md mx-auto mb-6">
                  <div className="relative w-full h-52 bg-gradient-to-r from-blue-600 to-indigo-800 rounded-xl shadow-xl overflow-hidden transform transition-all duration-300 ease-in-out hover:scale-105 select-none">
                    {/* Simplified credit card chip */}
                    <div className="absolute top-8 left-8 w-12 h-9 rounded-md overflow-hidden pointer-events-none">
                      <div className="w-full h-full bg-gradient-to-br from-yellow-300 to-yellow-500 p-0.5 rounded-md">
                        <div className="w-full h-full bg-yellow-400 rounded-sm border border-yellow-300 flex flex-col justify-between p-0.5">
                          <div className="grid grid-cols-2 gap-0.5 h-1/2">
                            <div className="bg-yellow-300 opacity-40"></div>
                            <div className="bg-yellow-300 opacity-40"></div>
                          </div>
                          <div className="grid grid-cols-3 gap-0.5 h-1/2">
                            <div className="bg-yellow-300 opacity-40"></div>
                            <div className="bg-yellow-300 opacity-40"></div>
                            <div className="bg-yellow-300 opacity-40"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Company Logo */}
                    <div className="absolute top-8 right-8 w-16 h-16 flex items-center justify-center pointer-events-none">
                      <img 
                        src="/images/McDevHub.png" 
                        alt="MCDevHub" 
                        className="w-full h-full object-contain select-none"
                        draggable="false"
                      />
                    </div>
                    
                    {/* Card Holder & Balance */}
                    <div className="absolute bottom-14 left-8 right-8 flex justify-between items-end pointer-events-none">
                      <div className="flex flex-col">
                        <span className="text-white opacity-70 text-xs uppercase mb-1">Udviklerkonto</span>
                        <span className="text-white font-semibold tracking-wider">MCDEVHUB</span>
                      </div>
                      <div className="flex flex-col items-end">
                        <span className="text-white opacity-70 text-xs uppercase mb-1">Balance</span>
                        <BalanceDisplay className="border-0 shadow-none p-0 bg-transparent text-white" />
                      </div>
                    </div>
                    
                    {/* Card Actions */}
                    <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-3 pointer-events-auto">
                      <button 
                        onClick={() => setIsDepositModalOpen(true)}
                        className="inline-flex items-center justify-center px-5 py-2 bg-green-500/80 hover:bg-green-600/80 text-white text-sm font-medium rounded-md border border-white/30 transition-all shadow-md hover:shadow-lg"
                      >
                        <FaPlus className="mr-2 text-xs" />
                        Indsæt penge
                      </button>
                      
                      <Link 
                        href="/profile/newcashoutpage" 
                        className="inline-flex items-center justify-center px-5 py-2 bg-white/10 hover:bg-white/20 text-white text-sm font-medium rounded-md border border-white/30 transition-all shadow-md hover:shadow-lg"
                      >
                        <FaExchangeAlt className="mr-2 text-xs" />
                        Udbetal
                      </Link>
                    </div>
                    
                    {/* Decorative Elements */}
                    <svg className="absolute bottom-0 left-0 w-full h-24 text-white opacity-10 pointer-events-none" viewBox="0 0 100 100" preserveAspectRatio="none">
                      <path d="M0,70 Q50,30 100,70 L100,100 L0,100 Z" fill="currentColor" />
                      <path d="M0,90 Q50,50 100,90 L100,100 L0,100 Z" fill="currentColor" />
                    </svg>
                    
                    {/* Additional subtle background pattern */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white to-blue-500 opacity-5 pointer-events-none"></div>
                  </div>
                </div>
                
                {/* Additional Account Info */}
                <div className="w-full bg-gray-50 rounded-lg p-4 mb-4 border border-gray-100">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Kontoinformation</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="text-gray-500">Konto ID:</div>
                    <div className="text-gray-800 font-medium">
                      {loadingUserId ? (
                        <div className="inline-block w-16 h-4 bg-gray-200 animate-pulse rounded"></div>
                      ) : mongoDbUserId ? (
                        mongoDbUserId
                      ) : (
                        "Ikke tilgængelig"
                      )}
                    </div>
                    <div className="text-gray-500">Status:</div>
                    <div className="text-green-600 font-medium">Aktiv</div>
                    <div className="text-gray-500">Oprettet:</div>
                    <div className="text-gray-800 font-medium">
                      {loadingUserId ? (
                        <div className="inline-block w-20 h-4 bg-gray-200 animate-pulse rounded"></div>
                      ) : userCreatedAt ? (
                        userCreatedAt
                      ) : (
                        "Ikke tilgængelig"
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Right Column - Transactions Table */}
          <div className="md:col-span-2">
            <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 h-full">
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-white flex items-center">
                    <FaExchangeAlt className="mr-2" /> 
                    Transaktionsoversigt
                  </h3>
                  <div className="text-white text-sm bg-white/20 px-3 py-1 rounded-full">
                    Seneste 30 dage
                  </div>
                </div>
              </div>
              
              {/* Transaction Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
                <div className="bg-gray-50 rounded-lg p-3 border border-gray-100">
                  <div className="text-xs text-gray-500 mb-1">Total indtægt</div>
                  <div className="text-lg font-bold text-gray-800">{totalIncome.toLocaleString('da-DK')} DKK</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 border border-gray-100">
                  <div className="text-xs text-gray-500 mb-1">Antal transaktioner</div>
                  <div className="text-lg font-bold text-gray-800">{transactionCount}</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 border border-gray-100">
                  <div className="text-xs text-gray-500 mb-1">Gebyr</div>
                  <div className="text-lg font-bold text-gray-800">{feePercentage}%</div>
                </div>
              </div>
              
              {/* Transactions Table */}
              <div className="px-4 pb-4">
                <TransactionsTable />
              </div>
            </div>
          </div>
        </div>
        
        {/* Deposit Modal */}
        <DepositModal 
          isOpen={isDepositModalOpen}
          onClose={() => setIsDepositModalOpen(false)}
        />
      </div>
    );
  };

  // ... existing code ...

  return (
    <div className="min-h-screen bg-gray-50 pt-28 pb-20">
      {/* Add Suspense boundary for TabParamHandler */}
      <Suspense fallback={null}>
        <TabParamHandler setActiveTab={setActiveTab} isAdmin={isAdmin} />
      </Suspense>
      
      <div className="container mx-auto px-4">
        {/* Verification Banner - Only shown for verified users */}
        {verificationStatus?.isVerified && (
          <div className="max-w-6xl mx-auto mb-6 bg-white border-l-4 border-amber-500 rounded-lg shadow-lg overflow-hidden animate-slide-down">
            <div className="px-8 py-6">
              <div className="flex flex-col md:flex-row md:items-center gap-4">
                <div className="flex-shrink-0 bg-gradient-to-br from-amber-400 to-amber-600 rounded-xl p-3 shadow-lg text-white">
                  <svg className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="flex flex-wrap items-center gap-2 mb-2">
                    <h3 className="text-xl font-bold text-amber-800">
                      Systemopgradering
                    </h3>
                    <span className="bg-amber-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">Nyt</span>
                  </div>
                  <div className="prose text-amber-800">
                    <p>
                      Vi har opgraderet vores database til en langt hurtigere og større løsning, hvilket betyder at 
                      <span className="font-medium bg-amber-200 px-1 mx-1 rounded">alle produkters coverbilleder</span> 
                      nu kan opdateres korrekt.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Profile Header */}
        <div className="max-w-6xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden mb-8 border border-gray-100">
          {/* Banner: customizable color, text, and upload */}
          <div
            ref={bannerRef}
            className="h-32 relative flex items-end cursor-pointer group"
            style={{ background: bannerColor }}
            onClick={handleBannerClick}
          >
            {/* Banner color picker button */}
            <button
              className="absolute top-3 right-3 z-20 px-3 py-1 bg-blue-800 bg-opacity-95 rounded shadow text-xs font-bold text-white hover:bg-opacity-100 transition border border-blue-900"
              onClick={handleBannerColorPicker}
              type="button"
            >
              Skift Bannerfarve
            </button>
            {showBannerColorPicker && (
              <>
                {/* Overlay to close color picker on outside click */}
                <div
                  className="fixed inset-0 z-30"
                  onClick={() => { setShowBannerColorPicker(false); setMenuOpen(false); }}
                  style={{ background: 'transparent' }}
                />
                <div className="absolute top-10 right-3 z-40" onClick={e => e.stopPropagation()}>
                  <SketchPicker
                    color={bannerColor}
                    onChange={color => setBannerColor(color.hex)}
                    disableAlpha
                  />
                  <button className="mt-2 px-2 py-1 bg-gray-800 text-white rounded text-xs font-semibold" onClick={() => { setShowBannerColorPicker(false); setMenuOpen(false); }}>Luk</button>
                </div>
              </>
            )}
            {/* Add Text Button */}
            <button
              className="absolute top-3 left-3 z-20 px-3 py-1 bg-blue-800 bg-opacity-95 rounded shadow text-xs font-bold text-white hover:bg-opacity-100 transition border border-blue-900"
              onClick={handleAddText}
              type="button"
            >
              Tilføj Tekst
            </button>
            {/* Render banner texts */}
            {bannerTexts.map(textObj => (
              <div
                key={textObj.id}
                className={`absolute z-20 flex items-center gap-1 ${(!textObj.editing && !textObj.showColor) ? 'cursor-move' : 'cursor-default'}`}
                style={{
                  left: textObj.x,
                  top: textObj.y,
                  color: textObj.color,
                  fontWeight: textObj.bold ? 'bold' : 'normal',
                  fontSize: 22,
                  userSelect: textObj.editing ? 'text' : 'none',
                  pointerEvents: 'auto',
                }}
                onMouseDown={e => handleTextMouseDown(textObj.id, e)}
                onClick={e => {
                  e.stopPropagation();
                  if (!textObj.editing && !textObj.showColor) {
                    setBannerTexts(bannerTexts => bannerTexts.map(t => t.id === textObj.id ? { ...t, editing: true } : t));
                    setActiveTextId(textObj.id);
                    setMenuOpen(true);
                  }
                }}
              >
                {textObj.editing ? (
                  <>
                    <input
                      type="text"
                      value={textObj.text}
                      onChange={e => setBannerTexts(bannerTexts => bannerTexts.map(t => t.id === textObj.id ? { ...t, text: e.target.value } : t))}
                      onBlur={() => setBannerTexts(bannerTexts => bannerTexts.map(t => t.id === textObj.id ? { ...t, editing: false } : t))}
                      className="bg-white bg-opacity-80 px-1 rounded"
                      autoFocus
                    />
                    {/* Color swatch for current text color */}
                    <span
                      className="inline-block ml-2 w-4 h-4 rounded-full border border-gray-300 align-middle"
                      style={{ background: textObj.color }}
                      title="Nuværende tekstfarve"
                    />
                  </>
                ) : (
                  <span>{textObj.text}</span>
                )}
                {/* Bold toggle button */}
                <button
                  className={`flex items-center gap-1 px-2 py-1 rounded text-xs border ml-1 transition-all duration-150
                    ${textObj.bold ? 'bg-blue-700 text-white border-blue-900 shadow font-bold scale-105' : 'bg-white bg-opacity-90 text-blue-900 border-blue-300 hover:bg-blue-100'}
                  `}
                  title="Gør teksten fed"
                  onClick={e => {
                    e.stopPropagation();
                    setBannerTexts(bannerTexts => bannerTexts.map(t => t.id === textObj.id ? { ...t, bold: !t.bold } : t));
                  }}
                  type="button"
                  style={{ minWidth: 48 }}
                >
                  <span style={{ fontWeight: 'bold', fontSize: 18 }}>B</span>
                  <span className="text-xs font-semibold">Fedt</span>
                </button>
                {/* Text color picker toggle */}
                <button
                  className="px-1 py-0.5 bg-white bg-opacity-80 rounded text-xs border ml-1"
                  onClick={e => toggleTextColorPicker(textObj.id, e)}
                  type="button"
                >
                  🎨
                </button>
                {/* Delete text button */}
                <button
                  className="px-1 py-0.5 bg-red-100 rounded text-xs border ml-1 text-red-600"
                  onClick={e => { e.stopPropagation(); handleRemoveText(textObj.id); }}
                  type="button"
                >
                  ✕
                </button>
                {/* Text color picker */}
                {textObj.showColor && (
                  <div className="absolute left-full top-0 z-30" onClick={e => e.stopPropagation()}>
                    <SketchPicker
                      color={textObj.color}
                      onChange={color => setBannerTexts(bannerTexts => bannerTexts.map(t => t.id === textObj.id ? { ...t, color: color.hex } : t))}
                      disableAlpha
                    />
                    <button className="mt-2 px-2 py-1 bg-gray-800 text-white rounded text-xs font-semibold" onClick={() => { setBannerTexts(bannerTexts => bannerTexts.map(t => t.id === textObj.id ? { ...t, showColor: false } : t)); setMenuOpen(false); }}>Luk</button>
                  </div>
                )}
              </div>
            ))}
            {/* Edit button for image upload (hover) */}
            <div className="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity pointer-events-auto">
                <div className="flex items-center gap-2 px-4 py-2 bg-black bg-opacity-80 text-white rounded-full shadow-lg text-sm">
                  <FaCamera className="mr-2" /> Rediger billede
                </div>
              </div>
            </div>
            {/* Loading overlay */}
            {bannerUploading && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 z-20">
                <div className="text-white font-semibold">Uploader...</div>
              </div>
            )}
            {/* Error overlay */}
            {bannerError && (
              <div className="absolute top-2 left-2 bg-red-600 text-white px-3 py-1 rounded z-20 text-xs">
                {bannerError}
              </div>
            )}
            {/* Hidden file input */}
            <input
              id="banner-upload"
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleBannerChange}
              disabled={bannerUploading}
            />
            <div className="absolute -bottom-12 left-8">
              <div className="w-24 h-24 rounded-full border-4 border-white overflow-hidden bg-white shadow-lg">
                {user?.image ? (
                  <Image
                    src={user.image}
                    alt={user.name || 'User'}
                    width={96}
                    height={96}
                    className="w-full h-full object-cover"
                    unoptimized
                  />
                ) : (
                  <div className="w-full h-full bg-blue-100 flex items-center justify-center">
                    <FaUser className="text-blue-600 text-3xl" />
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="pt-16 pb-8 px-8">
            <div className="flex items-center mb-1">
              <h1 className="text-2xl font-bold text-gray-800 mr-2">
                {user?.name || 'Bruger'}
              </h1>
              
              {/* Admin Badge + Verification Badge (Admin first) */}
              <div className="flex items-center space-x-1">
                {/* Admin Badge: Only for MyckasP */}
                {user?.name === 'myckasp' && (
                  <div className="flex items-center group relative mr-1">
                    <div className="text-red-500 hover:text-red-600 transition-colors">
                      <FaCheckCircle className="h-5 w-5" />
                    </div>
                    {/* Tooltip */}
                    <div className="absolute -top-8 left-1/2 -translate-x-1/2 bg-red-700 text-white text-sm px-2 py-1 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                      <div className="absolute bottom-[-4px] left-1/2 -translate-x-1/2 w-2 h-2 bg-red-700 transform rotate-45"></div>
                      Administrator
                    </div>
                  </div>
                )}
                {/* Verification Badge */}
                {verificationStatus?.isVerified && (
                  <div className="flex items-center group relative">
                    <div className="text-blue-500 hover:text-blue-600 transition-colors">
                      <FaCheckCircle className="h-5 w-5" />
                    </div>
                    {/* Tooltip */}
                    <div className="absolute -top-8 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-sm px-2 py-1 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                      <div className="absolute bottom-[-4px] left-1/2 -translate-x-1/2 w-2 h-2 bg-gray-800 transform rotate-45"></div>
                      Verificeret Bruger
                    </div>
                  </div>
                )}
              </div>
            </div>
            <p className="text-gray-500 mb-6">{adminUserData?.email || user?.email}</p>
            
            {/* Show Balance Badge for Admins/Freelancers */}
            {isAdmin && (
              <div className="mb-6">
                <div className="inline-flex items-center bg-indigo-50 px-4 py-2 rounded-lg">
                  <FaWallet className="text-indigo-600 mr-2" />
                  <span className="font-medium text-indigo-700">Balance:</span>
                  <span className="ml-2 font-bold text-indigo-900">
                    <BalanceBadge />
                  </span>
                </div>
              </div>
            )}
            
            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 overflow-x-auto">
                <button
                  onClick={() => handleTabChange('purchases')}
                  className={`py-3 px-1 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                    activeTab === 'purchases'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } transition-colors duration-200 ease-out`}
                >
                  <FaShoppingBag className="mr-2" />
                  <span>Mine Køb</span>
                </button>
                <button
                  onClick={() => handleTabChange('favorites')}
                  className={`py-3 px-1 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                    activeTab === 'favorites'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } transition-colors duration-200 ease-out group`}
                >
                  <FaHeart className={`mr-2 ${activeTab === 'favorites' ? 'text-red-500' : 'group-hover:text-red-500'}`} />
                  <span>Mine Favoritter</span>
                </button>
                
                <button
                  onClick={() => handleTabChange('notifications')}
                  className={`py-3 px-1 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                    activeTab === 'notifications'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } transition-colors duration-200 ease-out`}
                >
                  <FaBell className="mr-2" />
                  <span>Notifikationer</span>
                </button>
                
                {/* Only show Products tab for admin users */}
                {(isAdmin || hasAdminAccessBeenConfirmed) && (
                  <button
                    onClick={() => handleTabChange('products')}
                    className={`py-3 px-1 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                      activeTab === 'products'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } transition-colors duration-200 ease-out`}
                  >
                    <FaCode className="mr-2" />
                    <span>Mine Produkter</span>
                  </button>
                )}

                {/* Only show Profile tab for admin users */}
                {(isAdmin || hasAdminAccessBeenConfirmed) && (
                  <button
                    onClick={() => handleTabChange('profile')}
                    className={`py-3 px-1 border-b-2 font-medium text-sm flex items-center ${
                      activeTab === 'profile'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } transition-colors duration-200 ease-out`}
                  >
                    <FaUser className="mr-2" />
                    <span>Min Udvikler Profil</span>
                  </button>
                )}

                {/* Transactions tab for admin/freelancers */}
                {(isAdmin || hasAdminAccessBeenConfirmed) && (
                  <button
                    onClick={() => handleTabChange('transactions')}
                    className={`py-3 px-1 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                      activeTab === 'transactions'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } transition-colors duration-200 ease-out`}
                  >
                    <FaExchangeAlt className="mr-2" />
                    <span>Transaktioner</span>
                  </button>
                )}
              </nav>
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="max-w-6xl mx-auto">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
}

// Small component to show balance in the header
function BalanceBadge() {
  const [balance, setBalance] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBalance = async () => {
      try {
        const response = await fetch('/api/users/balance');
        if (response.ok) {
          const data = await response.json();
          setBalance(data.balance);
        }
      } catch (error) {
        console.error('Error fetching balance:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchBalance();
  }, []);

  if (loading) {
    return <span className="inline-block w-6 h-4 bg-indigo-200 animate-pulse rounded"></span>;
  }

  return <span>{balance?.toLocaleString('da-DK')} DKK</span>;
}