import { NextResponse } from 'next/server';

/**
 * This route handles placeholder image requests.
 * It returns a simple SVG placeholder image for screenshots
 * and a placeholder response for files.
 */
export async function GET(request, { params }) {
  const path = params.path ? params.path.join('/') : '';
  
  // Create a simple SVG placeholder
  if (path.includes('screenshot')) {
    // This is a placeholder for a screenshot
    const svg = `
      <svg xmlns="http://www.w3.org/2000/svg" width="1920" height="1080" viewBox="0 0 1920 1080">
        <rect width="100%" height="100%" fill="#f0f0f0"/>
        <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="32" text-anchor="middle" fill="#888">
          Placeholder Image - Upload Failed
        </text>
      </svg>
    `;
    
    return new NextResponse(svg, {
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
      },
    });
  } else if (path.includes('file')) {
    // This is a placeholder for a file
    // Return a simple text file
    const text = 'This is a placeholder file. The original upload failed.';
    
    return new NextResponse(text, {
      headers: {
        'Content-Type': 'text/plain',
        'Content-Disposition': 'attachment; filename="placeholder.txt"',
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
      },
    });
  }
  
  // Default fallback
  return new NextResponse('Placeholder not found', { status: 404 });
} 