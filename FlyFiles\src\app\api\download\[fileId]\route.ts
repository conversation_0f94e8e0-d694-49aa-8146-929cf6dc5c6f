import { NextRequest, NextResponse } from 'next/server'
import { getFilesCollection, getDownloadLogsCollection } from '@/app/lib/mongodb'
import { ApiResponse } from '@/app/lib/types'
import { ObjectId } from 'mongodb'

export async function GET(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const { fileId } = params
    
    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'File ID required' } as ApiResponse,
        { status: 400 }
      )
    }

    const files = await getFilesCollection()
    const file = await files.findOne({ filename: fileId, isActive: true })
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'File not found or expired' } as ApiResponse,
        { status: 404 }
      )
    }

    // Check if file has expired
    if (new Date() > new Date(file.expiryDate)) {
      // Mark file as inactive
      await files.updateOne(
        { _id: file._id },
        { $set: { isActive: false } }
      )
      
      return NextResponse.json(
        { success: false, error: 'File has expired' } as ApiResponse,
        { status: 410 }
      )
    }

    // Check download limits
    if (file.downloadLimit !== -1 && file.downloadCount >= file.downloadLimit) {
      return NextResponse.json(
        { success: false, error: 'Download limit reached' } as ApiResponse,
        { status: 429 }
      )
    }

    // Log the download
    const downloadLogs = await getDownloadLogsCollection()
    const userAgent = request.headers.get('user-agent') || 'Unknown'
    const forwarded = request.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : 'unknown'

    await downloadLogs.insertOne({
      fileId: file._id.toString(),
      downloadedAt: new Date(),
      ip: ip,
      userAgent: userAgent
    })

    // Increment download count
    await files.updateOne(
      { _id: file._id },
      { $inc: { downloadCount: 1 } }
    )

    // In a real implementation, this would redirect to the actual file or stream it
    // For now, return file metadata
    return NextResponse.json({
      success: true,
      data: {
        filename: file.originalName,
        size: file.size,
        mimeType: file.mimeType,
        downloadUrl: `#download-${fileId}`, // Placeholder URL
        expiryDate: file.expiryDate,
        downloadsRemaining: file.downloadLimit === -1 ? 'unlimited' : file.downloadLimit - file.downloadCount - 1
      },
      message: 'File download ready - storage integration needed for actual file serving'
    } as ApiResponse)

  } catch (error) {
    console.error('Error processing download:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
}

// HEAD request for checking file availability without logging download
export async function HEAD(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const { fileId } = params
    
    const files = await getFilesCollection()
    const file = await files.findOne({ filename: fileId, isActive: true })
    
    if (!file || new Date() > new Date(file.expiryDate)) {
      return new NextResponse(null, { status: 404 })
    }

    return new NextResponse(null, { 
      status: 200,
      headers: {
        'Content-Length': file.size.toString(),
        'Content-Type': file.mimeType,
        'Cache-Control': 'no-cache'
      }
    })

  } catch (error) {
    return new NextResponse(null, { status: 500 })
  }
} 