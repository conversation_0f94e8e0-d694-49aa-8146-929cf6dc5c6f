import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import * as notificationUtils from '../../../../../lib/notifications';

// Timeout for the request in milliseconds (10 seconds)
const REQUEST_TIMEOUT = 10000;

// Helper function to add timeout to a promise
const timeoutPromise = (promise, ms) => {
  return Promise.race([
    promise,
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error(`Request timed out after ${ms}ms`)), ms)
    )
  ]);
};

// POST /api/user/notifications/mark-read - Mark a notification as read
export async function POST(request: NextRequest) {
  // Add a timeout to the entire request
  return timeoutPromise(handlePostRequest(request), REQUEST_TIMEOUT)
    .catch(error => {
      console.error('Mark notification as read API request timed out:', error);
      return NextResponse.json({ 
        success: false, 
        error: 'Request timed out. Please try again later.' 
      }, { status: 504 });
    });
}

// Separate the actual handler logic for cleaner error handling
async function handlePostRequest(request: NextRequest) {
  try {
    console.log('Mark notification as read API: Request received');
    
    // Get the request body
    const data = await request.json();
    const { notificationId } = data;
    
    console.log('Received notificationId:', notificationId);
    
    if (!notificationId) {
      return NextResponse.json({ error: 'Notification ID is required' }, { status: 400 });
    }
    
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      console.log('Mark notification as read API: Unauthorized request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    console.log('User discordId:', discordId?.slice(0, 6));
    
    if (!discordId) {
      console.log('Mark notification as read API: No Discord ID found');
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Mark notification as read using the utility function
    const result = await notificationUtils.markNotificationAsRead(discordId, notificationId);
    
    console.log('markNotificationAsRead result:', result);
    
    if (!result.success) {
      return NextResponse.json({ 
        error: result.error || 'Failed to mark notification as read',
        success: false
      }, { status: result.error === 'Notification not found' ? 404 : 500 });
    }
    
    return NextResponse.json({
      success: true,
      modified: result.modified
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return NextResponse.json({ 
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
} 