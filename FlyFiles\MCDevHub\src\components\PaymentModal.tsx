import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { FaTimes, FaExclamationTriangle, FaCheckCircle, FaWallet } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
  price: number;
  finalPrice?: number;
}

export default function PaymentModal({ isOpen, onClose, productId, productName, price, finalPrice }: PaymentModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { user } = useAuth();
  const [alreadyPurchased, setAlreadyPurchased] = useState<boolean>(false);
  const [checkingPurchase, setCheckingPurchase] = useState<boolean>(false);
  const [userBalance, setUserBalance] = useState<number | null>(null);
  const [loadingBalance, setLoadingBalance] = useState<boolean>(false);
  
  // Check if user has already purchased this product
  useEffect(() => {
    if (isOpen && user && productId) {
      setCheckingPurchase(true);
      
      fetch(`/api/users/has-purchased?productId=${productId}`)
        .then(response => response.json())
        .then(data => {
          setAlreadyPurchased(data.hasPurchased);
        })
        .catch(error => {
          console.error('Error checking product ownership:', error);
        })
        .finally(() => {
          setCheckingPurchase(false);
        });
    } else {
      // Reset state when modal opens without a user
      setAlreadyPurchased(false);
    }
  }, [isOpen, user, productId]);
  
  // Fetch user balance when modal opens
  useEffect(() => {
    if (isOpen && user) {
      setLoadingBalance(true);
      fetch('/api/users/balance')
        .then(response => response.json())
        .then(data => {
          setUserBalance(data.balance || 0);
        })
        .catch(error => {
          console.error('Error fetching user balance:', error);
          setUserBalance(0);
        })
        .finally(() => {
          setLoadingBalance(false);
        });
    }
  }, [isOpen, user]);

  // Prevent scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      // Store original body style
      const originalStyle = window.getComputedStyle(document.body).overflow;
      // Disable scrolling
      document.body.style.overflow = 'hidden';
      // Add padding to prevent layout shift when scrollbar disappears
      document.documentElement.style.paddingRight = `${window.innerWidth - document.documentElement.clientWidth}px`;
      
      // Reset error when opening
      setErrorMessage(null);
      
      // Cleanup function to restore original scroll behavior
      return () => {
        document.body.style.overflow = originalStyle;
        document.documentElement.style.paddingRight = '0';
      };
    }
  }, [isOpen]);

  const displayPrice = finalPrice !== undefined ? finalPrice : price;
  
  // Handle balance payment
  const handleBalancePayment = async () => {
    try {
      // Check if user is logged in
      if (!user) {
        throw new Error('Du skal være logget ind for at bruge din balance');
      }
      
      // Prevent purchase if already owned
      if (alreadyPurchased) {
        setErrorMessage('Du ejer allerede dette produkt');
        return;
      }
      
      // Check if user has enough balance
      if (userBalance !== null && userBalance < displayPrice) {
        throw new Error('Du har ikke nok balance til at købe dette produkt');
      }
      
      setIsProcessing(true);
      setErrorMessage(null);
      
      console.log(`Initiating balance payment for ${productId}`);
      
      // Use the balance-purchase endpoint
      const response = await fetch('/api/payments/balance-purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
        }),
      });
      
      console.log(`Balance payment API response status: ${response.status}`);
      
      const data = await response.json();
      console.log('Balance payment API response data:', data);
      
      if (!response.ok) {
        console.error('Balance payment API error:', data);
        
        // Format the error message for display
        let errorMsg: string;
        if (data.error === 'Insufficient balance') {
          errorMsg = 'Du har ikke nok balance til at købe dette produkt';
        } else if (data.error === 'Product not found') {
          errorMsg = `Produktet blev ikke fundet i databasen (ID: ${productId}).`;
        } else if (data.details) {
          errorMsg = `${data.error}: ${data.details}`;
        } else {
          errorMsg = data.error || 'Der opstod en fejl ved betalingen';
        }
        
        throw new Error(errorMsg);
      }
      
      // Update user balance
      setUserBalance((prevBalance) => prevBalance !== null ? prevBalance - displayPrice : null);
      
      // Show success message and redirect to profile
      window.location.href = '/profile?tab=purchases&purchase_success=true';
      
    } catch (error) {
      console.error('Balance payment error:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Der opstod en fejl ved betalingen. Prøv igen senere.');
      setIsProcessing(false);
    }
  }

  const handlePaymentSelection = async (method: 'stripe' | 'mobilepay') => {
    try {
      // Check if user is logged in
      if (!user) {
        // Redirect to Discord login using NextAuth
        const { signIn } = await import('next-auth/react');
        await signIn('discord', { callbackUrl: '/' });
        return;
      }
      
      // Prevent purchase if already owned
      if (alreadyPurchased) {
        setErrorMessage('Du ejer allerede dette produkt');
        return;
      }
      
      setIsProcessing(true);
      setErrorMessage(null);
      
      console.log(`Initiating ${method} payment for ${productId}`);
      
      // Create a new payment session
      const response = await fetch('/api/payments/create-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          paymentMethod: method,
          price: displayPrice,
        }),
      });

      console.log(`Payment API response status: ${response.status}`);
      
      const data = await response.json();
      console.log('Payment API response data:', data);
      
      if (!response.ok) {
        console.error('Payment API error:', data);
        
        // Format the error message for display
        let errorMsg: string;
        if (data.error === 'Product not found') {
          errorMsg = `Produktet blev ikke fundet i databasen (ID: ${productId}).`;
        } else if (data.details) {
          errorMsg = `${data.error}: ${data.details}`;
        } else {
          errorMsg = data.error || 'Der opstod en fejl ved betalingen';
        }
        
        throw new Error(errorMsg);
      }

      // Redirect to the payment URL
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('Ingen betalings-URL modtaget fra serveren');
      }
    } catch (error) {
      console.error('Payment error:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Der opstod en fejl ved betalingen. Prøv igen senere.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop with blur effect */}
          <motion.div 
            className="fixed inset-0 bg-black/50 backdrop-blur-sm" 
            onClick={onClose}
            aria-hidden="true"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          />
          
          {/* Modal content */}
          <motion.div 
            className="relative bg-white w-full max-w-md mx-auto rounded-xl shadow-2xl p-6 z-10"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            {/* Close button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Luk"
              disabled={isProcessing}
            >
              <FaTimes size={20} />
            </button>
            
            {/* Header */}
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">Vælg betalingsmetode</h3>
              <p className="text-gray-600 mt-1">
                Køb {productName} ({displayPrice} DKK)
              </p>
            </div>
            
            {/* Already purchased message */}
            {alreadyPurchased && (
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 flex items-start">
                <FaCheckCircle className="flex-shrink-0 w-5 h-5 mr-3 mt-0.5 text-blue-500" />
                <div>
                  <p className="font-medium">Du ejer allerede dette produkt</p>
                  <p className="text-sm mt-1">Dette produkt findes allerede i din samling. Du kan se alle dine køb på din profilside.</p>
                  <Link
                    href="/profile?tab=purchases"
                    className="mt-3 inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors group"
                    onClick={() => onClose()}
                  >
                    <span>Gå til Mine Køb</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5 group-hover:scale-110" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </Link>
                </div>
              </div>
            )}
            
            {/* Error message */}
            {!alreadyPurchased && errorMessage && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-700 flex items-start">
                <FaExclamationTriangle className="flex-shrink-0 w-5 h-5 mr-2 mt-0.5 text-red-500" />
                <div>
                  <p className="font-medium">Der opstod en fejl</p>
                  <p>{errorMessage}</p>
                </div>
              </div>
            )}
            
            {/* Loading state while checking purchases */}
            {checkingPurchase && (
              <div className="mb-4 flex justify-center items-center p-4">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500 mr-2"></div>
                <p className="text-gray-600">Kontrollerer tidligere køb...</p>
              </div>
            )}
            
            {/* Payment options - Hide completely if already purchased */}
            {!alreadyPurchased && (
              <div className="space-y-4">
                {/* Balance option */}
                {userBalance !== null && (
                  <button
                    onClick={handleBalancePayment}
                    disabled={isProcessing || checkingPurchase || loadingBalance || userBalance < displayPrice}
                    className={`w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:shadow-md transition-all group 
                      ${isProcessing || checkingPurchase || loadingBalance ? 'opacity-50 cursor-default bg-gray-50' : ''} 
                      ${userBalance < displayPrice ? 'opacity-50 cursor-not-allowed bg-gray-50' : ''}`}
                  >
                    <div className="flex flex-col items-start">
                      <span className="font-medium text-gray-800 group-hover:text-blue-600">
                        {isProcessing ? 'Behandler...' : loadingBalance ? 'Indlæser balance...' : 'Betal med Balance'}
                      </span>
                      {!loadingBalance && (
                        <span className={`text-xs mt-1 ${userBalance < displayPrice ? 'text-red-500' : 'text-green-600'}`}>
                          {userBalance < displayPrice 
                            ? `Ikke nok balance (${userBalance} DKK)` 
                            : `Din balance: ${userBalance} DKK`}
                        </span>
                      )}
                    </div>
                    <div className="w-24 h-8 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#635BFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-8 h-8">
                            <line x1="3" y1="22" x2="21" y2="22"></line>
                            <line x1="6" y1="18" x2="6" y2="11"></line>
                            <line x1="10" y1="18" x2="10" y2="11"></line>
                            <line x1="14" y1="18" x2="14" y2="11"></line>
                            <line x1="18" y1="18" x2="18" y2="11"></line>
                            <polygon points="12 2 20 7 4 7"></polygon>
                        </svg>
                    </div>
                  </button>
                )}
                
                {/* Stripe option */}
                <button
                  onClick={() => handlePaymentSelection('stripe')}
                  disabled={isProcessing || checkingPurchase}
                  className={`w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:shadow-md transition-all group 
                    ${isProcessing || checkingPurchase ? 'opacity-50 cursor-default bg-gray-50' : ''}`}
                >
                  <span className="font-medium text-gray-800 group-hover:text-blue-600">
                    {isProcessing ? 'Behandler...' : 'Betal med kort'}
                  </span>
                  <div className="w-24 h-8 flex items-center justify-center">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-8 h-8">
                      <path d="M21 4H3C1.89543 4 1 4.89543 1 6V18C1 19.1046 1.89543 20 3 20H21C22.1046 20 23 19.1046 23 18V6C23 4.89543 22.1046 4 21 4Z" stroke="#635BFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M1 10H23" stroke="#635BFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                </button>
                
                {/* MobilePay option */}
                <button
                  onClick={() => handlePaymentSelection('mobilepay')}
                  disabled={isProcessing || checkingPurchase}
                  className={`w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:shadow-md transition-all group 
                    ${isProcessing || checkingPurchase ? 'opacity-50 cursor-default bg-gray-50' : ''}`}
                >
                  <span className="font-medium text-gray-800 group-hover:text-blue-600">
                    {isProcessing ? 'Behandler...' : 'Betal med MobilePay'}
                  </span>
                  <div className="w-24 h-8 relative">
                    <Image
                      src="https://developer.mobilepay.dk/img/logo-blue.svg"
                      alt="MobilePay"
                      fill
                      style={{ objectFit: 'contain' }}
                    />
                  </div>
                </button>
              </div>
            )}
            
            {/* Already purchased action */}
            {alreadyPurchased && (
              <button
                onClick={onClose}
                className="w-full flex justify-center items-center p-4 bg-gray-100 rounded-lg hover:bg-gray-200 transition-all text-gray-700 font-medium"
              >
                Luk
              </button>
            )}
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
} 