import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

export async function GET(request) {
  try {
    // This endpoint should be protected in production
    // Check for admin authentication here
    
    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Get all freelancers that have a discordUserId but no avatar
    const freelancers = await db.collection('adminusers')
      .find({ 
        admintype: 'Freelancer',
        discordUserId: { $exists: true, $ne: null },
        $or: [
          { avatar: { $exists: false } },
          { avatar: null }
        ]
      })
      .toArray();
    
    if (freelancers.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No freelancers found that need avatar fixes',
        fixed: 0
      });
    }
    
    // For each freelancer, fetch their Discord avatar
    const updateResults = await Promise.all(
      freelancers.map(async (freelancer) => {
        try {
          // Fetch user data from Discord API
          const response = await fetch(`https://discord.com/api/users/${freelancer.discordUserId}`, {
            headers: {
              Authorization: `Bot ${process.env.DISCORD_BOT_TOKEN}`
            }
          });
          
          if (!response.ok) {
            console.error(`Failed to fetch Discord data for user ${freelancer.username}: ${response.status}`);
            return { success: false, username: freelancer.username };
          }
          
          const userData = await response.json();
          
          if (!userData.avatar) {
            console.error(`User ${freelancer.username} has no Discord avatar`);
            return { success: false, username: freelancer.username, reason: 'No avatar' };
          }
          
          // Update the user in the database
          await db.collection('adminusers').updateOne(
            { _id: freelancer._id },
            { $set: { avatar: userData.avatar } }
          );
          
          return { success: true, username: freelancer.username };
        } catch (error) {
          console.error(`Error updating avatar for ${freelancer.username}:`, error);
          return { success: false, username: freelancer.username, error: error.message };
        }
      })
    );
    
    const successfulUpdates = updateResults.filter(result => result.success);
    
    return NextResponse.json({
      success: true,
      message: `Fixed avatars for ${successfulUpdates.length} of ${freelancers.length} freelancers`,
      results: updateResults
    });
    
  } catch (error) {
    console.error('Error fixing Discord avatars:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      message: error.message 
    }, { status: 500 });
  }
} 