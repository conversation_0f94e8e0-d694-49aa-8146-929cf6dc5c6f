'use client';

import React, { useState, useEffect, useRef } from 'react';

interface CalendarProps {
  onSelectDate: (date: Date) => void;
  onClose: () => void;
  minDays?: number;
}

const Calendar: React.FC<CalendarProps> = ({ onSelectDate, onClose, minDays = 5 }) => {
  const [currentMonth, setCurrentMonth] = useState(() => {
    const today = new Date();
    const minDate = new Date();
    minDate.setDate(minDate.getDate() + minDays);
    
    // If minDate is in the next month, start with next month
    if (minDate.getMonth() !== today.getMonth()) {
      today.setMonth(today.getMonth() + 1);
      today.setDate(1);
    }
    return today;
  });
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const scrollPositionRef = useRef<number>(0);
  
  const minDate = new Date();
  minDate.setDate(minDate.getDate() + minDays);
  
  // Calculate days in month grid
  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const firstDayOfMonth = new Date(year, month, 1).getDay();
    
    // Adjust for Sunday being 0
    const adjustedFirstDay = firstDayOfMonth === 0 ? 6 : firstDayOfMonth - 1;
    
    const days = [];
    
    // Add empty slots for days before the first day of month
    for (let i = 0; i < adjustedFirstDay; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(year, month, i));
    }
    
    return days;
  };
  
  const days = getDaysInMonth(currentMonth);
  const weekdays = ['Man', 'Tir', 'Ons', 'Tor', 'Fre', 'Lør', 'Søn'];
  const months = [
    'Januar', 'Februar', 'Marts', 'April', 'Maj', 'Juni',
    'Juli', 'August', 'September', 'Oktober', 'November', 'December'
  ];
  
  const canGoBack = () => {
    const today = new Date();
    return currentMonth.getMonth() > today.getMonth() || currentMonth.getFullYear() > today.getFullYear();
  };
  
  const changeMonth = (increment: number) => {
    // Don't allow going back if it would go before current month
    if (increment < 0 && !canGoBack()) return;
    
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + increment);
    setCurrentMonth(newMonth);
  };
  
  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
    onSelectDate(date);
    setTimeout(() => {
      onClose();
    }, 200);
  };
  
  const isDateSelectable = (date: Date | null) => {
    if (!date) return false;
    return date >= minDate;
  };
  
  const formatDateString = (date: Date) => {
    return `${date.getDate()}. ${months[date.getMonth()]} ${date.getFullYear()}`;
  };
  
  // Prevent body scrolling when calendar is open, but preserve scroll position
  useEffect(() => {
    scrollPositionRef.current = window.scrollY;
    document.body.style.overflow = 'hidden';
    document.body.style.height = '100%';
    document.body.style.width = '100%';
    
    return () => {
      document.body.style.overflow = '';
      document.body.style.height = '';
      document.body.style.width = '';
      
      setTimeout(() => {
        window.scrollTo({
          top: scrollPositionRef.current,
          behavior: 'instant'
        });
      }, 0);
    };
  }, []);
  
  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('.calendar-container') && !target.closest('.deadline-input')) {
        onClose();
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);
  
  return (
    <div className="fixed inset-0 bg-transparent backdrop-blur-sm z-50 flex items-center justify-center calendar-overlay">
      <div className="calendar-container bg-white rounded-xl shadow-xl p-4 max-w-md w-full transform transition-transform animate-fadeIn">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-800">Vælg Deadline</h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="flex justify-between items-center mb-4">
          <button 
            type="button"
            onClick={() => changeMonth(-1)}
            className={`p-2 rounded-full hover:bg-gray-100 ${!canGoBack() ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            disabled={!canGoBack()}
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="black" xmlns="http://www.w3.org/2000/svg">
              <path d="M15 19l-7-7 7-7" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          <div className="text-gray-800 font-medium">
            {months[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </div>
          <button 
            type="button"
            onClick={() => changeMonth(1)}
            className="p-2 rounded-full hover:bg-gray-100 cursor-pointer"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="black" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 5l7 7-7 7" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
        
        <div className="grid grid-cols-7 gap-1 mb-2">
          {weekdays.map(day => (
            <div key={day} className="text-center text-sm font-medium text-gray-600 py-1">
              {day}
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-7 gap-1">
          {days.map((day, index) => (
            <div 
              key={index}
              className={`text-center py-2 rounded-md text-sm ${
                day && selectedDate && 
                day.getDate() === selectedDate.getDate() && 
                day.getMonth() === selectedDate.getMonth() && 
                day.getFullYear() === selectedDate.getFullYear()
                  ? 'bg-blue-600 text-white'
                  : !isDateSelectable(day) && day
                    ? 'text-gray-400 cursor-not-allowed bg-gray-50'
                    : day
                      ? 'text-gray-800 hover:bg-blue-100 cursor-pointer'
                      : ''
              }`}
              onClick={() => day && isDateSelectable(day) && handleDateClick(day)}
            >
              {day ? day.getDate() : ''}
            </div>
          ))}
        </div>
        
        <div className="mt-4 text-center text-sm text-gray-600">
          <p>Ikke muligt at vælge en deadline indenfor {minDays} dage fra i dag</p>
          <p className="mt-2 text-blue-600">Minimum deadline: {formatDateString(minDate)}</p>
        </div>
      </div>
    </div>
  );
};

export default Calendar; 