// User Plan Types
export type UserPlan = 'guest' | 'free' | 'upgrade1' | 'upgrade2';

// User Interface
export interface User {
  _id: string;
  email: string;
  name: string;
  image?: string;
  plan: UserPlan;
  createdAt: Date;
  updatedAt: Date;
  usage: {
    monthly: number; // bytes used this month
    weekly: number;  // bytes used this week
    session?: number; // bytes used this session (for guests)
  };
  subscription?: {
    id?: string;
    status: 'active' | 'inactive' | 'past_due';
    currentPeriodStart: Date;
    currentPeriodEnd: Date;
  };
}

// File Interface
export interface FileRecord {
  _id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  ownerId?: string; // undefined for guest uploads
  uploadDate: Date;
  expiryDate: Date;
  downloadCount: number;
  downloadLimit: number; // -1 for unlimited
  isActive: boolean;
  storageProvider?: 'local' | 'storj' | 'backblaze';
  storageKey?: string;
  sessionId?: string; // for guest uploads
}

// Session Interface for guest users
export interface GuestSession {
  _id: string;
  sessionId: string;
  totalUploaded: number; // bytes
  createdAt: Date;
  expiresAt: Date;
  files: string[]; // file IDs
}

// Plan Configuration
export interface PlanConfig {
  name: string;
  uploadLimit: {
    amount: number; // bytes
    period: 'session' | 'month' | 'week';
  };
  fileExpiry: number; // days
  downloadLimits: {
    configurable: boolean;
    unlimited: boolean;
  };
  features: string[];
  price?: {
    amount: number; // kr
    period: 'month';
  };
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Upload Response
export interface UploadResponse {
  fileId: string;
  filename: string;
  downloadUrl: string;
  expiryDate: Date;
}

// Usage Statistics
export interface UsageStats {
  current: number;
  limit: number;
  period: string;
  percentage: number;
  plan: UserPlan;
}

// File Upload Request
export interface FileUploadRequest {
  filename: string;
  size: number;
  mimeType: string;
  downloadLimit?: number;
}

// Download tracking
export interface DownloadLog {
  _id: string;
  fileId: string;
  downloadedAt: Date;
  ip: string;
  userAgent: string;
} 