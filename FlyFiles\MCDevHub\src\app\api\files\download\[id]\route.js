import { NextResponse } from 'next/server';
import { ObjectId } from 'mongodb';
import { connectToDatabase } from '@/lib/mongodb';
import { Readable } from 'stream';
import { GridFSBucket } from 'mongodb';

/**
 * API endpoint to download files directly from MongoDB GridFS
 */
export async function GET(request, context) {
  try {
    // Get the file ID from the route parameter
    const params = context.params;
    const id = params?.id;

    if (!id) {
      return NextResponse.json(
        { error: 'Invalid file ID' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Create GridFS bucket
    const bucket = new GridFSBucket(db);
    
    // Check if the file exists
    const fileInfo = await db.collection('fs.files').findOne({ 
      _id: new ObjectId(id)
    });

    if (!fileInfo) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Create readable stream from GridFS
    const downloadStream = bucket.openDownloadStream(new ObjectId(id));
    
    // Collect all chunks
    const chunks = [];
    
    // Wait for data
    for await (const chunk of downloadStream) {
      chunks.push(chunk);
    }
    
    // Combine chunks
    const fileBuffer = Buffer.concat(chunks);

    // Create a readable stream from buffer
    const stream = Readable.from(fileBuffer);

    // Set header based on the file's content type
    const contentType = fileInfo.metadata?.contentType || 'application/octet-stream';
    const filename = fileInfo.filename || 'download';
    
    // Use the original filename from metadata if available
    const originalFilename = fileInfo.metadata?.originalName || filename;

    // Create and return a streaming response
    return new NextResponse(stream, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${originalFilename}"`,
      },
    });
  } catch (error) {
    console.error('Error downloading file from GridFS:', error);
    return NextResponse.json(
      { error: 'Error downloading file' },
      { status: 500 }
    );
  }
} 