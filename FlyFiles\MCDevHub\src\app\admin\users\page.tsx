'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { FaUserPlus, FaUser, FaUsers, FaTimes, FaClipboard, FaCheck, FaSpinner, FaExclamationTriangle } from 'react-icons/fa';
import { FaDiscord } from 'react-icons/fa6';

interface AdminUser {
  _id: string;
  username: string;
  admintype: string;
  allowedcases: string;
  discordUserId: string;
  createdAt: string;
  createdBy: string;
}

export default function UserManagementPage() {
  const router = useRouter();
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<string | null>(null);
  const [isGeneratingInvite, setIsGeneratingInvite] = useState(false);
  const [inviteUrl, setInviteUrl] = useState<string | null>(null);
  const [isCopied, setIsCopied] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [formData, setFormData] = useState({
    admintype: 'Freelancer',
    allowedcases: [],
    discordUserId: '',
    description: ''
  });
  const [formErrors, setFormErrors] = useState({
    admintype: '',
    allowedcases: '',
    discordUserId: ''
  });

  // Available case types
  const caseTypes = [
    { value: 'partner', label: 'Partner' },
    { value: 'contact', label: 'Kontakt' },
    { value: 'custom-order', label: 'Bestilling' }
  ];

  useEffect(() => {
    const checkAdminAndFetchUsers = async () => {
      try {
        setLoading(true);
        
        // First check if user is logged in and is "MyckasP"
        const adminResponse = await fetch('/api/admin/me');
        if (!adminResponse.ok) {
          throw new Error('Du skal være logget ind for at se denne side');
        }
        
        const adminData = await adminResponse.json();
        if (adminData.user.username !== 'MyckasP') {
          router.push('/admin/dashboard');
          return;
        }
        
        setCurrentUser(adminData.user.username);
        
        // Fetch all admin users
        const usersResponse = await fetch('/api/admin/users');
        if (!usersResponse.ok) {
          throw new Error('Kunne ikke hente brugere');
        }
        
        const userData = await usersResponse.json();
        setUsers(userData.users);
      } catch (error) {
        console.error('Error fetching users:', error);
        setError(error.message || 'Der opstod en fejl ved indlæsning af brugere');
      } finally {
        setLoading(false);
      }
    };
    
    checkAdminAndFetchUsers();
  }, [router]);

  useEffect(() => {
    if (loading) {
      document.body.style.overflow = 'hidden';
      window.scrollTo(0, 0);
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [loading]);

  const handleCaseTypeToggle = (caseType: string) => {
    setFormData(prev => {
      const newAllowedCases = prev.allowedcases.includes(caseType)
        ? prev.allowedcases.filter(item => item !== caseType)
        : [...prev.allowedcases, caseType];
      
      return {
        ...prev,
        allowedcases: newAllowedCases
      };
    });
    
    // Clear error when selecting
    setFormErrors(prev => ({
      ...prev,
      allowedcases: ''
    }));
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear errors when typing
    setFormErrors(prev => ({
      ...prev,
      [name]: ''
    }));
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = { ...formErrors };
    
    if (!formData.admintype) {
      newErrors.admintype = 'Admin type er påkrævet';
      isValid = false;
    }
    
    if (formData.allowedcases.length === 0) {
      newErrors.allowedcases = 'Vælg mindst én formulartype';
      isValid = false;
    }
    
    if (!formData.discordUserId) {
      newErrors.discordUserId = 'Discord ID er påkrævet';
      isValid = false;
    }
    
    setFormErrors(newErrors);
    return isValid;
  };

  const handleGenerateInvite = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsGeneratingInvite(true);
    setError(null);
    setInviteUrl(null);
    
    try {
      const response = await fetch('/api/admin/generate-invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          admintype: formData.admintype,
          allowedcases: formData.allowedcases.join(','),
          discordUserId: formData.discordUserId,
          description: formData.description
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Kunne ikke generere invitationslink');
      }

      const data = await response.json();
      setInviteUrl(data.inviteUrl);
      setShowModal(false);
    } catch (error) {
      console.error('Error generating invite:', error);
      setError(error.message || 'Der opstod en fejl ved generering af invitationslink');
    } finally {
      setIsGeneratingInvite(false);
    }
  };

  const copyToClipboard = () => {
    if (inviteUrl) {
      navigator.clipboard.writeText(inviteUrl)
        .then(() => {
          setIsCopied(true);
          setTimeout(() => setIsCopied(false), 3000);
        })
        .catch(err => {
          console.error('Could not copy text: ', err);
        });
    }
  };

  const resetForm = () => {
    setFormData({
      admintype: 'Freelancer',
      allowedcases: [],
      discordUserId: '',
      description: ''
    });
    setFormErrors({
      admintype: '',
      allowedcases: '',
      discordUserId: ''
    });
    setError(null);
  };

  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90 backdrop-blur-sm">
        <div className="flex flex-col items-center max-w-sm mx-auto p-8 rounded-xl bg-white shadow-lg border border-gray-100">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mb-6" style={{ animationDuration: '0.4s' }}></div>
          <div className="text-blue-700 text-lg font-medium">Indlæser brugere...</div>
          <p className="text-gray-500 text-sm mt-2 text-center">Vent venligst mens vi henter brugerlisten</p>
        </div>
      </div>
    );
  }

  if (error && !showModal) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-center text-red-500 mb-4">
              <FaExclamationTriangle className="h-12 w-12" />
            </div>
            <h1 className="text-2xl font-bold text-center text-gray-800 mb-2">Fejl</h1>
            <p className="text-gray-600 text-center mb-6">{error}</p>
            <div className="flex justify-center">
              <Link 
                href="/admin/dashboard"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Tilbage til dashboard
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl shadow-lg p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white mb-2">Brugerstyring</h1>
              <p className="text-blue-100">Administrer brugere og generer invitationslinks</p>
            </div>
            <Link
              href="/admin/add-users"
              className="mt-4 md:mt-0 inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-indigo-700 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white transition-colors cursor-default"
            >
              <FaUserPlus className="mr-2" />
              Inviter ny bruger
            </Link>
          </div>
        </div>

        {/* Users List */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-6 cursor-default">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center group hover:text-orange-500 transition-colors duration-200">
            <FaUsers className="mr-2 text-gray-600 group-hover:text-orange-500 transition-colors duration-200" />
            <h2 className="text-xl font-semibold text-gray-800 group-hover:text-orange-500 transition-colors duration-200">Admin brugere</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-default">
                    <div className="flex items-center justify-center">
                      <svg className="w-4 h-4 mr-2 hover:stroke-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      <span className="text-center">Brugernavn</span>
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-default">
                    <div className="flex items-center justify-center">
                      <svg className="w-4 h-4 mr-2 hover:stroke-purple-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      Admin Type
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-default">
                    <div className="flex items-center justify-center">
                      <svg className="w-4 h-4 mr-2 hover:stroke-green-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                      </svg>
                      Tilladte Formularer
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-default">
                    <div className="flex items-center justify-center">
                      <FaDiscord className="w-4 h-4 mr-2 text-gray-500 hover:text-indigo-500 transition-colors duration-200" />
                      <span className="text-gray-500">Discord ID</span>
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-default">
                    <div className="flex items-center justify-center">
                      <svg className="w-4 h-4 mr-2 hover:stroke-pink-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      Oprettet Dato
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-default">
                    <div className="flex items-center justify-center">
                      <svg className="w-4 h-4 mr-2 hover:stroke-orange-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                      </svg>
                      Oprettet Af
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user, index) => (
                  <tr key={user._id} className="hover:bg-gray-50 cursor-default">
                    <td className="px-6 py-4 whitespace-nowrap cursor-default group">
                      <div className="flex flex-col items-center justify-center">
                        <div className={`flex-shrink-0 h-10 w-10 flex items-center justify-center bg-gray-100 rounded-full cursor-default mb-2 transition-colors duration-200 group-hover:${
                            index % 3 === 0 ? 'bg-blue-100' : index % 3 === 1 ? 'bg-purple-100' : 'bg-orange-100'
                          }`}>
                          <FaUser className={`h-5 w-5 text-gray-500 cursor-default transition-colors duration-200 group-hover:${
                            index % 3 === 0 ? 'text-blue-500' : index % 3 === 1 ? 'text-purple-500' : 'text-orange-500'
                          }`} />
                        </div>
                        <div className="cursor-default">
                          <div className={`text-sm font-medium text-gray-900 cursor-default text-center transition-colors duration-200 group-hover:${
                            index % 3 === 0 ? 'text-blue-600' : index % 3 === 1 ? 'text-purple-600' : 'text-orange-600'
                          }`}>
                            {user.username}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap cursor-default">
                      <div className="flex justify-center">
                        <span className="px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200 cursor-default">
                          {user.admintype}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap cursor-default">
                      <div className="flex flex-wrap gap-1 justify-center cursor-default">
                        {user.allowedcases.split(',').map((caseType, index, array) => (
                          <span 
                            key={index} 
                            className={`px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200 cursor-default ${
                              array.length > 2 ? 'mb-1' : ''
                            }`}
                          >
                            {caseType.trim()}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center cursor-default">
                      {user.discordUserId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center cursor-default">
                      {new Date(user.createdAt).toLocaleString('da-DK', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center cursor-default">
                      {user.createdBy}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {users.length === 0 && (
            <div className="p-6 text-center text-gray-500">
              <p>Ingen brugere fundet.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
