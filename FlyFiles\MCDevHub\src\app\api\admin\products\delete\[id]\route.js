import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { verifyAdminToken } from '@/lib/auth';
import { ObjectId } from 'mongodb';
import { deleteFromCloudinary } from '@/lib/cloudinary';

/**
 * DELETE /api/admin/products/delete/[id]
 * Deletes a product and its images from MongoDB and Cloudinary.
 */
export async function DELETE(request, { params }) {
  try {
    // Authenticate admin
    const admin = await verifyAdminToken();
    if (!admin) {
      return NextResponse.json({ message: 'Ikke autoriseret' }, { status: 401 });
    }

    // Get the id from params
    const { id } = params;
    if (!id || !ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Ugyldigt produkt ID' }, { status: 400 });
    }

    const { db } = await connectToDatabase();
    const product = await db.collection('products').findOne({ _id: new ObjectId(id) });

    if (!product) {
      return NextResponse.json({ message: 'Produkt ikke fundet' }, { status: 404 });
    }

    // Delete images from Cloudinary
    const deleteImage = async (fileObj) => {
      if (fileObj && fileObj.fileId && !fileObj.isPlaceholder) {
        try {
          await deleteFromCloudinary(fileObj.fileId);
        } catch (err) {
          // Log but don't fail the whole operation
          console.error('Cloudinary delete error:', err);
        }
      }
    };

    if (Array.isArray(product.screenshotUrls)) {
      for (const img of product.screenshotUrls) {
        await deleteImage(img);
      }
    }
    if (Array.isArray(product.fileUrls)) {
      for (const file of product.fileUrls) {
        await deleteImage(file);
      }
    }

    // Delete product from MongoDB
    await db.collection('products').deleteOne({ _id: new ObjectId(id) });

    return NextResponse.json({ message: 'Produkt og billeder slettet' }, { status: 200 });
  } catch (error) {
    console.error('Fejl ved sletning af produkt:', error);
    return NextResponse.json({ message: 'Serverfejl ved sletning af produkt' }, { status: 500 });
  }
}