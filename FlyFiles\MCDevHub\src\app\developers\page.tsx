'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FaGithub, FaDiscord, FaEnvelope, FaJava, FaDatabase, FaTools, FaGamepad, FaCode, FaGlobe, FaYoutube, FaSearch, FaUser, FaCheckCircle, FaBox } from 'react-icons/fa';
import Head from 'next/head';

// Freelancer interface
interface Freelancer {
  _id: string;
  username: string;
  discordUserId: string;
  avatar: string;
  avatarUrl?: string;
  bannerImage?: string;
  description?: string;
  isVerified?: boolean;
  verifiedAt?: string;
  productCount: number;
  activeProductCount: number;
}

export default function DevelopersPage() {
  const [freelancers, setFreelancers] = useState<Freelancer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredFreelancers, setFilteredFreelancers] = useState<Freelancer[]>([]);
  const [failedAvatars, setFailedAvatars] = useState<Set<string>>(new Set());

  // Set page title
  useEffect(() => {
    // Set initial title
    document.title = "Freelancere | MCDevHub";
    
    // Maintain title with an interval in case it gets overwritten
    const titleInterval = setInterval(() => {
      if (document.title !== "Freelancere | MCDevHub") {
        document.title = "Freelancere | MCDevHub";
      }
    }, 500);
    
    // Cleanup
    return () => clearInterval(titleInterval);
  }, []);

  // Fetch freelancers when component mounts
  useEffect(() => {
    const fetchFreelancers = async () => {
      try {
        const response = await fetch('/api/freelancers');
        if (response.ok) {
          const data = await response.json();
          setFreelancers(data.freelancers || []);
          setFilteredFreelancers(data.freelancers || []);
        }
      } catch (error) {
        console.error('Error fetching freelancers:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFreelancers();
  }, []);

  // Handle avatar loading errors
  const handleAvatarError = (freelancerId: string) => {
    setFailedAvatars(prev => new Set(prev).add(freelancerId));
  };

  // Filter freelancers based on search query
  useEffect(() => {
    // Build the list based on search query
    let newList = searchQuery.trim() === ''
      ? [...freelancers]
      : freelancers.filter(freelancer =>
          freelancer.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (freelancer.description && freelancer.description.toLowerCase().includes(searchQuery.toLowerCase()))
        );
    // Always position MyckasP first if present (by username)
    const idx = newList.findIndex(f => f.username === 'MyckasP');
    if (idx > 0) {
      const [me] = newList.splice(idx, 1);
      newList.unshift(me);
    }
    setFilteredFreelancers(newList);
  }, [searchQuery, freelancers]);

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };
  
  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" }
    },
    hover: { 
      y: -5,
      boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      transition: { duration: 0.2, ease: "easeOut" }
    }
  };
  return (
    <>
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 pt-12">
        <Head>
          <title>Freelancere | MCDevHub</title>
        </Head>
        {/* Freelancers Section */}
        <section className="py-16 relative z-10">
          <div className="container mx-auto px-4">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={fadeIn}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-gray-800 mb-4">Find den rette freelancer til dit projekt</h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto whitespace-nowrap">Alle vores freelancere er nøje udvalgt og har bevist deres færdigheder gennem kvalitetsprodukter</p>
              
              {/* Search Bar */}
              <div className="max-w-2xl mx-auto mt-8 relative">
                <div className="flex items-center border-2 border-gray-300 rounded-full overflow-hidden bg-white shadow-sm focus-within:shadow-md focus-within:border-blue-500 transition-all">
                  <div className="pl-4 pr-2">
                    <FaSearch className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Søg efter en udvikler..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full py-3 px-2 outline-none text-gray-700"
                  />
                </div>
              </div>
            </motion.div>
            
            {/* Freelancers Grid */}
            {isLoading ? (
              <div className="flex flex-col items-center justify-center py-20">
                <div className="flex flex-col items-center max-w-xs mx-auto p-6 rounded-xl bg-white shadow border border-gray-100">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4" style={{ animationDuration: '0.4s' }}></div>
                  <div className="text-blue-700 text-base font-medium">Indlæser freelancere...</div>
                  <p className="text-gray-500 text-xs mt-1 text-center">Vent venligst mens vi henter listen</p>
                </div>
              </div>
            ) : (
              <motion.div 
                variants={staggerContainer}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, margin: "-50px" }}
                className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
              >
                {filteredFreelancers.length > 0 ? (
                  filteredFreelancers.map((freelancer) => (
                    <motion.div 
                      key={freelancer._id}
                      variants={cardVariants}
                      whileHover="hover"
                      className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 transition-all"
                    >
                      <Link href={`/developers/${freelancer.username}`} className="block">
                        <div className="p-4 text-center">
                          <div className="relative w-24 h-24 mx-auto mb-4">
                            <div className="w-full h-full rounded-full overflow-hidden border-4 border-white shadow-md bg-gray-100">
                              {freelancer.avatarUrl && !failedAvatars.has(freelancer._id) ? (
                                <Image
                                  src={freelancer.avatarUrl}
                                  alt={freelancer.username}
                                  width={96}
                                  height={96}
                                  className="w-full h-full object-cover"
                                  unoptimized
                                  onError={() => handleAvatarError(freelancer._id)}
                                />
                              ) : freelancer.discordUserId && freelancer.avatar && !failedAvatars.has(freelancer._id) ? (
                                <Image
                                  src={`https://cdn.discordapp.com/avatars/${freelancer.discordUserId}/${freelancer.avatar}.png?size=256`}
                                  alt={freelancer.username}
                                  width={96}
                                  height={96}
                                  className="w-full h-full object-cover"
                                  unoptimized
                                  onError={() => handleAvatarError(freelancer._id)}
                                />
                              ) : (
                                <div className="w-full h-full bg-blue-100 flex items-center justify-center">
                                  <FaUser className="text-blue-600 text-3xl" />
                                </div>
                              )}
                            </div>
                            {/* BADGE LOGIC: Only one badge, always bottom-left */}
                            {freelancer.isVerified && freelancer.discordUserId === 'MYCKASP_DISCORD_ID' && (
                              <div className="absolute bottom-0 left-0 bg-red-500 rounded-full p-1 border-2 border-white shadow-sm">
                                <FaCheckCircle className="text-white w-3 h-3" />
                              </div>
                            )}
                            {freelancer.isVerified && freelancer.discordUserId !== 'MYCKASP_DISCORD_ID' && (
                              <div className="absolute bottom-0 left-0 bg-blue-500 rounded-full p-1 border-2 border-white shadow-sm">
                                <FaCheckCircle className="text-white w-3 h-3" />
                              </div>
                            )}
                            {freelancer.username === 'MyckasP' && (
                              <div className="absolute bottom-0 left-0 bg-red-500 rounded-full p-1 border-2 border-white shadow-sm">
                                <FaCheckCircle className="text-white w-3 h-3" />
                              </div>
                            )}
                          </div>
                          <h3 className="font-bold text-lg text-gray-800 mb-1">{freelancer.username}</h3>
                          <div className="flex justify-center space-x-2 mb-2">
                            {freelancer.username === 'MyckasP' && (
                              <>
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                  Admin
                                </span>
                                {freelancer.isVerified && (
                                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Verificeret
                                  </span>
                                )}
                              </>
                            )}
                            {freelancer.username !== 'MyckasP' && freelancer.isVerified && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                Verificeret
                              </span>
                            )}
                          </div>
                          <p className="text-gray-500 text-sm">
                            <span className="font-medium">
                              {freelancer.username === 'MyckasP' ? 1 + freelancer.productCount : freelancer.productCount}
                            </span>
                            <span className="ml-1">
                              {freelancer.username === 'MyckasP' ? 
                                (1 + freelancer.productCount === 1 ? 'Produkt' : 'Produkter') : 
                                (freelancer.productCount === 1 ? 'Produkt' : 'Produkter')
                              }
                            </span>
                            {freelancer.activeProductCount !== freelancer.productCount && freelancer.activeProductCount > 0 && (
                              <span className="ml-1 text-green-600">
                                ({freelancer.activeProductCount} aktiv{freelancer.activeProductCount !== 1 ? 'e' : ''})
                              </span>
                            )}
                          </p>
                        </div>
                      </Link>
                    </motion.div>
                  ))
                ) : (
                  <div className="col-span-full py-16 text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                      <FaSearch className="text-gray-400 text-xl" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 mb-2">Ingen freelancere fundet</h3>
                    <p className="text-gray-500">
                      {searchQuery 
                        ? `Ingen freelancere matchede søgningen "${searchQuery}"`
                        : 'Der er endnu ingen freelancere tilgængelige'}
                    </p>
                  </div>
                )}
              </motion.div>
            )}
          </div>
        </section>
        
        
        {/* Call to Action */}
        <section className="relative py-20 overflow-hidden bg-gray-900">
          {/* Background pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute -right-24 -top-24 w-96 h-96 rounded-full bg-blue-600"></div>
            <div className="absolute -left-24 -bottom-24 w-96 h-96 rounded-full bg-purple-600"></div>
            <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] rounded-full bg-gradient-to-r from-indigo-600 to-blue-600 blur-3xl opacity-20"></div>
            <div className="absolute inset-0" style={{ backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%239C92AC\' fill-opacity=\'0.05\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")', backgroundSize: '30px 30px' }}></div>
          </div>

          <div className="container relative mx-auto px-4">
            <motion.div 
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="flex flex-col md:flex-row items-center justify-between gap-12"
            >
              {/* Left side content */}
              <div className="md:w-1/2 max-w-xl">
                <div className="inline-block px-3 py-1 mb-6 text-xs font-semibold rounded-full bg-blue-600/20 text-blue-300 hover:bg-blue-600/30 transition-all duration-300 ease-in-out cursor-default">
                  CUSTOM UDVIKLING
                </div>
                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6 text-white">
                  Få udviklet dit <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">drømmeplugin</span> af professionelle
                </h2>
                <p className="text-lg text-gray-300 mb-10 leading-relaxed">
                  Uanset om det er et simpelt skript eller et komplekst plugin, så står vores dygtige udviklere
                  klar til at bringe dine idéer til live og gøre din Minecraft server unik.
                </p>
                <Link 
                  href="/custom-orders"
                  className="inline-flex items-center justify-center bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8 py-4 rounded-xl font-medium text-lg shadow-lg transition-all duration-300 ease-in-out hover:shadow-blue-500/25 hover:shadow-xl group"
                >
                  <span>Start Dit Projekt i Dag</span>
                  <svg 
                    className="w-5 h-5 ml-2 transform group-hover:translate-x-1 group-hover:scale-110 transition-all duration-200 ease-[cubic-bezier(0.4,0,0.2,1)]" 
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>

              {/* Right side illustration */}
              <div className="md:w-1/2 relative flex justify-center">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="w-full max-w-md relative"
                >
                  <div className="relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-6 shadow-2xl border border-gray-700 overflow-hidden">
                    <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
                    <div className="flex items-center mb-6">
                      <div className="flex space-x-2">
                        <div className="w-3 h-3 rounded-full bg-red-500"></div>
                        <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      </div>
                      <div className="ml-4 text-xs text-gray-400 font-mono">plugin.java</div>
                    </div>
                    <code className="block text-left text-sm font-mono">
                      <div className="text-gray-400">// Din idé → Vores kode</div>
                      <div className="mt-2">
                        <span className="text-purple-400">public class </span>
                        <span className="text-yellow-300">DrømmePlugin </span>
                        <span className="text-blue-300">extends </span>
                        <span className="text-green-300">JavaPlugin </span>{"{"}
                      </div>
                      <div className="ml-4 mt-1">
                        <span className="text-purple-400">@Override</span>
                      </div>
                      <div className="ml-4">
                        <span className="text-purple-400">public void </span>
                        <span className="text-blue-300">onEnable</span>() {"{"}
                      </div>
                      <div className="ml-8 text-green-300">
                        // Dit unikke plugin starter her
                      </div>
                      <div className="ml-8 text-green-300">
                        // Fortæl os om din idé!
                      </div>
                      <div className="ml-4">{"}"}</div>
                      <div>{"}"}</div>
                    </code>
                    <div className="absolute -bottom-6 -right-6 w-24 h-24 rounded-full bg-blue-500 blur-3xl opacity-20"></div>
                  </div>
                  <div className="absolute -bottom-4 -right-4 w-full h-full rounded-2xl border border-blue-400/20 -z-10"></div>
                  <div className="absolute -bottom-8 -right-8 w-full h-full rounded-2xl border border-purple-400/20 -z-20"></div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
} 
