@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Arrow animation for links */
.arrow-wrapper {
  display: inline-flex;
  align-items: center;
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.group:hover .arrow-wrapper {
  transform: translateX(8px);
}

.arrow-wrapper svg {
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.group:hover .arrow-wrapper svg {
  transform: scale(1.1);
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover animations */
.transition-transform {
  transition: transform 0.3s ease;
}

.hover\:transform:hover {
  transform: scale(1.05);
}

/* Form styles */
input, textarea, select {
  color: #333 !important;
  background-color: white !important;
}

input::placeholder, textarea::placeholder {
  color: #9ca3af !important;
  opacity: 1 !important;
}

select {
  appearance: menulist !important;
  padding-right: 2rem !important;
}

select option {
  color: #333 !important;
  background-color: white !important;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

.animate-typing {
  animation: typing 8s steps(40, end) infinite;
}
