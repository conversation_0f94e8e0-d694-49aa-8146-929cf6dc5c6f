import NextAuth from "next-auth"
import Discord<PERSON><PERSON>ider from "next-auth/providers/discord"
import { MongoDBAdapter } from "@auth/mongodb-adapter"
import { MongoClient } from "mongodb"

// Validate required environment variables
if (!process.env.MONGODB_URI) {
  throw new Error('MONGODB_URI is not defined')
}
if (!process.env.DISCORD_CLIENT_ID) {
  throw new Error('DISCORD_CLIENT_ID is not defined')
}
if (!process.env.DISCORD_CLIENT_SECRET) {
  throw new Error('DISCORD_CLIENT_SECRET is not defined')
}
if (!process.env.NEXTAUTH_SECRET) {
  throw new Error('NEXTAUTH_SECRET is not defined')
}

const client = new MongoClient(process.env.MONGODB_URI, {
  serverSelectionTimeoutMS: 5000,
  connectTimeoutMS: 10000,
})

// Create a promise that handles connection errors
const clientPromise = client.connect().catch(error => {
  console.error('MongoDB connection error:', error)
  throw error
})

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: MongoDBAdapter(clientPromise, { databaseName: "codehub" }),
  trustHost: true,
  debug: process.env.NODE_ENV === "development",
  providers: [
    DiscordProvider({
      clientId: process.env.DISCORD_CLIENT_ID,
      clientSecret: process.env.DISCORD_CLIENT_SECRET,
      authorization: {
        params: {
          scope: "identify email",
          response_type: "code"
        }
      },
      profile(profile) {
        return {
          id: profile.id,
          name: profile.username,
          email: profile.email,
          image: profile.avatar
            ? `https://cdn.discordapp.com/avatars/${profile.id}/${profile.avatar}.png`
            : null,
        }
      }
    })
  ],
  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      // Allow account linking for Discord provider
      if (account?.provider === "discord") {
        try {
          const db = (await clientPromise).db("codehub")

          // Check if there's already a user with this email
          const existingUser = await db.collection("users").findOne({
            email: user.email
          })

          if (existingUser) {
            console.log(`Linking Discord account to existing user: ${user.email}`)

            // Check if this Discord account is already linked to another user
            const existingAccount = await db.collection("accounts").findOne({
              provider: "discord",
              providerAccountId: account.providerAccountId
            })

            if (existingAccount && existingAccount.userId !== existingUser._id.toString()) {
              console.log("Discord account already linked to different user")
              return false
            }

            // Link the Discord account to the existing user if not already linked
            if (!existingAccount) {
              await db.collection("accounts").insertOne({
                userId: existingUser._id.toString(),
                type: account.type,
                provider: account.provider,
                providerAccountId: account.providerAccountId,
                access_token: account.access_token,
                refresh_token: account.refresh_token,
                expires_at: account.expires_at,
                token_type: account.token_type,
                scope: account.scope,
                // Store Discord profile information
                username: profile?.username || user.name,
                avatar: profile?.avatar,
                avatarUrl: user.image || (profile?.avatar ? `https://cdn.discordapp.com/avatars/${profile.id}/${profile.avatar}.png` : null),
                email: profile?.email || user.email,
                discordId: profile?.id || account.providerAccountId,
              })
              console.log("Successfully linked Discord account to existing user")
            } else {
              // Update existing account with latest profile information
              await db.collection("accounts").updateOne(
                {
                  userId: existingUser._id.toString(),
                  provider: "discord",
                  providerAccountId: account.providerAccountId
                },
                {
                  $set: {
                    access_token: account.access_token,
                    refresh_token: account.refresh_token,
                    expires_at: account.expires_at,
                    username: profile?.username || user.name,
                    avatar: profile?.avatar,
                    avatarUrl: user.image || (profile?.avatar ? `https://cdn.discordapp.com/avatars/${profile.id}/${profile.avatar}.png` : null),
                    email: profile?.email || user.email,
                    discordId: profile?.id || account.providerAccountId,
                    lastLogin: new Date()
                  }
                }
              )
              console.log("Updated existing Discord account with latest profile info")
            }
          }

          // Also update adminusers collection if this user is an admin
          const discordUserId = profile?.id || account.providerAccountId
          console.log(`Checking for admin user with Discord ID: ${discordUserId}`)

          if (discordUserId) {
            const adminUser = await db.collection("adminusers").findOne({
              discordUserId: discordUserId
            })

            console.log(`Admin user lookup result:`, adminUser ? `Found: ${adminUser.username}` : 'Not found')

            if (adminUser) {
              // Update admin user with latest Discord avatar information
              const avatarUrl = user.image || (profile?.avatar ? `https://cdn.discordapp.com/avatars/${profile.id}/${profile.avatar}.png` : null)

              await db.collection("adminusers").updateOne(
                { discordUserId: discordUserId },
                {
                  $set: {
                    avatar: profile?.avatar,
                    avatarUrl: avatarUrl,
                    lastLogin: new Date()
                  }
                }
              )
              console.log(`Updated admin user avatar for ${adminUser.username} with URL: ${avatarUrl}`)
            }
          }

          return true
        } catch (error) {
          console.error("Error in signIn callback:", error)
          return false
        }
      }

      return true
    },
    async session({ session, user }) {
      // Add user ID to session
      if (session.user) {
        session.user.id = user.id

        const db = (await clientPromise).db("codehub")

        // Fetch Discord account information to get username and avatar
        const discordAccount = await db.collection("accounts").findOne({
          userId: user.id,
          provider: "discord"
        })

        // Also try to get user info from the general users collection
        const generalUser = await db.collection("users").findOne({
          discordUserId: user.id
        })

        // If we have Discord account data, use it to populate session
        if (discordAccount) {
          session.user.name = discordAccount.username || session.user.name
          session.user.image = discordAccount.avatarUrl || session.user.image
          session.user.email = discordAccount.email || session.user.email
        } else if (generalUser) {
          // Fallback to general user data if no Discord account data
          session.user.name = generalUser.username || session.user.name
          session.user.image = generalUser.avatarUrl || session.user.image
          session.user.email = generalUser.email || session.user.email
        }

        // Check if user is admin by looking up Discord ID in adminusers collection
        // First, get the actual Discord ID from the account
        let discordUserId = user.id;
        if (discordAccount && discordAccount.discordId) {
          discordUserId = discordAccount.discordId;
        } else if (discordAccount && discordAccount.providerAccountId) {
          discordUserId = discordAccount.providerAccountId;
        }

        // Store the Discord ID in the session for API endpoints to use
        session.user.id = discordUserId;

        console.log('Looking up admin with Discord ID:', discordUserId);
        const admin = await db.collection("adminusers").findOne({
          discordUserId: discordUserId
        })

        session.user.isAdmin = !!admin
        if (admin) {
          session.user.adminType = admin.admintype
          session.user.allowedCases = admin.allowedcases
        }
      }
      return session
    },
    async jwt({ token, user, account }) {
      // Store Discord ID in token
      if (account && user) {
        token.discordId = account.providerAccountId
      }
      return token
    }
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  session: {
    strategy: "database",
  },
})

// Legacy function for backward compatibility - now uses NextAuth session
export async function verifyAdminToken() {
  try {
    const session = await auth()

    if (!session?.user?.isAdmin) {
      return null
    }

    return {
      username: session.user.name,
      admintype: session.user.adminType,
      allowedcases: session.user.allowedCases,
      discordUserId: session.user.id
    }
  } catch (error) {
    console.error('Error verifying admin token:', error)
    return null
  }
}