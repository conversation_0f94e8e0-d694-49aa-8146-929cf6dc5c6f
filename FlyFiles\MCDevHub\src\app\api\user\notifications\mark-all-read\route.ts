import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { MongoClient } from 'mongodb';

// MongoDB connection setup
if (!process.env.MONGODB_URI) {
  throw new Error('Please add your Mongo URI to .env.local');
}

const uri = process.env.MONGODB_URI;

// Timeout for the request in milliseconds (10 seconds)
const REQUEST_TIMEOUT = 10000;

// Helper function to add timeout to a promise
const timeoutPromise = (promise, ms) => {
  return Promise.race([
    promise,
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error(`Request timed out after ${ms}ms`)), ms)
    )
  ]);
};

// POST /api/user/notifications/mark-all-read - Mark all notifications as read
export async function POST(request: NextRequest) {
  // Add a timeout to the entire request
  return timeoutPromise(handlePostRequest(request), REQUEST_TIMEOUT)
    .catch(error => {
      console.error('Mark all notifications as read API request timed out:', error);
      return NextResponse.json({ 
        success: false, 
        error: 'Request timed out. Please try again later.' 
      }, { status: 504 });
    });
}

// Separate the actual handler logic for cleaner error handling
async function handlePostRequest(request: NextRequest) {
  try {
    console.log('Mark all notifications as read API: Request received');
    
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      console.log('Mark all notifications as read API: Unauthorized request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      console.log('Mark all notifications as read API: No Discord ID found');
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    console.log(`Mark all notifications as read API: Marking all notifications as read for user ${discordId.slice(0, 6)}...`);
    
    // Connect to MongoDB with timeout options
    const client = new MongoClient(uri, {
      serverSelectionTimeoutMS: 5000, // 5 seconds
      connectTimeoutMS: 5000, // 5 seconds
      socketTimeoutMS: 5000 // 5 seconds
    });
    
    try {
      await client.connect();
      const db = client.db("codehub");
      
      // Update all unread notifications to read
      const result = await db.collection('users').updateOne(
        { discordUserId: discordId },
        { 
          $set: { 
            "notifications.$[elem].read": true,
            "notifications.$[elem].readAt": new Date().toISOString()
          } 
        },
        {
          arrayFilters: [{ "elem.read": { $ne: true } }] // Only update notifications that aren't already read
        }
      );
      
      await client.close();
      
      if (result.matchedCount === 0) {
        console.log(`Mark all notifications as read API: No user found with ID ${discordId.slice(0, 6)}`);
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      
      console.log(`Mark all notifications as read API: Successfully marked ${result.modifiedCount} notifications as read`);
      
      return NextResponse.json({
        success: true,
        modifiedCount: result.modifiedCount
      });
    } catch (dbError) {
      console.error('Database error in mark all notifications as read API:', dbError);
      return NextResponse.json({ 
        error: dbError instanceof Error ? dbError.message : 'Database error occurred' 
      }, { status: 500 });
    } finally {
      try {
        await client.close();
      } catch (e) {
        console.error('Error closing MongoDB connection:', e);
      }
    }
  } catch (error) {
    console.error('Error in mark all notifications as read API:', error);
    return NextResponse.json({ 
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
} 