'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { FaHome, FaSearch, FaExclamationTriangle, FaArrowLeft, FaQuestion, FaHandshake, FaBox, FaEnvelope, FaUsers, FaCode, FaTerminal, FaUserCog } from 'react-icons/fa';
import { motion } from 'framer-motion';

export default function NotFound() {
  const [searchTerm, setSearchTerm] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  
  const commonPages = [
    { name: 'Forside', path: '/' },
    { name: 'Produkter', path: '/products' },
    { name: '<PERSON>bestillinger', path: '/custom-orders' },
    { name: 'Om Os', path: '/om-os' },
    { name: 'Kontakt', path: '/kontakt' },
    { name: 'Freelancers', path: '/developers' },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON> og Betingelser', path: '/tos' },
    { name: 'Privatlivspolitik', path: '/privatlivspolitik' },
    { name: '<PERSON><PERSON>', path: '/partners' }
  ];

  const popularPages = [
    { name: 'Forside', path: '/' },
    { name: 'Produkter', path: '/products' },
    { name: 'Specialbestillinger', path: '/custom-orders' },
    { name: 'Om Os', path: '/om-os' },
    { name: 'Kontakt', path: '/kontakt' },
    { name: 'Udviklere', path: '/developers' },
  ];

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setSuggestions([]);
      return;
    }
    
    const filtered = commonPages
      .filter(page => 
        page.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        page.path.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .map(page => page.name);
      
    setSuggestions(filtered);
  }, [searchTerm]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-16">
      <div className="container mx-auto px-4">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-100"
        >
          {/* Error Header */}
          <div className="bg-gradient-to-br from-blue-800 to-indigo-900 py-16 px-8 text-white text-center relative overflow-hidden">
            <div className="absolute inset-0 bg-[url('/images/pattern.svg')] opacity-10"></div>
            <motion.div 
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: 'spring' }}
              className="relative mb-6 mx-auto w-28 h-28 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center"
            >
              <span className="text-6xl font-bold">404</span>
              <div className="absolute -right-2 -top-2 w-10 h-10 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
                <FaExclamationTriangle className="text-white text-lg" />
              </div>
            </motion.div>
            <h1 className="text-4xl md:text-5xl font-bold mb-3">Side ikke fundet</h1>
            <p className="text-blue-200 text-xl max-w-2xl mx-auto">
              Vi kunne ikke finde den side, du ledte efter.
            </p>
          </div>
          
          {/* Main Content */}
          <div className="p-8 md:p-12 space-y-12">
            {/* Search Section */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Lad os hjælpe dig med to finde vej</h2>
              
              {/* Search Box */}
              <div className="relative mb-4">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FaSearch className="text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Søg efter en side..."
                  className="w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-gray-50"
                />
              </div>
              
              {/* Search Suggestions */}
              {suggestions.length > 0 && (
                <motion.div 
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6"
                >
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Forslag:</h3>
                  <ul className="space-y-1">
                    {suggestions.map((suggestion, index) => {
                      const page = commonPages.find(p => p.name === suggestion);
                      return (
                        <motion.li 
                          key={index}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <Link 
                            href={page?.path || '/'} 
                            className="block px-3 py-2 rounded-md text-gray-800 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                          >
                            {suggestion}
                          </Link>
                        </motion.li>
                      );
                    })}
                  </ul>
                </motion.div>
              )}
              
              {/* Common Destinations */}
              <div className="mt-10">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Populære destinationer</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {popularPages.map((page, index) => (
                    <motion.div
                      key={index}
                      whileHover={{ y: -5 }}
                      transition={{ type: 'spring', stiffness: 300 }}
                    >
                      <Link
                        href={page.path}
                        className="bg-white p-5 rounded-xl border border-gray-100 hover:border-blue-200 hover:shadow-lg transition-all flex items-center group"
                      >
                        <div className="w-12 h-12 rounded-lg bg-blue-50 text-blue-600 flex items-center justify-center mr-4 group-hover:bg-blue-600 group-hover:text-white transition-all">
                          {index === 0 && <FaHome size={20} />}
                          {index === 1 && <FaBox size={20} />}
                          {index === 2 && <FaArrowLeft size={20} />}
                          {index === 3 && <FaUsers size={20} />}
                          {index === 4 && <FaEnvelope size={20} />}
                          {index === 5 && <FaUserCog size={20} />}
                        </div>
                        <span className="font-medium text-gray-800">{page.name}</span>
                      </Link>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Error Details */}
            <div className="bg-gray-50 rounded-xl p-6 border border-gray-100">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <FaExclamationTriangle className="text-amber-500 mr-2" />
                Hvad kan være gået galt?
              </h3>
              <ul className="text-gray-600 space-y-2 ml-6 list-disc">
                <li>URL'en blev tastet forkert</li>
                <li>Siden er blevet flyttet eller omdøbt</li>
                <li>Siden eksisterer ikke længere</li>
                <li>Du klikkede på et forældet link</li>
              </ul>
              
              <div className="mt-8 text-center">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Link 
                    href="/"
                    className="inline-flex items-center px-8 py-4 rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 transition-all font-medium shadow-lg hover:shadow-xl"
                  >
                    <motion.div
                      animate={{ rotate: [0, 10, -10, 0] }}
                      transition={{ duration: 1, repeat: Infinity }}
                    >
                      <FaHome className="mr-2" />
                    </motion.div>
                    Gå til forsiden
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
        
        {/* Support Contact */}
        <div className="mt-10 text-center text-gray-600">
          <p>Har du brug for hjælp? <Link href="/kontakt" className="text-blue-600 hover:underline font-medium">Kontakt vores support</Link></p>
        </div>
      </div>
    </div>
  );
}