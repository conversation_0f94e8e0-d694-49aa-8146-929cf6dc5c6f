import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { verifyAdminToken } from '@/lib/auth';
import { v4 as uuidv4 } from 'uuid';
import { ObjectId } from 'mongodb';

export async function POST(request) {
  try {
    // Verify admin user
    const admin = await verifyAdminToken();
    
    if (!admin) {
      return NextResponse.json(
        { message: 'Ikke autoriseret' },
        { status: 401 }
      );
    }
    
    // Only MyckasP can generate invite links
    if (admin.username !== 'MyckasP') {
      return NextResponse.json(
        { message: 'Du har ikke tilladelse til at generere invitationslinks' },
        { status: 403 }
      );
    }
    
    // Get request body
    const body = await request.json();
    const { admintype, allowedcases, discordUserId, description } = body;
    
    if (!admintype || !allowedcases || !discordUserId) {
      return NextResponse.json(
        { message: '<PERSON>gle<PERSON> påkrævede felter' },
        { status: 400 }
      );
    }
    
    // Connect to database
    const { db } = await connectToDatabase();
    
    // Generate a unique token for the invitation
    const token = uuidv4();
    
    // Store invitation in database
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 10); // Expires in 10 minutes
    
    await db.collection('invitations').insertOne({
      token,
      admintype,
      allowedcases,
      discordUserId,
      description: description || '',
      createdBy: admin.username,
      createdAt: new Date(),
      expiresAt,
      used: false
    });
    
    // Generate invitation URL using the request's origin
    const origin = request.headers.get('origin');
    const inviteUrl = `${origin}/admin/signup/${token}`;
    
    return NextResponse.json({ inviteUrl });
  } catch (error) {
    console.error('Error generating invite:', error);
    return NextResponse.json(
      { message: 'Der opstod en fejl ved generering af invitationslink' },
      { status: 500 }
    );
  }
} 