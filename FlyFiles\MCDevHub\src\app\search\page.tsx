'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Fa<PERSON><PERSON>ch, FaUser, FaArrowLeft } from 'react-icons/fa';
import UserSearch from '@/components/UserSearch';

export default function SearchPage() {
  const [selectedUser, setSelectedUser] = useState<any>(null);

  return (
    <div className="min-h-screen bg-gray-50 pt-20 sm:pt-28 pb-12 sm:pb-20">
      <div className="container mx-auto px-3 sm:px-4">
        {/* Back Link */}
        <div className="max-w-4xl mx-auto mb-4 sm:mb-6">
          <Link href="/" className="inline-flex items-center text-blue-600 hover:text-blue-800 group">
            <FaArrowLeft className="mr-2 transition-all duration-300 ease-in-out group-hover:-translate-x-1 group-hover:scale-110" />
            <span className="text-sm sm:text-base">Tilbage til forsiden</span>
          </Link>
        </div>

        {/* Search Header */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaSearch className="text-blue-600 text-2xl" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Søg efter brugere</h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Find og se profiler for andre brugere på MCDevHub. Søg efter Discord brugernavn eller Discord ID for at se deres profil på /profile/brugernavn.
            </p>
          </div>
        </div>

        {/* Search Section */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 sm:p-8">
            <div className="mb-6">
              <label htmlFor="user-search" className="block text-sm font-medium text-gray-700 mb-2">
                Søg efter brugere
              </label>
              <UserSearch
                placeholder="Indtast Discord brugernavn eller Discord ID..."
                className="w-full"
                onUserSelect={setSelectedUser}
              />
            </div>

            {/* Search Tips */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-blue-800 mb-2">Søgetips:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Søg efter Discord brugernavn (f.eks. "MyckasP")</li>
                <li>• Søg efter Discord ID (f.eks. "123456789012345678")</li>
                <li>• Søgningen finder brugere fra Discord accounts</li>
                <li>• Søgningen er ikke følsom over for store/små bogstaver</li>
                <li>• Du skal indtaste mindst 2 tegn for at starte søgningen</li>
              </ul>
            </div>

            {/* Selected User Preview */}
            {selectedUser && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Valgt bruger:</h3>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <FaUser className="text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{selectedUser.username}</p>
                      <p className="text-sm text-gray-500">
                        {selectedUser.isFreelancer ? 'Freelancer' : 'Bruger'}
                        {selectedUser.isVerified && ' • Verificeret'}
                      </p>
                    </div>
                  </div>
                  <Link
                    href={selectedUser.isFreelancer ? `/developers/${selectedUser.username}` : `/users/${selectedUser.discordUserId}`}
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md hover:bg-blue-700"
                  >
                    Se profil
                  </Link>
                </div>
              </div>
            )}
          </div>

          {/* Popular Users Section */}
          <div className="mt-8 bg-white rounded-xl shadow-lg border border-gray-100 p-6 sm:p-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Populære udviklere</h2>
            <p className="text-gray-600 mb-6">
              Udforsk profiler for nogle af vores mest aktive freelancere og udviklere.
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <Link href="/developers" className="block p-4 bg-gray-50 rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <FaUser className="text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Se alle freelancere</p>
                    <p className="text-sm text-gray-500">Udforsk alle verificerede udviklere</p>
                  </div>
                </div>
              </Link>
            </div>
          </div>

          {/* Privacy Notice */}
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">Privatlivsbeskyttelse</h3>
            <p className="text-sm text-yellow-700">
              Kun offentlige profiloplysninger vises til andre brugere. Private oplysninger som notifikationer 
              og personlige indstillinger forbliver kun synlige for profilindehaveren.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
