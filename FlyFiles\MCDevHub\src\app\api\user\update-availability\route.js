import { NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';

export const runtime = 'nodejs'; // Use Node.js runtime

export async function POST(request) {
  try {
    // Get the user's Discord ID from their NextAuth session
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Du er ikke logget ind' },
        { status: 401 }
      );
    }

    // Get Discord ID from session
    const discordUserId = session.user.id;

    console.log('POST /api/user/update-availability - Discord ID:', discordUserId);
    console.log('POST /api/user/update-availability - Session user:', JSON.stringify(session.user, null, 2));

    if (!discordUserId) {
      return NextResponse.json(
        { success: false, message: 'Discord ID ikke fundet' },
        { status: 400 }
      );
    }

    // Parse request body
    const { openForTasks } = await request.json();

    console.log('POST /api/user/update-availability - openForTasks:', openForTasks);

    if (typeof openForTasks !== 'boolean') {
      return NextResponse.json(
        { success: false, message: 'Ugyldig tilgængeligheds status' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    const { db } = await connectToDatabase();

    // Try multiple ways to find the user (similar to check-admin endpoint)
    let existingUser = await db.collection('adminusers').findOne({ discordUserId });
    let queryFilter = { discordUserId };

    if (!existingUser) {
      console.log('POST /api/user/update-availability - Direct match failed, trying string comparison');
      existingUser = await db.collection('adminusers').findOne({ discordUserId: discordUserId.toString() });
      queryFilter = { discordUserId: discordUserId.toString() };
    }

    if (!existingUser) {
      console.log('POST /api/user/update-availability - String match failed, trying number comparison');
      existingUser = await db.collection('adminusers').findOne({ discordUserId: parseInt(discordUserId) });
      queryFilter = { discordUserId: parseInt(discordUserId) };
    }

    console.log('POST /api/user/update-availability - Existing user found:', existingUser ? 'Yes' : 'No');
    if (existingUser) {
      console.log('POST /api/user/update-availability - Existing user data:', JSON.stringify(existingUser, null, 2));
    }

    if (!existingUser) {
      return NextResponse.json(
        { success: false, message: 'Ingen admin bruger fundet med dette Discord ID' },
        { status: 404 }
      );
    }

    // Update the admin user's availability status using the correct query filter
    const result = await db.collection('adminusers').updateOne(
      queryFilter,
      {
        $set: {
          openForTasks,
          updatedAt: new Date()
        }
      },
      { upsert: false } // Don't create if user doesn't exist
    );

    console.log('POST /api/user/update-availability - Update result:', JSON.stringify(result, null, 2));
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Tilgængelighed opdateret',
      openForTasks
    });
    
  } catch (error) {
    console.error('Error updating availability by Discord ID:', error);
    
    // Return error response
    return NextResponse.json(
      { success: false, message: 'Der opstod en fejl ved opdatering af tilgængelighed' },
      { status: 500 }
    );
  }
}

export async function GET(request) {
  try {
    // Get the user's Discord ID from their NextAuth session
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Du er ikke logget ind' },
        { status: 401 }
      );
    }

    // Get Discord ID from session
    const discordUserId = session.user.id;

    console.log('GET /api/user/update-availability - Discord ID:', discordUserId);
    console.log('GET /api/user/update-availability - Session user:', JSON.stringify(session.user, null, 2));

    if (!discordUserId) {
      return NextResponse.json(
        { success: false, message: 'Discord ID ikke fundet' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    const { db } = await connectToDatabase();

    // Try multiple ways to find the user (similar to check-admin endpoint)
    let user = await db.collection('adminusers').findOne(
      { discordUserId },
      { projection: { openForTasks: 1, username: 1, admintype: 1 } }
    );

    if (!user) {
      console.log('GET /api/user/update-availability - Direct match failed, trying string comparison');
      user = await db.collection('adminusers').findOne(
        { discordUserId: discordUserId.toString() },
        { projection: { openForTasks: 1, username: 1, admintype: 1 } }
      );
    }

    if (!user) {
      console.log('GET /api/user/update-availability - String match failed, trying number comparison');
      user = await db.collection('adminusers').findOne(
        { discordUserId: parseInt(discordUserId) },
        { projection: { openForTasks: 1, username: 1, admintype: 1 } }
      );
    }

    console.log('GET /api/user/update-availability - User found:', user ? 'Yes' : 'No');
    if (user) {
      console.log('GET /api/user/update-availability - User data:', JSON.stringify(user, null, 2));
    }

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Ingen admin bruger fundet med dette Discord ID' },
        { status: 404 }
      );
    }
    
    // Return the availability status (default to false if not set)
    return NextResponse.json({
      success: true,
      openForTasks: user.openForTasks || false
    });
    
  } catch (error) {
    console.error('Error fetching availability by Discord ID:', error);
    
    // Return error response
    return NextResponse.json(
      { success: false, message: 'Der opstod en fejl ved hentning af tilgængelighed' },
      { status: 500 }
    );
  }
}
