import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { FaTimes, FaExclamationTriangle, FaCheckCircle, FaPaypal } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';

interface CashoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  balance: number;
}

export default function CashoutModal({ isOpen, onClose, balance }: CashoutModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [withdrawalAmount, setWithdrawalAmount] = useState<string>('');
  const [selectedMethod, setSelectedMethod] = useState<'paypal' | 'mobilepay' | null>(null);
  const { user } = useAuth();
  
  // Reset the form when modal opens
  useEffect(() => {
    if (isOpen) {
      setWithdrawalAmount('');
      setSelectedMethod(null);
      setErrorMessage(null);
    }
  }, [isOpen]);
  
  // Prevent scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      // Store original body style
      const originalStyle = window.getComputedStyle(document.body).overflow;
      // Disable scrolling
      document.body.style.overflow = 'hidden';
      // Add padding to prevent layout shift when scrollbar disappears
      document.documentElement.style.paddingRight = `${window.innerWidth - document.documentElement.clientWidth}px`;
      
      // Cleanup function to restore original scroll behavior
      return () => {
        document.body.style.overflow = originalStyle;
        document.documentElement.style.paddingRight = '0';
      };
    }
  }, [isOpen]);

  // Validate the amount input
  const isValidAmount = () => {
    const amount = parseFloat(withdrawalAmount);
    return !isNaN(amount) && amount >= 50 && amount <= balance;
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow numbers and a single decimal point
    const value = e.target.value;
    if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
      setWithdrawalAmount(value);
    }
  };

  const handleMethodSelection = (method: 'paypal' | 'mobilepay') => {
    setSelectedMethod(method);
  };

  const handleSubmit = async () => {
    try {
      // Check if user is logged in
      if (!user) {
        setErrorMessage('Du skal være logget ind for at foretage en udbetaling');
        return;
      }
      
      // Validate the withdrawal amount
      if (!isValidAmount()) {
        setErrorMessage('Ugyldigt beløb. Beløbet skal være mindst 50 DKK og må ikke overstige din balance.');
        return;
      }
      
      // Validate the payment method
      if (!selectedMethod) {
        setErrorMessage('Vælg venligst en udbetalingsmetode');
        return;
      }
      
      setIsProcessing(true);
      setErrorMessage(null);
      
      // Simulate API call (this would be replaced with actual API call in a real implementation)
      setTimeout(() => {
        // For demonstration purposes, always show success for MobilePay
        if (selectedMethod === 'mobilepay') {
          // Here we would normally call the backend to process the withdrawal
          console.log(`Processing withdrawal: ${withdrawalAmount} DKK via ${selectedMethod}`);
          
          // Close the modal after successful submission
          onClose();
          
          // Show success notification (this could be improved with a proper notification system)
          alert(`Din anmodning om udbetaling af ${withdrawalAmount} DKK via MobilePay er blevet registreret. Den vil blive behandlet inden for 1-3 hverdage.`);
        } else {
          // Show error for PayPal as it's marked as unavailable
          setErrorMessage('PayPal er desværre ikke tilgængelig som udbetalingsmetode på nuværende tidspunkt.');
        }
        
        setIsProcessing(false);
      }, 1500);
    } catch (error) {
      console.error('Withdrawal error:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Der opstod en fejl ved udbetalingen. Prøv igen senere.');
      setIsProcessing(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop with blur effect */}
          <motion.div 
            className="fixed inset-0 bg-black/50 backdrop-blur-sm" 
            onClick={onClose}
            aria-hidden="true"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          />
          
          {/* Modal content */}
          <motion.div 
            className="relative bg-white w-full max-w-md mx-auto rounded-xl shadow-2xl p-6 z-10"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            {/* Close button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Luk"
              disabled={isProcessing}
            >
              <FaTimes size={20} />
            </button>
            
            {/* Header */}
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">Udbetal din balance</h3>
              <p className="text-gray-600 mt-1">
                Tilgængelig balance: {balance.toLocaleString('da-DK')} DKK
              </p>
            </div>
            
            {/* Error message */}
            {errorMessage && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-700 flex items-start">
                <FaExclamationTriangle className="flex-shrink-0 w-5 h-5 mr-2 mt-0.5 text-red-500" />
                <div>
                  <p className="font-medium">Der opstod en fejl</p>
                  <p>{errorMessage}</p>
                </div>
              </div>
            )}
            
            {/* Amount input */}
            <div className="mb-4">
              <label htmlFor="withdrawal-amount" className="block text-sm font-medium text-gray-700 mb-1">
                Beløb (min. 50 DKK)
              </label>
              <div className="relative rounded-md shadow-sm">
                <input
                  type="text"
                  name="withdrawal-amount"
                  id="withdrawal-amount"
                  className={`block w-full pl-3 pr-12 py-3 border ${
                    errorMessage ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'
                  } rounded-md placeholder-gray-400 text-lg font-medium`}
                  placeholder="0.00"
                  value={withdrawalAmount}
                  onChange={handleAmountChange}
                  disabled={isProcessing}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <span className="text-gray-500 sm:text-lg">DKK</span>
                </div>
              </div>
              <p className="mt-1 text-sm text-gray-500">
                Indtast det beløb, du ønsker at udbetale
              </p>
            </div>
            
            {/* Payment method selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Vælg udbetalingsmetode
              </label>
              
              {/* PayPal option (unavailable) */}
              <button
                type="button"
                onClick={() => handleMethodSelection('paypal')}
                disabled={true}
                className="relative w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg mb-3 opacity-60 cursor-default"
              >
                <div className="flex items-center">
                  <div className="w-6 h-6 mr-3 relative">
                    <Image
                      src="https://upload.wikimedia.org/wikipedia/commons/b/b7/PayPal_Logo_Icon_2014.svg"
                      alt="PayPal"
                      fill
                      style={{ objectFit: 'contain' }}
                    />
                  </div>
                  <div>
                    <span className="font-medium text-gray-800">PayPal</span>
                    <span className="absolute top-0 right-0 bg-gray-200 text-gray-700 text-xs font-bold px-2 py-1 m-2 rounded">
                      Utilgængelig
                    </span>
                  </div>
                </div>
              </button>
              
              {/* MobilePay option */}
              <button
                type="button"
                onClick={() => handleMethodSelection('mobilepay')}
                disabled={isProcessing}
                className={`w-full flex items-center justify-between p-4 border rounded-lg transition-all hover:border-indigo-500 hover:shadow-md ${
                  selectedMethod === 'mobilepay' 
                    ? 'border-indigo-500 bg-indigo-50 shadow-md' 
                    : 'border-gray-200'
                }`}
              >
                <div className="flex items-center">
                  <div className="w-6 h-6 mr-3 relative">
                    <Image
                      src="https://developer.mobilepay.dk/img/logo-blue.svg"
                      alt="MobilePay"
                      fill
                      style={{ objectFit: 'contain' }}
                    />
                  </div>
                  <span className="font-medium text-gray-800">MobilePay</span>
                </div>
                {selectedMethod === 'mobilepay' && (
                  <FaCheckCircle className="h-5 w-5 text-indigo-600" />
                )}
              </button>
              
              <p className="mt-2 text-sm text-gray-500">
                Du vil modtage din udbetaling inden for 1-3 hverdage
              </p>
            </div>
            
            {/* Submit button */}
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isProcessing || !isValidAmount() || !selectedMethod}
              className={`w-full py-3 px-4 rounded-lg text-white font-medium transition-all ${
                isProcessing || !isValidAmount() || !selectedMethod
                  ? 'bg-gray-400 cursor-default'
                  : 'bg-indigo-600 hover:bg-indigo-700 shadow-md hover:shadow-lg'
              }`}
            >
              {isProcessing ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Behandler...
                </span>
              ) : (
                'Anmod om udbetaling'
              )}
            </button>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
} 