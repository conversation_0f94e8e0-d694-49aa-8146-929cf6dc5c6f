'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaLock, FaSpinner } from 'react-icons/fa';

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'universal' | 'freelancer' | 'regular';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  color: string;
  earned: boolean;
  earnedAt?: string;
  progress?: number;
  maxProgress?: number;
  progressPercentage?: number;
}

interface BadgeDisplayProps {
  discordUserId?: string;
  showProgress?: boolean;
  showUnearned?: boolean;
  maxDisplay?: number;
  className?: string;
}

const BadgeDisplay: React.FC<BadgeDisplayProps> = ({
  discordUserId,
  showProgress = false,
  showUnearned = false,
  maxDisplay,
  className = ''
}) => {
  const [badges, setBadges] = useState<Record<string, Badge>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBadges();
  }, [discordUserId, showProgress]);

  const fetchBadges = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (showProgress) {
        params.append('includeProgress', 'true');
      }

      // Use different endpoint based on whether we're viewing own or other user's badges
      const endpoint = discordUserId
        ? `/api/badges/user/${discordUserId}?${params.toString()}`
        : `/api/badges?${params.toString()}`;

      const response = await fetch(endpoint);

      if (!response.ok) {
        throw new Error('Failed to fetch badges');
      }

      const data = await response.json();
      setBadges(data.badges || {});
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const getRarityColor = (rarity: string): string => {
    switch (rarity) {
      case 'common': return 'border-gray-400 bg-gray-50';
      case 'rare': return 'border-blue-400 bg-blue-50';
      case 'epic': return 'border-purple-400 bg-purple-50';
      case 'legendary': return 'border-yellow-400 bg-yellow-50';
      default: return 'border-gray-400 bg-gray-50';
    }
  };

  const getRarityGlow = (rarity: string): string => {
    switch (rarity) {
      case 'rare': return 'shadow-blue-200';
      case 'epic': return 'shadow-purple-200';
      case 'legendary': return 'shadow-yellow-200';
      default: return '';
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <FaSpinner className="animate-spin text-blue-500 text-2xl" />
        <span className="ml-3 text-gray-600 text-base font-medium">Indlæser badges...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-red-500 p-6 bg-red-50 rounded-lg border border-red-200 ${className}`}>
        <div className="text-base font-semibold mb-1">Fejl ved indlæsning af badges</div>
        <div className="text-sm">{error}</div>
      </div>
    );
  }

  const badgeEntries = Object.entries(badges);
  const earnedBadges = badgeEntries.filter(([_, badge]) => badge.earned);
  const unearnedBadges = badgeEntries.filter(([_, badge]) => !badge.earned);

  let displayBadges = earnedBadges;
  if (showUnearned) {
    displayBadges = [...earnedBadges, ...unearnedBadges];
  }

  if (maxDisplay) {
    displayBadges = displayBadges.slice(0, maxDisplay);
  }

  if (displayBadges.length === 0) {
    return (
      <div className={`text-gray-500 p-8 text-center bg-gray-50 rounded-lg border border-gray-200 ${className}`}>
        <div className="text-base font-medium mb-2">
          {showUnearned ? 'Ingen badges tilgængelige' : 'Ingen badges optjent endnu'}
        </div>
        <div className="text-sm text-gray-400">
          {showUnearned ? 'Der er ingen badges at vise i øjeblikket.' : 'Fortsæt med at bruge platformen for at optjene dine første badges!'}
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {displayBadges.map(([badgeId, badge]) => (
          <div
            key={badgeId}
            className={`
              relative p-4 rounded-xl border-2 transition-all duration-300 hover:scale-105 hover:shadow-lg group
              ${badge.earned
                ? `${getRarityColor(badge.rarity)} ${getRarityGlow(badge.rarity)} shadow-md`
                : 'border-gray-200 bg-gray-50 opacity-70'
              }
            `}
          >
            {/* Badge Icon */}
            <div className="flex flex-col items-center text-center">
              <div className={`
                text-3xl mb-3 p-3 rounded-full flex-shrink-0 transition-all duration-300
                ${badge.earned ? 'bg-white shadow-md group-hover:shadow-lg' : 'bg-gray-200'}
              `}>
                {badge.earned ? badge.icon : <FaLock className="text-gray-400" />}
              </div>

              {/* Badge Name */}
              <h3 className={`font-bold text-sm mb-2 leading-tight text-center ${badge.earned ? 'text-gray-800' : 'text-gray-500'}`}>
                {badge.name}
              </h3>

              {/* Badge Category */}
              <p className={`text-xs font-medium mb-2 ${badge.earned ? 'text-gray-600' : 'text-gray-400'}`}>
                {badge.rarity === 'legendary' && '🌟 '}
                {badge.rarity === 'epic' && '💎 '}
                {badge.rarity === 'rare' && '⭐ '}
                {badge.category === 'freelancer' && 'Freelancer'}
                {badge.category === 'regular' && 'Bruger'}
                {badge.category === 'universal' && 'Universal'}
              </p>
            </div>

            {/* Badge Description - Hidden by default, shown on hover */}
            <div className="absolute inset-0 bg-white bg-opacity-95 rounded-xl p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-center items-center text-center z-10">
              <div className="text-2xl mb-2">{badge.earned ? badge.icon : <FaLock className="text-gray-400" />}</div>
              <h4 className="font-bold text-sm mb-2 text-gray-800">{badge.name}</h4>
              <p className="text-xs text-gray-600 mb-3 leading-relaxed">
                {badge.description}
              </p>

              {/* Progress Bar (if showing progress and not earned) */}
              {showProgress && !badge.earned && badge.maxProgress && (
                <div className="w-full mb-3">
                  <div className="flex justify-between text-xs text-gray-600 mb-1 font-medium">
                    <span>Fremskridt</span>
                    <span>{badge.progress || 0}/{badge.maxProgress}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${badge.progressPercentage || 0}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Earned Date */}
              {badge.earned && badge.earnedAt && (
                <div className="text-xs text-gray-500 font-medium bg-gray-100 px-2 py-1 rounded">
                  Optjent: {new Date(badge.earnedAt).toLocaleDateString('da-DK')}
                </div>
              )}
            </div>

            {/* Rarity Indicator */}
            {badge.earned && badge.rarity !== 'common' && (
              <div className="absolute top-2 right-2">
                <FaTrophy className={`
                  text-sm
                  ${badge.rarity === 'legendary' ? 'text-yellow-500 drop-shadow-lg' : ''}
                  ${badge.rarity === 'epic' ? 'text-purple-500 drop-shadow-lg' : ''}
                  ${badge.rarity === 'rare' ? 'text-blue-500 drop-shadow-lg' : ''}
                `} />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Summary */}
      {showProgress && (
        <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-xl border border-gray-200 shadow-sm">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="text-center sm:text-left">
              <h4 className="text-lg font-bold text-gray-800 mb-1">Badge Oversigt</h4>
              <span className="text-sm font-semibold text-gray-700">
                Optjente badges: {earnedBadges.length}/{badgeEntries.length}
              </span>
            </div>
            <div className="flex flex-wrap gap-2 text-sm font-medium">
              <span className="text-yellow-700 bg-yellow-100 px-3 py-1 rounded-full border border-yellow-200 shadow-sm">
                🌟 {earnedBadges.filter(([_, b]) => b.rarity === 'legendary').length}
              </span>
              <span className="text-purple-700 bg-purple-100 px-3 py-1 rounded-full border border-purple-200 shadow-sm">
                💎 {earnedBadges.filter(([_, b]) => b.rarity === 'epic').length}
              </span>
              <span className="text-blue-700 bg-blue-100 px-3 py-1 rounded-full border border-blue-200 shadow-sm">
                ⭐ {earnedBadges.filter(([_, b]) => b.rarity === 'rare').length}
              </span>
              <span className="text-gray-700 bg-gray-200 px-3 py-1 rounded-full border border-gray-300 shadow-sm">
                {earnedBadges.filter(([_, b]) => b.rarity === 'common').length}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BadgeDisplay;
