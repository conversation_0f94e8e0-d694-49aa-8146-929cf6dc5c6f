import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

export async function GET(request, { params }) {
  const { username } = await params;
  
  if (!username) {
    return NextResponse.json(
      { message: 'Manglende brugernavn parameter' },
      { status: 400 }
    );
  }
  
  try {
    // Connect to the database
    const { db } = await connectToDatabase();
    
    // Create a case-insensitive regular expression for the username
    const usernameRegex = new RegExp(`^${username}$`, 'i');
    
    // Find the freelancer using case-insensitive matching
    const freelancer = await db.collection('adminusers').findOne(
      { 
        username: usernameRegex,
        admintype: 'Freelancer'
      },
      { 
        projection: {
          username: 1,
          discordUserId: 1,
          avatar: 1,
          avatarUrl: 1,
          bannerImage: 1,
          description: 1,
          isVerified: 1,
          verifiedAt: 1,
          email: 1,
          youtubeUrl: 1,
          githubUsername: 1,
          createdAt: 1,
          openForTasks: 1
        }
      }
    );
    
    if (!freelancer) {
      return NextResponse.json(
        { message: 'Freelancer ikke fundet' },
        { status: 404 }
      );
    }
    
    // Count the freelancer's products - use the actual username from the DB for this
    // to ensure proper case matching with products collection
    const productCount = await db.collection('products').countDocuments({
      createdBy: freelancer.username
    });
    
    // Count active products separately
    const activeProductCount = await db.collection('products').countDocuments({
      createdBy: freelancer.username,
      status: 'active'
    });
    
    // Check verified collection for isVerified true
    let isVerified = freelancer.isVerified || false;
    if (freelancer.discordUserId) {
      const verifiedDoc = await db.collection('verified').findOne({
        discordId: freelancer.discordUserId,
        isVerified: true
      });
      isVerified = !!verifiedDoc;
    }
    
    // Add productCount to the freelancer object
    const freelancerWithProductCount = {
      ...freelancer,
      productCount,
      activeProductCount,
      isVerified
    };
    
    return NextResponse.json({
      freelancer: freelancerWithProductCount
    });
  } catch (error) {
    console.error('Error fetching freelancer:', error);
    return NextResponse.json(
      { message: 'Der opstod en fejl ved hentning af freelancer' },
      { status: 500 }
    );
  }
} 