'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  FaShieldAlt, 
  FaLock, 
  FaDiscord, 
  FaDatabase, 
  FaShare, 
  FaUserShield, 
  FaCookieBite, 
  FaHistory, 
  FaEnvelope,
  FaArrowRight, 
  FaChevronDown, 
  FaChevronUp, 
  FaClock, 
  FaArrowUp,
  FaCheck,
  FaCreditCard,
  FaShoppingCart,
  FaDesktop,
  FaInfoCircle,
  FaShoppingBag,
  FaHeadset,
  FaMoneyBill,
  FaChartLine,
  FaCircle,
  FaUser,
  FaCalendarAlt
} from 'react-icons/fa';

export default function PrivacyPage() {
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('');
  const [mobileTOCOpen, setMobileTOCOpen] = useState(false);

  // Function to get current date in Danish format
  function getCurrentDate() {
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return new Date().toLocaleDateString('da-DK', options);
  }

  useEffect(() => {
    // Function to handle scroll events
    const handleScroll = () => {
      // Show scroll to top button after scrolling down 500px
      setShowScrollToTop(window.scrollY > 500);
      
      // Update active section based on scroll position
      const sections = document.querySelectorAll('.section-container');
      let currentActiveSection = '';
      
      sections.forEach((section) => {
        const sectionTop = section.getBoundingClientRect().top;
        if (sectionTop < 200) {
          currentActiveSection = section.id;
        }
      });
      
      if (currentActiveSection !== activeSection) {
        setActiveSection(currentActiveSection);
      }
    };
    
    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);
    
    // Initial check for active section
    handleScroll();
    
    // Clean up event listener
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [activeSection]);
  
  // Function to scroll to a specific section
  const scrollToSection = (sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      window.scrollTo({
        top: section.offsetTop - 100,
        behavior: 'smooth'
      });
    }
  };
  
  // Function to scroll to top
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      <main className="pt-24 pb-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
          {/* Hero section */}
          <div className="mb-12">
            <div className="relative bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl overflow-hidden">
              <div className="absolute inset-0 opacity-10" style={{ backgroundImage: 'url(/privacy-pattern.svg)' }}></div>
              <div className="relative z-10 px-4 py-8 sm:px-12 sm:py-16 md:py-20 text-center md:text-left">
                <div className="max-w-4xl mx-auto">
                  <h1 className="text-2xl sm:text-4xl md:text-5xl font-bold text-white mb-4">Privatlivspolitik</h1>
                  <p className="text-indigo-100 text-base sm:text-xl md:max-w-3xl">
                    Vi tager beskyttelsen af dine personlige oplysninger alvorligt. Denne privatlivspolitik forklarer, 
                    hvordan vi indsamler, bruger og beskytter dine data.
                  </p>
                  <div className="mt-6 text-indigo-200 flex flex-col items-center gap-2 md:flex-row md:items-center md:justify-start">
                    <FaClock className="mr-2" />
                    <span>Senest opdateret: {getCurrentDate()}</span>
                  </div>
                </div>
              </div>
        </div>
          </div>
          
          <style jsx global>{`
            .section-container {
              background-color: white;
              border-radius: 1rem;
              box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
              padding: 1.25rem;
              margin-bottom: 1.5rem;
              border: 1px solid #f0f0f0;
              scroll-margin-top: 5rem;
            }
            @media (min-width: 640px) {
              .section-container {
                padding: 2rem;
                margin-bottom: 2rem;
                scroll-margin-top: 7rem;
              }
            }
          `}</style>

          {/* Mobile TOC - visible and sticky only on mobile */}
          <div className="block sm:hidden sticky top-16 z-40 px-4 mt-2">
            <button 
              onClick={() => setIsMobileNavOpen(!isMobileNavOpen)}
              className="w-full bg-white rounded-xl shadow-lg p-4 flex items-center justify-between border border-gray-100 transition-all duration-300 hover:shadow-xl"
            >
              <div className="flex items-center">
                <div className="bg-indigo-100 text-indigo-600 rounded-lg w-8 h-8 inline-flex items-center justify-center mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                  </svg>
                </div>
                <span className="font-bold text-gray-900">Indhold</span>
              </div>
              <div className="transition-all duration-125 ease-in-out transform origin-center" style={{ transform: isMobileNavOpen ? 'rotate(180deg)' : 'rotate(0deg)' }}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </button>
            
            {/* Mobile TOC content */}
            <div className={`bg-white rounded-xl shadow-xl mt-2 border border-gray-100 overflow-hidden transition-all duration-300 ${isMobileNavOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'}`}>
              <div className="p-4">
                <ul className="space-y-3">
                  <li>
                    <button 
                      onClick={() => {
                        scrollToSection('indsamling');
                        setIsMobileNavOpen(false);
                      }}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'indsamling' ? 'bg-indigo-50 text-indigo-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-500'}`}
                    >
                      Indsamling af oplysninger
                    </button>
                  </li>
                  <li>
                    <button 
                      onClick={() => {
                        scrollToSection('anvendelse');
                        setIsMobileNavOpen(false);
                      }}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'anvendelse' ? 'bg-indigo-50 text-indigo-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-500'}`}
                    >
                      Anvendelse af oplysninger
                    </button>
                  </li>
                  <li>
                    <button 
                      onClick={() => {
                        scrollToSection('discord');
                        setIsMobileNavOpen(false);
                      }}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'discord' ? 'bg-indigo-50 text-indigo-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-500'}`}
                    >
                      Discord-integration
                    </button>
                  </li>
                  <li>
                    <button 
                      onClick={() => {
                        scrollToSection('opbevaring');
                        setIsMobileNavOpen(false);
                      }}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'opbevaring' ? 'bg-indigo-50 text-indigo-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-500'}`}
                    >
                      Opbevaring af data
                    </button>
                  </li>
                  <li>
                    <button 
                      onClick={() => {
                        scrollToSection('deling');
                        setIsMobileNavOpen(false);
                      }}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'deling' ? 'bg-indigo-50 text-indigo-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-500'}`}
                    >
                      Deling af oplysninger
                    </button>
                  </li>
                  <li>
                    <button 
                      onClick={() => {
                        scrollToSection('rettigheder');
                        setIsMobileNavOpen(false);
                      }}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'rettigheder' ? 'bg-indigo-50 text-indigo-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-500'}`}
                    >
                      Dine rettigheder
                    </button>
                  </li>
                  <li>
                    <button 
                      onClick={() => {
                        scrollToSection('cookies');
                        setIsMobileNavOpen(false);
                      }}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'cookies' ? 'bg-indigo-50 text-indigo-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-500'}`}
                    >
                      Cookies
                    </button>
                  </li>
                  <li>
                    <button 
                      onClick={() => {
                        scrollToSection('aendringer');
                        setIsMobileNavOpen(false);
                      }}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'aendringer' ? 'bg-indigo-50 text-indigo-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-500'}`}
                    >
                      Ændringer
                    </button>
                  </li>
                  <li>
                    <button 
                      onClick={() => {
                        scrollToSection('kontakt');
                        setIsMobileNavOpen(false);
                      }}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 ${activeSection === 'kontakt' ? 'bg-indigo-50 text-indigo-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-500'}`}
                    >
                      Kontakt
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Privacy Policy Content */}
          <section className="py-16 px-4">
            <div className="max-w-6xl mx-auto">
              <div className="flex flex-col lg:flex-row gap-8">
                {/* Navigation sidebar for desktop */}
                <div className="lg:w-80 shrink-0 hidden md:block">
                  <div className="bg-white rounded-2xl shadow-xl p-6 sticky top-28 border border-gray-100 overflow-hidden group transition-all duration-300 hover:shadow-2xl">
                    <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 to-purple-600"></div>
                    <div className="absolute -right-20 -top-20 w-40 h-40 bg-indigo-50 rounded-full opacity-70 group-hover:bg-indigo-100 transition-all duration-500"></div>
                    <div className="relative">
                      <h3 className="font-bold text-xl text-gray-900 mb-6 hidden md:flex">
                        <span className="bg-indigo-100 text-indigo-600 rounded-lg w-8 h-8 inline-flex items-center justify-center mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                          </svg>
                        </span>
                        Indhold
                      </h3>
                      <ul className="space-y-5">
                        <li>
                          <button
                            onClick={() => scrollToSection('indsamling')}
                            className={`text-left w-full ${activeSection === 'indsamling' ? 'text-indigo-600' : 'text-gray-700 hover:text-indigo-600'} flex items-center group transition-all duration-300 ease-in-out`}
                          >
                            <span className={`${activeSection === 'indsamling' ? 'bg-indigo-100' : 'bg-indigo-50'} text-indigo-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-indigo-100 transition-all duration-300 ease-in-out shadow-sm`}>
                              <FaShieldAlt className="text-sm" />
                            </span>
                            <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Indsamling af oplysninger</span>
                          </button>
                        </li>
                        <li>
                          <button
                            onClick={() => scrollToSection('anvendelse')}
                            className={`text-left w-full ${activeSection === 'anvendelse' ? 'text-indigo-600' : 'text-gray-700 hover:text-indigo-600'} flex items-center group transition-all duration-300 ease-in-out`}
                          >
                            <span className={`${activeSection === 'anvendelse' ? 'bg-indigo-100' : 'bg-indigo-50'} text-indigo-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-indigo-100 transition-all duration-300 ease-in-out shadow-sm`}>
                              <FaLock className="text-sm" />
                            </span>
                            <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Anvendelse af oplysninger</span>
                          </button>
                        </li>
                        <li>
                          <button
                            onClick={() => scrollToSection('discord')}
                            className={`text-left w-full ${activeSection === 'discord' ? 'text-indigo-600' : 'text-gray-700 hover:text-indigo-600'} flex items-center group transition-all duration-300 ease-in-out`}
                          >
                            <span className={`${activeSection === 'discord' ? 'bg-indigo-100' : 'bg-indigo-50'} text-indigo-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-indigo-100 transition-all duration-300 ease-in-out shadow-sm`}>
                              <FaDiscord className="text-sm" />
                            </span>
                            <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Discord-integration</span>
                          </button>
                        </li>
                        <li>
                          <button
                            onClick={() => scrollToSection('opbevaring')}
                            className={`text-left w-full ${activeSection === 'opbevaring' ? 'text-indigo-600' : 'text-gray-700 hover:text-indigo-600'} flex items-center group transition-all duration-300 ease-in-out`}
                          >
                            <span className={`${activeSection === 'opbevaring' ? 'bg-indigo-100' : 'bg-indigo-50'} text-indigo-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-indigo-100 transition-all duration-300 ease-in-out shadow-sm`}>
                              <FaDatabase className="text-sm" />
                            </span>
                            <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Opbevaring af data</span>
                          </button>
                        </li>
                        <li>
                          <button
                            onClick={() => scrollToSection('deling')}
                            className={`text-left w-full ${activeSection === 'deling' ? 'text-indigo-600' : 'text-gray-700 hover:text-indigo-600'} flex items-center group transition-all duration-300 ease-in-out`}
                          >
                            <span className={`${activeSection === 'deling' ? 'bg-indigo-100' : 'bg-indigo-50'} text-indigo-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-indigo-100 transition-all duration-300 ease-in-out shadow-sm`}>
                              <FaShare className="text-sm" />
                            </span>
                            <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Deling af oplysninger</span>
                          </button>
                        </li>
                        <li>
                          <button
                            onClick={() => scrollToSection('rettigheder')}
                            className={`text-left w-full ${activeSection === 'rettigheder' ? 'text-indigo-600' : 'text-gray-700 hover:text-indigo-600'} flex items-center group transition-all duration-300 ease-in-out`}
                          >
                            <span className={`${activeSection === 'rettigheder' ? 'bg-indigo-100' : 'bg-indigo-50'} text-indigo-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-indigo-100 transition-all duration-300 ease-in-out shadow-sm`}>
                              <FaUserShield className="text-sm" />
                            </span>
                            <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Dine rettigheder</span>
                          </button>
                        </li>
                        <li>
                          <button
                            onClick={() => scrollToSection('cookies')}
                            className={`text-left w-full ${activeSection === 'cookies' ? 'text-indigo-600' : 'text-gray-700 hover:text-indigo-600'} flex items-center group transition-all duration-300 ease-in-out`}
                          >
                            <span className={`${activeSection === 'cookies' ? 'bg-indigo-100' : 'bg-indigo-50'} text-indigo-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-indigo-100 transition-all duration-300 ease-in-out shadow-sm`}>
                              <FaCookieBite className="text-sm" />
                            </span>
                            <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Cookies</span>
                          </button>
                        </li>
                        <li>
                          <button
                            onClick={() => scrollToSection('aendringer')}
                            className={`text-left w-full ${activeSection === 'aendringer' ? 'text-indigo-600' : 'text-gray-700 hover:text-indigo-600'} flex items-center group transition-all duration-300 ease-in-out`}
                          >
                            <span className={`${activeSection === 'aendringer' ? 'bg-indigo-100' : 'bg-indigo-50'} text-indigo-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-indigo-100 transition-all duration-300 ease-in-out shadow-sm`}>
                              <FaHistory className="text-sm" />
                            </span>
                            <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Ændringer</span>
                          </button>
                        </li>
                        <li>
                          <button
                            onClick={() => scrollToSection('kontakt')}
                            className={`text-left w-full ${activeSection === 'kontakt' ? 'text-indigo-600' : 'text-gray-700 hover:text-indigo-600'} flex items-center group transition-all duration-300 ease-in-out`}
                          >
                            <span className={`${activeSection === 'kontakt' ? 'bg-indigo-100' : 'bg-indigo-50'} text-indigo-600 rounded-lg w-10 h-10 inline-flex items-center justify-center mr-3 group-hover:bg-indigo-100 transition-all duration-300 ease-in-out shadow-sm`}>
                        <FaEnvelope className="text-sm" />
                      </span>
                            <span className="font-medium transform transition-all duration-300 ease-in-out group-hover:translate-x-1.5">Kontakt</span>
                          </button>
                  </li>
                </ul>
                
                <div className="mt-8 pt-6 border-t border-gray-200">
                        <Link href="/tos" className="group bg-gradient-to-r from-indigo-50 to-purple-50 hover:from-indigo-100 hover:to-purple-100 p-4 rounded-xl flex items-center transition-all duration-300 border border-indigo-100">
                          <div className="bg-white shadow-sm rounded-lg p-2 mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h16a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                    </svg>
                          </div>
                          <div className="flex-1">
                            <span className="text-sm text-indigo-700 font-medium">Se også vores</span>
                            <p className="text-indigo-900 font-semibold">Brugsvilkår</p>
                          </div>
                          <FaArrowRight className="text-indigo-400 group-hover:text-indigo-600 group-hover:translate-x-1 transition-all duration-200" />
                  </Link>
                      </div>
                </div>
              </div>
            </div>
            
            {/* Main content */}
            <div className="flex-1">
                  {/* Scroll to top button */}
                  {showScrollToTop && (
                    <button 
                      onClick={scrollToTop}
                      className="fixed bottom-6 right-6 bg-indigo-600 text-white rounded-full p-3 shadow-lg hover:bg-indigo-700 transition-all duration-200 z-50 animate-fadeIn"
                      aria-label="Scroll to top"
                    >
                      <FaArrowUp />
                    </button>
                  )}
                  
                  {/* Introduction */}
                  <div id="indsamling" className="section-container">
                    <div className="relative bg-gradient-to-r from-indigo-50 to-purple-50 p-5 sm:p-8 rounded-2xl mb-8 overflow-hidden">
                      <div className="absolute inset-0 opacity-10" style={{ backgroundImage: 'url(/privacy-pattern.svg)' }}></div>
                      <div className="relative flex flex-col md:flex-row md:items-center gap-6">
                        <div className="bg-indigo-500 text-white rounded-2xl p-4 md:p-5 shadow-md flex-shrink-0">
                          <FaShieldAlt className="text-3xl" />
                </div>
                        <div>
                          <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3">Indsamling af personlige oplysninger</h2>
                          <p className="text-gray-700">Vi indsamler personlige oplysninger, når du:</p>
                          <ul className="mt-3 space-y-2">
                    <li className="flex items-start">
                              <div className="bg-white text-indigo-500 rounded-lg p-1 mr-3 mt-0.5">
                                <FaCheck className="text-xs" />
                              </div>
                              <span className="text-gray-700">Opretter en konto på vores platform</span>
                    </li>
                    <li className="flex items-start">
                              <div className="bg-white text-indigo-500 rounded-lg p-1 mr-3 mt-0.5">
                                <FaCheck className="text-xs" />
                              </div>
                              <span className="text-gray-700">Foretager et køb gennem vores tjeneste</span>
                    </li>
                    <li className="flex items-start">
                              <div className="bg-white text-indigo-500 rounded-lg p-1 mr-3 mt-0.5">
                                <FaCheck className="text-xs" />
                              </div>
                              <span className="text-gray-700">Kontakter os for support eller anden assistance</span>
                    </li>
                    <li className="flex items-start">
                              <div className="bg-white text-indigo-500 rounded-lg p-1 mr-3 mt-0.5">
                                <FaCheck className="text-xs" />
                              </div>
                              <span className="text-gray-700">Tilmelder dig vores nyhedsbrev eller andre kommunikationstjenester</span>
                    </li>
                  </ul>
                  </div>
                </div>
              </div>

                    <p className="text-gray-700 mb-4">
                      De personlige oplysninger, vi indsamler, kan omfatte, men er ikke begrænset til:
                    </p>
                    
                    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
                      <ul className="space-y-4">
                        <li className="flex items-start">
                          <div className="bg-indigo-100 text-indigo-600 rounded-lg p-2 mr-4">
                            <FaUser className="text-sm" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900">Personlige identifikatorer</span>
                            <p className="text-gray-600 text-sm mt-1">Dit navn, email-adresse, faktureringsadresse, og Discord-brugernavn</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="bg-indigo-100 text-indigo-600 rounded-lg p-2 mr-4">
                            <FaCreditCard className="text-sm" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900">Betalingsinformation</span>
                            <p className="text-gray-600 text-sm mt-1">Kreditkortoplysninger og anden betalingsinformation (bemærk at selve kreditkortdata behandles sikkert af vores betalingstjenesteudbydere)</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="bg-indigo-100 text-indigo-600 rounded-lg p-2 mr-4">
                            <FaShoppingCart className="text-sm" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900">Transaktionsdata</span>
                            <p className="text-gray-600 text-sm mt-1">Information om produkter og tjenester, du har købt eller vist interesse for</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="bg-indigo-100 text-indigo-600 rounded-lg p-2 mr-4">
                            <FaDesktop className="text-sm" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900">Tekniske data</span>
                            <p className="text-gray-600 text-sm mt-1">IP-adresse, browser-type, enheds-type, og andre tekniske identifikatorer</p>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                  
                  {/* Anvendelse af oplysninger */}
                  <div id="anvendelse" className="section-container">
                <div className="flex items-center mb-6">
                      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl p-3 mr-4 shadow-md">
                    <FaLock className="text-xl" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">Anvendelse af oplysninger</h2>
                </div>
                    
                    <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 mb-6">
                      <p className="text-gray-700 mb-4">
                        Vi anvender de indsamlede oplysninger til forskellige formål, herunder:
                      </p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-300 border border-indigo-100">
                          <div className="flex items-center mb-3">
                            <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-3">
                              <FaShoppingBag className="text-sm" />
                            </div>
                            <h3 className="font-semibold text-gray-900">Produktlevering</h3>
                          </div>
                          <p className="text-gray-600 text-sm">
                            Levering af de produkter og tjenester du har købt, herunder digital kode-adgang
                          </p>
                        </div>
                        
                        <div className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-300 border border-indigo-100">
                          <div className="flex items-center mb-3">
                            <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-3">
                              <FaHeadset className="text-sm" />
                            </div>
                            <h3 className="font-semibold text-gray-900">Kundesupport</h3>
                          </div>
                          <p className="text-gray-600 text-sm">
                            Yde teknisk support og besvare dine forespørgsler om vores produkter og tjenester
                          </p>
                        </div>
                        
                        <div className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-300 border border-indigo-100">
                          <div className="flex items-center mb-3">
                            <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-3">
                              <FaMoneyBill className="text-sm" />
                            </div>
                            <h3 className="font-semibold text-gray-900">Fakturering</h3>
                          </div>
                          <p className="text-gray-600 text-sm">
                            Behandling af betalinger, fakturering, og andre finansielle transaktioner
                          </p>
                        </div>
                        
                        <div className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-300 border border-indigo-100">
                          <div className="flex items-center mb-3">
                            <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-3">
                              <FaChartLine className="text-sm" />
                            </div>
                            <h3 className="font-semibold text-gray-900">Tjenesten forbedring</h3>
                          </div>
                          <p className="text-gray-600 text-sm">
                            Forbedre og optimere vores hjemmeside, produkter, og tjenester baseret på brugeradfærd
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
                      <div className="flex items-center mb-4">
                        <div className="bg-yellow-100 text-yellow-600 p-2 rounded-lg mr-3">
                          <FaInfoCircle />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900">Lovligt grundlag for behandling</h3>
                      </div>
                      <p className="text-gray-700 mb-3">
                        Vi behandler kun dine personlige oplysninger, når vi har et lovligt grundlag for at gøre det. Vores lovlige grundlag inkluderer:
                      </p>
                      <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                          <div className="text-indigo-500 mr-2 mt-1">
                            <FaCircle className="text-xs" />
                          </div>
                          <span><strong>Opfyldelse af kontrakt:</strong> Når vi har brug for dine oplysninger for at opfylde vores kontraktlige forpligtelser over for dig</span>
                    </li>
                    <li className="flex items-start">
                          <div className="text-indigo-500 mr-2 mt-1">
                            <FaCircle className="text-xs" />
                          </div>
                          <span><strong>Legitime interesser:</strong> Når det er i vores legitime interesser at behandle dine oplysninger for at drive vores virksomhed</span>
                    </li>
                    <li className="flex items-start">
                          <div className="text-indigo-500 mr-2 mt-1">
                            <FaCircle className="text-xs" />
                          </div>
                          <span><strong>Samtykke:</strong> Når du har givet os dit samtykke til at behandle dine oplysninger til specifikke formål</span>
                    </li>
                    <li className="flex items-start">
                          <div className="text-indigo-500 mr-2 mt-1">
                            <FaCircle className="text-xs" />
                          </div>
                          <span><strong>Juridiske forpligtelser:</strong> Når vi er forpligtet til at behandle dine oplysninger for at overholde lovgivningen</span>
                    </li>
                  </ul>
                </div>
              </div>

                  {/* Discord-integration section */}
                  <div id="discord" className="section-container">
                <div className="flex items-center mb-6">
                      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl p-3 mr-4 shadow-md">
                    <FaDiscord className="text-xl" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">Discord-integration</h2>
                </div>
                    
                    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
                      <div className="relative p-6">
                        <div className="absolute top-0 right-0 w-40 h-40 opacity-5">
                          <svg viewBox="0 0 800 800" xmlns="http://www.w3.org/2000/svg">
                            <path d="M678.27 438.89c-27.23-21.49-56.2-36.97-86.78-46.54 3.98-5.05 7.86-10.69 11.42-16.65 16.48-27.55 30.65-61.5 40.42-103.41 9.76-41.91 14.9-91.41 14.9-151.31H558.63c-1.15 53.29-5.62 97.05-13.43 131.28-7.8 34.23-18.62 60.27-32.46 78.12-13.84 17.85-30.95 26.77-51.34 26.77-20.38 0-37.5-8.92-51.34-26.77-13.84-17.85-24.66-43.89-32.46-78.12-7.8-34.23-12.28-78-13.43-131.28H265.57c0 59.9 5.14 109.4 14.9 151.31 9.76 41.91 23.94 75.86 40.42 103.41 3.56 5.95 7.44 11.6 11.42 16.65-30.58 9.57-59.54 25.05-86.78 46.54-64.38 50.82-106.81 125.01-114.7 207.27h661.13c-7.89-82.26-50.31-156.45-114.7-207.27z" fill="currentColor"/>
                          </svg>
                        </div>
                        <div className="relative z-10">
                          <p className="text-gray-700 mb-4">
                            Vi bruger Discord som vores primære kommunikationsplatform for at yde support og levere produkter. Når du:
                          </p>
                          <ul className="space-y-3 mb-5">
                    <li className="flex items-start">
                              <div className="bg-indigo-100 text-indigo-600 rounded-lg p-1 mr-3 mt-0.5 flex-shrink-0">
                                <FaCheck className="text-xs" />
                              </div>
                              <span className="text-gray-700">Køber et produkt fra os, beder vi om dit Discord-brugernavn</span>
                    </li>
                    <li className="flex items-start">
                              <div className="bg-indigo-100 text-indigo-600 rounded-lg p-1 mr-3 mt-0.5 flex-shrink-0">
                                <FaCheck className="text-xs" />
                              </div>
                              <span className="text-gray-700">Kontakter os for support, vil du typisk dele dit Discord-brugernavn med os</span>
                    </li>
                    <li className="flex items-start">
                              <div className="bg-indigo-100 text-indigo-600 rounded-lg p-1 mr-3 mt-0.5 flex-shrink-0">
                                <FaCheck className="text-xs" />
                              </div>
                              <span className="text-gray-700">Bestiller en specialudviklet løsning, kommunikerer vi via Discord og har eventuelt brug for yderligere oplysninger relateret til dit projekt</span>
                    </li>
                  </ul>
                          
                          <div className="bg-indigo-50 border-l-4 border-indigo-400 p-5 rounded-r-lg">
                            <h3 className="text-indigo-800 font-semibold mb-2 flex items-center">
                              <FaInfoCircle className="mr-2" />
                              Vores brug af Discord-brugernavne
                            </h3>
                    <p className="text-gray-700">
                              Dit Discord-brugernavn bruges kun til at identificere dig til support, produktlevering og købsverifikation. Vi deler ikke dette med tredjepart undtagen i de tilfælde, der er beskrevet i afsnittet "Deling af oplysninger".
                    </p>
                  </div>
                </div>
                  </div>
                </div>
              </div>

                  {/* Opbevaring af data section */}
                  <div id="opbevaring" className="section-container">
                <div className="flex items-center mb-6">
                      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl p-3 mr-4 shadow-md">
                    <FaDatabase className="text-xl" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">Opbevaring af data</h2>
                </div>
                    
                    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
                      <div className="flex items-start">
                        <div className="mr-6 bg-indigo-50 rounded-xl p-4 mt-1">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-3">Opbevaringsperiode</h3>
                          <p className="text-gray-700 mb-4">
                            Vi opbevarer dine personlige oplysninger så længe det er nødvendigt for at opfylde de formål, der er beskrevet i denne privatlivspolitik, medmindre en længere opbevaringsperiode er påkrævet eller tilladt ved lov.
                          </p>
                          <p className="text-gray-700 mb-4">
                            For køb og finansielle transaktioner gemmer vi data i minimum 5 år for at overholde bogføringslovgivningen.
                          </p>
                          
                          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                              <div className="flex items-center mb-3">
                                <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-3">
                                  <FaShieldAlt className="text-sm" />
                                </div>
                                <h4 className="font-semibold text-gray-900">Datasikkerhed</h4>
                              </div>
                              <p className="text-gray-600 text-sm">
                                Vi implementerer passende tekniske og organisatoriske foranstaltninger for at beskytte dine personlige oplysninger.
                              </p>
                            </div>
                            
                            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                              <div className="flex items-center mb-3">
                                <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-3">
                                  <FaUserShield className="text-sm" />
                                </div>
                                <h4 className="font-semibold text-gray-900">Adgangskontrol</h4>
                              </div>
                              <p className="text-gray-600 text-sm">
                                Adgang til personlige oplysninger er begrænset til godkendte medarbejdere og udviklere, der har brug for adgang for at kunne levere vores tjenester.
                    </p>
                  </div>
                </div>
              </div>
                  </div>
                </div>
              </div>

                  {/* Deling af oplysninger section */}
                  <div id="deling" className="section-container">
                <div className="flex items-center mb-6">
                      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl p-3 mr-4 shadow-md">
                    <FaShare className="text-xl" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">Deling af oplysninger</h2>
                </div>
                    
                    <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 mb-6">
                      <p className="text-gray-700 mb-5">
                        Vi sælger ikke dine personlige oplysninger til tredjeparter. Vi kan dog dele dine personlige oplysninger i følgende situationer:
                      </p>
                      
                      <div className="space-y-4">
                        <div className="bg-white rounded-lg p-5 shadow-sm transition-all duration-300 hover:shadow-md border border-indigo-100">
                          <div className="flex items-start">
                            <div className="bg-indigo-500 text-white rounded-lg p-2 mr-4 mt-1">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                              </svg>
                            </div>
                            <div>
                              <h3 className="font-semibold text-lg text-gray-900 mb-2">Udviklere og specialordrer</h3>
                              <p className="text-gray-700">
                                Hvis du bestiller en specialudvikling, kan vi dele relevante oplysninger med de udviklere, der arbejder på dit projekt. Disse udviklere er underlagt fortrolighedsforpligtelser.
                    </p>
                  </div>
                          </div>
                        </div>
                        
                        <div className="bg-white rounded-lg p-5 shadow-sm transition-all duration-300 hover:shadow-md border border-indigo-100">
                          <div className="flex items-start">
                            <div className="bg-indigo-500 text-white rounded-lg p-2 mr-4 mt-1">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                              </svg>
                            </div>
                            <div>
                              <h3 className="font-semibold text-lg text-gray-900 mb-2">Tjenesteudbydere</h3>
                              <p className="text-gray-700">
                                Vi samarbejder med tredjepartstjenesteudbydere, der hjælper os med at drive vores forretning (f.eks. betalingsprocessorer, hostingudbydere, support-platforme). Disse tjenesteudbydere har adgang til dine personlige oplysninger for at udføre disse opgaver på vores vegne.
                              </p>
                            </div>
                </div>
              </div>

                        <div className="bg-white rounded-lg p-5 shadow-sm transition-all duration-300 hover:shadow-md border border-indigo-100">
                          <div className="flex items-start">
                            <div className="bg-indigo-500 text-white rounded-lg p-2 mr-4 mt-1">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                              </svg>
                            </div>
                            <div>
                              <h3 className="font-semibold text-lg text-gray-900 mb-2">Myndighedskrav</h3>
                              <p className="text-gray-700">
                                Vi kan videregive dine personlige oplysninger, hvis det kræves ved lov eller i respons på gyldige anmodninger fra offentlige myndigheder (f.eks. en domstol eller en regeringsmyndighed).
                              </p>
                            </div>
                          </div>
                  </div>
                </div>
                    </div>
                  </div>

                  {/* Dine rettigheder section */}
                  <div id="rettigheder" className="section-container">
                    <div className="flex items-center mb-6">
                      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl p-3 mr-4 shadow-md">
                        <FaUserShield className="text-xl" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Dine rettigheder</h2>
                    </div>
                    
                    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
                      <p className="text-gray-700 mb-5">
                        Som bruger har du en række rettigheder i forhold til dine personlige oplysninger. Du har ret til at:
                      </p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-5 relative overflow-hidden group hover:shadow-md transition-all duration-300">
                          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full -mr-10 -mt-10 opacity-50 group-hover:scale-110 transition-transform duration-500"></div>
                          <div className="relative z-10">
                            <div className="flex items-center mb-3">
                              <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                              </div>
                              <h3 className="font-semibold text-gray-900">Adgang</h3>
                            </div>
                            <p className="text-gray-700 text-sm">
                              Du har ret til at anmode om adgang til de personlige oplysninger, vi har om dig.
                            </p>
                          </div>
                    </div>
                        
                        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-5 relative overflow-hidden group hover:shadow-md transition-all duration-300">
                          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full -mr-10 -mt-10 opacity-50 group-hover:scale-110 transition-transform duration-500"></div>
                          <div className="relative z-10">
                            <div className="flex items-center mb-3">
                              <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                    </div>
                              <h3 className="font-semibold text-gray-900">Rettelse</h3>
                    </div>
                            <p className="text-gray-700 text-sm">
                              Du har ret til at anmode om rettelse af unøjagtige eller ufuldstændige personlige oplysninger.
                            </p>
                    </div>
                  </div>
                        
                        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-5 relative overflow-hidden group hover:shadow-md transition-all duration-300">
                          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full -mr-10 -mt-10 opacity-50 group-hover:scale-110 transition-transform duration-500"></div>
                          <div className="relative z-10">
                            <div className="flex items-center mb-3">
                              <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </div>
                              <h3 className="font-semibold text-gray-900">Sletning</h3>
                            </div>
                            <p className="text-gray-700 text-sm">
                              Du har ret til at anmode om sletning af dine personlige oplysninger under visse omstændigheder.
                  </p>
                </div>
              </div>

                        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-5 relative overflow-hidden group hover:shadow-md transition-all duration-300">
                          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full -mr-10 -mt-10 opacity-50 group-hover:scale-110 transition-transform duration-500"></div>
                          <div className="relative z-10">
                            <div className="flex items-center mb-3">
                              <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                                </svg>
                              </div>
                              <h3 className="font-semibold text-gray-900">Begrænsning</h3>
                            </div>
                            <p className="text-gray-700 text-sm">
                              Du har ret til at anmode om begrænsning af behandlingen af dine personlige oplysninger.
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="mt-6 bg-indigo-50 rounded-xl p-5 border border-indigo-100">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Sådan udøve du dine rettigheder
                        </h3>
                        <p className="text-gray-700 mb-3">
                          Du kan udøve dine rettigheder ved at kontakte os via vores <Link href="/kontakt" className="text-indigo-600 hover:text-indigo-800 font-medium">kontaktside</Link>. Vi vil besvare din anmodning inden for 30 dage.
                        </p>
                        <p className="text-gray-700">
                          Hvis du er utilfreds med vores håndtering af dine personlige oplysninger, har du ret til at indgive en klage til den relevante tilsynsmyndighed.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Cookies section */}
                  <div id="cookies" className="section-container">
                <div className="flex items-center mb-6">
                      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl p-3 mr-4 shadow-md">
                    <FaCookieBite className="text-xl" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">Cookies</h2>
                </div>
                    
                    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
                      <div className="flex flex-col md:flex-row gap-6">
                        <div className="md:w-1/3 flex justify-center">
                          <div className="relative w-48 h-48">
                            <div className="absolute inset-0 bg-indigo-100 rounded-full opacity-20 animate-pulse"></div>
                            <div className="absolute inset-4 bg-indigo-100 rounded-full opacity-40"></div>
                            <div className="absolute inset-0 flex items-center justify-center" onContextMenu={(e) => e.preventDefault()} draggable="false">
                              <img src="https://www.svgrepo.com/show/30963/cookie.svg" alt="Cookie" className="h-24 w-24 text-indigo-300 select-none" draggable="false" />
                            </div>
                          </div>
                        </div>
                        <div className="md:w-2/3">
                          <p className="text-gray-700 mb-4">
                            Vores website bruger cookies for at forbedre din brugeroplevelse. Cookies er små tekstfiler, der gemmes på din enhed, når du besøger vores website.
                          </p>
                          <p className="text-gray-700 mb-4">
                    Vi bruger følgende typer cookies:
                  </p>
                          <ul className="space-y-3 mb-5">
                            <li className="flex items-start">
                              <div className="bg-indigo-100 text-indigo-600 rounded-lg p-1 mr-3 mt-0.5">
                                <FaCheck className="text-xs" />
                              </div>
                              <div>
                                <span className="font-medium text-gray-900">Nødvendige cookies</span>
                                <p className="text-gray-600 text-sm">Disse er afgørende for, at websitet kan fungere korrekt</p>
                              </div>
                            </li>
                            <li className="flex items-start">
                              <div className="bg-indigo-100 text-indigo-600 rounded-lg p-1 mr-3 mt-0.5">
                                <FaCheck className="text-xs" />
                              </div>
                              <div>
                                <span className="font-medium text-gray-900">Funktionelle cookies</span>
                                <p className="text-gray-600 text-sm">Disse hjælper med at huske dine præferencer og forbedre din brugeroplevelse</p>
                              </div>
                            </li>
                            <li className="flex items-start">
                              <div className="bg-indigo-100 text-indigo-600 rounded-lg p-1 mr-3 mt-0.5">
                                <FaCheck className="text-xs" />
                              </div>
                              <div>
                                <span className="font-medium text-gray-900">Analytiske cookies</span>
                                <p className="text-gray-600 text-sm">Disse hjælper os med at forstå, hvordan besøgende interagerer med vores website</p>
                              </div>
                            </li>
                          </ul>
                          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                            <p className="text-gray-700 text-sm">
                              Du kan blokere cookies ved at aktivere indstillingen i din browser, der giver dig mulighed for at afvise alle eller nogle cookies. Hvis du blokerer cookies, vil du muligvis ikke kunne bruge alle funktioner på vores website.
                            </p>
                    </div>
                    </div>
                    </div>
                </div>
              </div>

                  {/* Ændringer section */}
                  <div id="aendringer" className="section-container">
                <div className="flex items-center mb-6">
                      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl p-3 mr-4 shadow-md">
                    <FaHistory className="text-xl" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">Ændringer</h2>
                </div>
                    
                    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
                      <div className="flex items-start">
                        <div className="bg-indigo-100 text-indigo-600 rounded-xl p-3 mr-4 flex-shrink-0">
                          <FaClock className="text-xl" />
                        </div>
                        <div>
                          <p className="text-gray-700 mb-4">
                            Vi kan opdatere denne privatlivspolitik fra tid til anden for at afspejle ændringer i vores praksis eller af andre operationelle, juridiske eller lovgivningsmæssige årsager.
                          </p>
                          <p className="text-gray-700 mb-4">
                            Hvis vi foretager væsentlige ændringer, vil vi informere dig ved at opdatere datoen øverst på denne privatlivspolitik og, hvor det er relevant, give dig yderligere besked.
                          </p>
                          <div className="mt-8 bg-white rounded-2xl shadow-lg p-6 border border-gray-100 relative overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-r from-indigo-50 to-purple-50 opacity-50"></div>
                            <div className="relative z-10">
                              <div className="flex items-center gap-4">
                                <div className="bg-gradient-to-br from-indigo-500 to-purple-600 text-white rounded-xl p-3 shadow-md">
                                  <FaCalendarAlt className="text-xl" />
                                </div>
                                <div>
                                  <p className="text-gray-700 font-semibold text-lg">
                                    Seneste opdatering: <span className="text-indigo-600">29. marts 2025</span>
                                  </p>
                                </div>
                              </div>
                              <div className="mt-4 bg-indigo-50/50 rounded-xl p-4 border border-indigo-100 flex items-start gap-3">
                                <div className="bg-white text-indigo-600 rounded-lg p-2 shadow-sm flex-shrink-0">
                                  <FaInfoCircle className="text-lg" />
                                </div>
                                <p className="text-gray-700 text-sm leading-relaxed">
                                  Vi anbefaler at du regelmæssigt gennemgår denne privatlivspolitik for at forblive informeret om, hvordan vi beskytter og håndterer dine personlige oplysninger.
                                </p>
                              </div>
                            </div>
                            <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-purple-100 rounded-full opacity-20"></div>
                            <div className="absolute -top-8 -left-8 w-24 h-24 bg-indigo-100 rounded-full opacity-20"></div>
                          </div>
                        </div>
                  </div>
                </div>
              </div>

                  {/* Kontakt section */}
                  <div id="kontakt" className="section-container">
                    <div className="flex items-center mb-8">
                      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl p-3 mr-4 shadow-lg">
                        <FaEnvelope className="text-xl" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">Kontakt</h2>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 mb-6 relative overflow-hidden">
                      <div className="absolute inset-0 opacity-10 bg-[url('/privacy-pattern.svg')]"></div>
                      <div className="relative z-10">
                        <p className="text-gray-700 mb-6 text-lg leading-relaxed">
                          Har du spørgsmål eller bekymringer om vores privatlivspolitik eller behandling af dine oplysninger? Vi er her for at hjælpe.
                        </p>

                        <div className="bg-white rounded-2xl shadow-xl p-8 border border-blue-100">
                          <div className="flex flex-col md:flex-row items-center gap-8">
                            <div className="flex-shrink-0">
                              <div className="bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-2xl w-20 h-20 flex items-center justify-center shadow-lg">
                                <FaEnvelope className="text-3xl" />
                              </div>
                            </div>
                            <div className="flex-1">
                              <h3 className="text-2xl font-bold text-gray-900 mb-3">Kontakt os i dag</h3>
                              <p className="text-gray-700 mb-6 text-lg">
                                Vi står klar til at besvare dine spørgsmål og hjælpe med dine bekymringer.
                              </p>
                              <Link
                                href="/kontakt"
                                className="group inline-flex items-center px-6 py-3.5 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                              >
                                <span>Gå til kontaktformular</span>
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-3 transform transition-all duration-300 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                              </Link>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
    </main>
    </div>
  );
}