'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { FaArrowLeft, FaDownload, FaSpinner, FaCheck, FaFileAlt, FaCheckCircle, FaExclamationCircle, FaPuzzlePiece, FaExternalLinkAlt, FaPlug, FaClock, FaCode, FaTag, FaShoppingCart } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { getImageUrl } from '@/lib/imageUtils';
import PaymentModal from '@/components/PaymentModal';
import FavoriteButton from '@/components/FavoriteButton';

// Download popup state type
interface DownloadState {
  isVisible: boolean;
  filename: string;
  status: 'downloading' | 'completed' | 'error';
}

interface Addon {
  name: string;
  url?: string;
  version?: string;
  required: boolean;
  isDirectDownload?: boolean;
}

interface Product {
  _id: string;
  projectName: string;
  projectDescription: string;
  version: string;
  productType: string;
  price: number;
  discount?: number;
  isPremium: boolean;
  createdBy: string;
  discordUserId?: string;
  createdAt: string;
  updatedAt: string;
  status: string;
  addons?: Addon[];
  screenshotUrls: Array<{
    fileId: string;
    filename: string;
    contentType: string;
    url?: string;
    preventDrag?: boolean;
    preventContextMenu?: boolean;
  }>;
  fileUrls: Array<{
    fileId: string;
    filename: string;
    contentType: string;
    url?: string;
  }>;
}

interface Freelancer {
  _id: string;
  username: string;
  discordUserId?: string;
  avatar?: string;
  isVerified?: boolean;
  avatarUrl?: string;
  isAdmin?: boolean;
}

// Hardcoded animation service product
const ANIMATION_SERVICE: Product = {
  _id: 'animation-service',
  projectName: 'Animeret Billede Service',
  projectDescription: 'Få lavet professionelle animerede billeder til din Minecraft server eller personligt. Jeg tilbyder animerede billeder til profilbilleder, server logoer og mere. Med en smule erfaring inden for grafik kan jeg hjælpe dig med at bringe dine ideer til live med højkvalitets animerede billeder, der fanger opmærksomheden.',
  version: '1.0',
  productType: 'service',
  price: 199,
  discount: 50,
  isPremium: true,
  createdBy: 'MyckasP',
  createdAt: new Date('2025-04-21').toISOString(),
  updatedAt: new Date('2025-04-23').toISOString(),
  status: 'approved',
  screenshotUrls: [
    {
      fileId: 'handdrawn-image-1',
      filename: 'handdrawn.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745352236/tegnet_ytjlpi.png',
      preventDrag: true,
      preventContextMenu: true
    },
    {
      fileId: 'handdrawn-image-2',
      filename: 'handdrawn-raw.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745428885/FlickMC_Tegnet-removebg-preview_vqq6zu.png',
      preventDrag: true,
      preventContextMenu: true
    },
    {
      fileId: 'colored-image-1',
      filename: 'myckasp.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745352245/myckasp_whxdzt.png',
      preventDrag: true,
      preventContextMenu: true
    },
    {
      fileId: 'colored-image-2',
      filename: 'flickmc.png',
      contentType: 'image/png',
      url: 'https://res.cloudinary.com/dwqxk2tip/image/upload/v1745428835/FlickMC__2_-removebg-preview_eexzik.png',
      preventDrag: true,
      preventContextMenu: true
    }
  ],
  fileUrls: [],
};

// Hardcoded animation service freelancer
const ANIMATION_FREELANCER: Freelancer = {
  _id: 'animation-pro-id',
  username: 'MyckasP',
  discordUserId: '929307108002889769',
  avatar: '5f3fa1c7b0b552c5b03e4290204d0af1',
  isAdmin: true,
  isVerified: false,
  avatarUrl: 'https://cdn.discordapp.com/avatars/929307108002889769/5f3fa1c7b0b552c5b03e4290204d0af1.png'
};

export default function ProductDetailPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedScreenshot, setSelectedScreenshot] = useState<Product['screenshotUrls'][0] | null>(null);
  const [downloadState, setDownloadState] = useState<DownloadState>({
    isVisible: false,
    filename: '',
    status: 'downloading'
  });
  const [freelancer, setFreelancer] = useState<Freelancer | null>(null);
  const [freelancerLoading, setFreelancerLoading] = useState(false);
  const [avatarFailed, setAvatarFailed] = useState(false);
  // Payment modal state
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);

  // Robust title management that persists even if external scripts try to change it
  useEffect(() => {
    // Set initial title
    const originalTitle = document.title;
    const productTitle = product ? `${product.projectName} | MCDevHub` : 'Produkt | MCDevHub';
    document.title = productTitle;
    
    // Persistent title check via interval
    const titleInterval = setInterval(() => {
      if (document.title !== productTitle) {
        document.title = productTitle;
      }
    }, 500); // Check every 500ms
    
    // Cleanup
    return () => {
      clearInterval(titleInterval);
      document.title = originalTitle;
    };
  }, [product]);

  // Handle avatar loading errors
  const handleAvatarError = () => {
    setAvatarFailed(true);
  };

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        // Check if this is our hardcoded animation service product
        if (id === 'animation-service') {
          // Set the hardcoded animation service product
          setProduct(ANIMATION_SERVICE);
          
          // Set the first screenshot as the selected image
          if (ANIMATION_SERVICE.screenshotUrls.length > 0) {
            const firstScreenshot = ANIMATION_SERVICE.screenshotUrls[0];
            setSelectedImage(firstScreenshot.fileId);
            setSelectedScreenshot(firstScreenshot);
          }
          
          // Set the hardcoded freelancer
          setFreelancer(ANIMATION_FREELANCER);
          
          // Artificially delay to simulate loading
          setTimeout(() => {
            setLoading(false);
          }, 800);
          
          return;
        }
        
        // Regular API fetch for real products
        const response = await fetch(`/api/products/${id}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Produktet blev ikke fundet');
          }
          throw new Error('Kunne ikke hente produktinformationen');
        }
        
        const data = await response.json();
        setProduct(data);
        
        // Set the first screenshot as the selected image
        if (data?.screenshotUrls?.length > 0) {
          const firstScreenshot = data.screenshotUrls[0];
          setSelectedImage(firstScreenshot.fileId);
          setSelectedScreenshot(firstScreenshot);
        }
        // Fetch freelancer info after product is loaded
        if (data?.createdBy) {
          setFreelancerLoading(true);
          try {
            const freelancerRes = await fetch(`/api/freelancers/${data.createdBy}`);
            if (freelancerRes.ok) {
              const freelancerData = await freelancerRes.json();
              // Ensure MyckasP is marked as admin and not verified
              if (freelancerData.freelancer.username === 'MyckasP') {
                freelancerData.freelancer.isAdmin = true;
                freelancerData.freelancer.isVerified = false;
              }
              setFreelancer(freelancerData.freelancer);
            }
          } catch (e) {
            // ignore
          } finally {
            setFreelancerLoading(false);
          }
        }
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Der opstod en fejl');
      } finally {
        setLoading(false);
      }
    };
    fetchProduct();
  }, [id]);

  useEffect(() => {
    if (loading) {
      document.body.style.overflow = 'hidden';
      window.scrollTo(0, 0);
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [loading]);

  // Handle download click
  const handleDownload = async (filename: string, fileId: string, url?: string) => {
    // Show the download popup with downloading state
    setDownloadState({
      isVisible: true,
      filename: filename,
      status: 'downloading'
    });
    
    try {
      // Create the download URL
      const downloadUrl = url ? url : `/api/files/download/${fileId}`;
      
      // Check if the file exists by making a HEAD request
      // If using a direct URL, skip this check
      if (!url) {
        try {
          // Try checking if the file exists first
          const checkResponse = await fetch(`/api/files/check/${fileId}`);
          if (!checkResponse.ok) {
            throw new Error('File not found or inaccessible');
          }
        } catch (error) {
          // Continue anyway, as the download might still work
        }
      }
      
      // Actually trigger the download by creating a link to the file
      const downloadLink = document.createElement('a');
      downloadLink.href = downloadUrl;
      downloadLink.download = filename;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      
      // Update UI to show completion after a delay
      setTimeout(() => {
        setDownloadState(prev => ({
          ...prev,
          status: 'completed'
        }));
        
        // Automatically hide the popup after completion
        setTimeout(() => {
          setDownloadState({
            isVisible: false,
            filename: '',
            status: 'downloading'
          });
        }, 3000);
      }, 1500);
    } catch (error) {
      // Show error in the download state
      setDownloadState(prev => ({
        ...prev,
        status: 'error'
      }));
      
      // Hide the error message after a delay
      setTimeout(() => {
        setDownloadState({
          isVisible: false,
          filename: '',
          status: 'downloading'
        });
      }, 3000);
    }
  };

  // Handle direct download for addons that have isDirectDownload set to true
  const handleDirectDownload = (name: string, url: string) => {
    // Show the download popup with downloading state
    setDownloadState({
      isVisible: true,
      filename: name,
      status: 'downloading'
    });
    
    try {
      // Open the URL in a new tab
      window.open(url, '_blank');
      
      // Update UI to show completion after a delay
      setTimeout(() => {
        setDownloadState(prev => ({
          ...prev,
          status: 'completed'
        }));
        
        // Automatically hide the popup after completion
        setTimeout(() => {
          setDownloadState({
            isVisible: false,
            filename: '',
            status: 'downloading'
          });
        }, 3000);
      }, 1000); // Shorter delay for direct downloads
    } catch (error) {
      // Show error in the download state
      setDownloadState(prev => ({
        ...prev,
        status: 'error'
      }));
      
      // Hide the error message after a delay
      setTimeout(() => {
        setDownloadState({
          isVisible: false,
          filename: '',
          status: 'downloading'
        });
      }, 3000);
    }
  };

  const getProductTypeLabel = (type: string) => {
    switch (type) {
      case 'plugin':
        return 'Plugin';
      case 'script':
        return 'Skript';
      case 'map':
        return 'Map';
      case 'build':
        return 'Build';
      case 'service':
        return 'Service';
      default:
        return type;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('da-DK', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Calculate final price if a discount is applied
  const calculateDiscountedPrice = (price: number, discount?: number): number => {
    if (!discount || discount <= 0 || price <= 0) return price;
    return Math.round(price * (1 - discount / 100));
  };

  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90 backdrop-blur-sm">
        <div className="flex flex-col items-center max-w-sm mx-auto p-8 rounded-xl bg-white shadow-lg border border-gray-100">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mb-6" style={{ animationDuration: '0.4s' }}></div>
          <div className="text-blue-700 text-lg font-medium">Indlæser produkt...</div>
          <p className="text-gray-500 text-sm mt-2 text-center">Vent venligst mens vi henter produktinformationen</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 p-6 rounded-lg text-center">
            <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Fejl ved indlæsning af produkt</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link href="/products" className="inline-flex items-center text-blue-600 hover:underline">
              <FaArrowLeft className="mr-2" />
              Tilbage til produkter
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-gray-100 border border-gray-200 p-6 rounded-lg text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Produkt ikke fundet</h2>
            <p className="text-gray-600 mb-4">Vi kunne ikke finde det ønskede produkt.</p>
            <Link href="/products" className="inline-flex items-center text-blue-600 hover:underline">
              <FaArrowLeft className="mr-2" />
              Tilbage til produkter
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20 px-4 pb-12">
      {/* Payment Modal */}
      {product && (
        <PaymentModal
          isOpen={isPaymentModalOpen}
          onClose={() => setIsPaymentModalOpen(false)}
          productId={product._id}
          productName={product.projectName}
          price={product.price}
          finalPrice={calculateDiscountedPrice(product.price, product.discount)}
        />
      )}

      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <Link href="/products" className="inline-flex items-center text-gray-600 hover:text-blue-600 transition-colors group">
            <FaArrowLeft className="mr-2 transition-all duration-300 ease-in-out group-hover:-translate-x-1 group-hover:scale-110" />
            <span>Tilbage til produkter</span>
          </Link>
        </div>

        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          {/* Product Header */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-700 p-6 text-white">
            <div className="flex flex-wrap items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">{product.projectName}</h1>
                <div className="flex items-center mt-2 space-x-4">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-200 text-blue-800">
                    {getProductTypeLabel(product.productType)}
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-200 text-blue-800">
                    Version {product.version}
                  </span>
                  {product.discount && product.discount > 0 && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-300 text-amber-800">
                      <FaTag className="mr-1 h-3 w-3" /> {product.discount}% Rabat
                    </span>
                  )}
                </div>
              </div>
              <div className="mt-4 md:mt-0">
                {product.price > 0 ? (
                  product.discount && product.discount > 0 ? (
                    <div className="flex flex-col items-end">
                      <div className="text-2xl font-bold">{calculateDiscountedPrice(product.price, product.discount)} DKK</div>
                      <div className="text-sm line-through text-gray-300">{product.price} DKK</div>
                    </div>
                  ) : (
                    <div className="text-2xl font-bold">{product.price} DKK</div>
                  )
                ) : (
                  <div className="text-lg font-semibold text-green-300">Gratis</div>
                )}
              </div>
            </div>
          </div>

          {/* Product Content */}
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left column - Images and Addons */}
              <div className="space-y-8">
              {/* Product Images */}
                {product.screenshotUrls && product.screenshotUrls.length > 0 ? (
                  <div>
                    <div className="border border-gray-200 rounded-lg overflow-hidden h-80 bg-gray-100 mb-4 relative">
                      {selectedScreenshot ? (
                        <>
                          <FavoriteButton productId={product._id} position="top-right" />
                          <img
                            src={getImageUrl(selectedScreenshot)}
                            alt={product.projectName}
                            className="w-full h-full object-contain"
                          />
                        </>
                      ) : (
                        <div className="flex items-center justify-center h-full text-gray-400">
                          <p>Vælg et billede nedenfor</p>
                        </div>
                      )}
                    </div>
                    <div className="grid grid-cols-4 gap-2">
                      {product.screenshotUrls.map((screenshot, index) => (
                        <div
                          key={screenshot.fileId || `screenshot-${index}`}
                          className={`border-2 rounded-md overflow-hidden h-20 cursor-pointer ${
                            selectedImage === screenshot.fileId
                              ? 'border-blue-500'
                              : 'border-gray-200 hover:border-blue-300'
                          }`}
                          onClick={() => {
                            setSelectedImage(screenshot.fileId);
                            setSelectedScreenshot(screenshot);
                          }}
                        >
                          <img
                            src={getImageUrl(screenshot)}
                            alt={screenshot.filename}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="border border-gray-200 rounded-lg h-80 flex items-center justify-center bg-gray-100 relative">
                    <FavoriteButton productId={product._id} position="top-right" />
                    <p className="text-gray-500">Ingen billeder tilgængelige</p>
                  </div>
                )}

                {/* Addons/Dependencies Section */}
                <div className="overflow-hidden rounded-xl shadow-sm border border-gray-200">
                  <div className="bg-gradient-to-r from-blue-500 to-indigo-600 px-5 py-4">
                    <div className="flex items-center">
                      <div className="bg-white/20 p-2 rounded-lg mr-3">
                        <FaPlug className="h-4 w-4 text-white" />
                      </div>
                      <h3 className="text-lg font-semibold text-white">Addons & Dependencies</h3>
                    </div>
                  </div>
                  
                  {product.addons && product.addons.length > 0 ? (
                    <div className="divide-y divide-gray-100">
                      {product.addons.map((addon, index) => (
                        <div key={index} className="px-5 py-4 hover:bg-gray-50 transition-colors">
                          <div className="flex items-center justify-between">
                            {/* Left content - Icon and addon info */}
                            <div className="flex items-start space-x-3">
                              <div className={`p-2 rounded-lg flex-shrink-0 ${addon.required ? 'bg-blue-100' : 'bg-gray-100'}`}>
                                <FaPuzzlePiece className={`h-4 w-4 ${addon.required ? 'text-blue-600' : 'text-gray-500'}`} />
                              </div>
                              <div>
                                <div className="flex items-center">
                                  <h4 className="font-medium text-gray-900">{addon.name}</h4>
                                  {addon.required && (
                                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                      Påkrævet
                                    </span>
                                  )}
                                  {addon.isDirectDownload && (
                                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                      Direkte Download
                                    </span>
                                  )}
                                </div>
                                
                                {/* Version and info */}
                                <div className="mt-1 flex items-center space-x-4 text-sm">
                                  {addon.version && (
                                    <span className="inline-flex items-center text-gray-500">
                                      <svg className="mr-1 h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                      </svg>
                                      v{addon.version}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            {/* Right content - URL button */}
                            {addon.url && (
                              addon.isDirectDownload ? (
                                <button 
                                  onClick={() => handleDirectDownload(addon.name, addon.url!)}
                                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:text-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                >
                                  <span className="mr-1.5">Download</span>
                                  <FaDownload className="h-3 w-3" />
                                </button>
                              ) : (
                                <a 
                                  href={addon.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:text-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                >
                                  <span className="mr-1.5">Besøg</span>
                                  <FaExternalLinkAlt className="h-3 w-3" />
                                </a>
                              )
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-8 text-center">
                      <div className="inline-flex items-center justify-center p-3 bg-blue-50 rounded-full mb-4">
                        <FaPuzzlePiece className="h-6 w-6 text-blue-500" />
                      </div>
                      <h4 className="text-gray-700 font-medium mb-1">Ingen dependencies fundet</h4>
                      <p className="text-gray-500 text-sm max-w-sm mx-auto">
                        Dette produkt har ikke nogen addons eller dependencies listet.
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Product Details */}
              <div>
                <h2 className="text-xl font-bold text-gray-800 mb-4">Beskrivelse</h2>
                <div className="prose prose-blue max-w-none mb-6 text-gray-700">
                  <p className="whitespace-pre-wrap">{product.projectDescription}</p>
                </div>

                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">Produktdetaljer</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>
                      <span className="font-medium">Type:</span> {getProductTypeLabel(product.productType)}
                    </li>
                    <li>
                      <span className="font-medium">Version:</span> {product.version}
                    </li>
                    <li>
                      <span className="font-medium">Oprettet:</span> {formatDate(product.createdAt)}
                    </li>
                    <li>
                      <span className="font-medium">Opdateret:</span> {formatDate(product.updatedAt)}
                    </li>
                  </ul>
                </div>
                {/* Developer Card */}
                <div className="flex items-center gap-3 mt-6">
                  {freelancerLoading ? (
                    <div className="w-12 h-12 rounded-full bg-gray-200 animate-pulse" />
                  ) : (
                    <Link href={freelancer ? `/developers/${freelancer.username}` : '#'} className="flex items-center gap-3 group">
                      {/* Avatar */}
                      {freelancer?.discordUserId && freelancer?.avatar && !avatarFailed ? (
                        <img
                          src={`https://cdn.discordapp.com/avatars/${freelancer.discordUserId}/${freelancer.avatar}.png?size=128`}
                          alt={freelancer.username}
                          className="w-12 h-12 rounded-full border-2 border-white shadow"
                          onError={handleAvatarError}
                        />
                      ) : freelancer?.avatarUrl && !avatarFailed ? (
                        <img
                          src={freelancer.avatarUrl}
                          alt={freelancer.username}
                          className="w-12 h-12 rounded-full border-2 border-white shadow"
                          onError={handleAvatarError}
                        />
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                          <svg className="w-7 h-7 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                      )}
                      {/* Username and verified */}
                      <div className="flex flex-col">
                        <span className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors flex items-center">
                          {freelancer?.username}
                          {freelancer?.isVerified && (
                            <span className="ml-1 text-blue-500" title="Verificeret">
                              <FaCheckCircle className="inline w-4 h-4" />
                            </span>
                          )}
                          {freelancer?.isAdmin && (
                            <span className="ml-1 text-red-500" title="Administrator">
                              <FaCheckCircle className="inline w-4 h-4" />
                            </span>
                          )}
                        </span>
                        <span className="text-xs text-gray-500">Udvikler</span>
                      </div>
                    </Link>
                  )}
                </div>
                {/* Files Section */}
                {product.fileUrls && product.fileUrls.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">Filer</h3>
                    <div className="border border-gray-200 rounded-lg divide-y select-none">
                      {product.fileUrls.map((file, index) => (
                        <div key={file.fileId || `file-${index}`} className="p-3 flex items-center justify-between">
                          <span className="text-gray-700 select-none">{file.filename}</span>
                          {product.price === 0 && product.status === 'approved' ? (
                            <button
                              onClick={() => handleDownload(file.filename, file.fileId, file.url)}
                              className="inline-flex items-center text-blue-500 hover:text-blue-700 cursor-pointer"
                            >
                              <FaDownload className="mr-1" />
                              <span>Download</span>
                            </button>
                          ) : (
                            <span className="inline-flex items-center text-gray-400 cursor-default select-none">
                              <FaDownload className="mr-1" />
                              <span>Download</span>
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Purchase/Download Button */}
                {product?.status === 'approved' && (
                  <div className="mt-8">
                    {product.price > 0 ? (
                      <button 
                        onClick={() => setIsPaymentModalOpen(true)}
                        className="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 transition-colors relative"
                      >
                        <div className="flex flex-col items-center">
                          <span className="flex items-center">
                            <FaShoppingCart className="mr-2" />
                            Køb for {calculateDiscountedPrice(product.price, product.discount)} DKK
                          </span>
                          {product.discount && product.discount > 0 ? (
                            <>
                              <span className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                                {product.discount}% rabat
                              </span>
                              <span className="text-xs line-through text-gray-200 mt-1">
                                {product.price} DKK
                              </span>
                            </>
                          ) : null}
                        </div>
                      </button>
                    ) : product.fileUrls && product.fileUrls.length > 0 ? (
                      <button
                        onClick={() => handleDownload(product.fileUrls[0].filename, product.fileUrls[0].fileId, product.fileUrls[0].url)}
                        className="block w-full text-center bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 transition-colors"
                      >
                        Download Nu
                      </button>
                    ) : (
                      <button
                        disabled
                        className="block w-full text-center bg-green-400 text-white py-3 px-6 rounded-lg font-semibold cursor-default"
                      >
                        Download Kommer Snart
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Download Animation Popup */}
      <AnimatePresence>
        {downloadState.isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="fixed bottom-6 right-6 bg-white rounded-xl shadow-xl p-4 z-50 w-80"
          >
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-3">
                {downloadState.status === 'downloading' ? (
                  <div className="bg-blue-100 rounded-full p-2">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <FaSpinner className="h-6 w-6 text-blue-600" />
                    </motion.div>
                  </div>
                ) : downloadState.status === 'completed' ? (
                  <motion.div
                    initial={{ scale: 0.5 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", damping: 10, stiffness: 200 }}
                    className="bg-green-100 rounded-full p-2"
                  >
                    <FaCheck className="h-6 w-6 text-green-600" />
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ scale: 0.5 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", damping: 10, stiffness: 200 }}
                    className="bg-red-100 rounded-full p-2"
                  >
                    <FaExclamationCircle className="h-6 w-6 text-red-600" />
                  </motion.div>
                )}
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-semibold text-gray-900 flex items-center">
                  <FaFileAlt className="mr-1 text-gray-500" />
                  {downloadState.filename}
                </h3>
                <p className="mt-1 text-sm text-gray-600">
                  {downloadState.status === 'downloading' ? 'Downloader fil...' : downloadState.status === 'completed' ? 'Download gennemført!' : 'Der opstod en fejl'}
                </p>
                <div className="mt-2">
                  {downloadState.status === 'downloading' ? (
                    <div className="h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
                      <motion.div
                        initial={{ width: "5%" }}
                        animate={{ width: "100%" }}
                        transition={{ duration: 1.5, ease: "easeInOut" }}
                        className="h-full bg-blue-600"
                      />
                    </div>
                  ) : (
                    <motion.button
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5 }}
                      className="text-xs text-blue-600 hover:text-blue-800 mt-1 font-medium"
                      onClick={() => setDownloadState({ isVisible: false, filename: '', status: 'downloading' })}
                    >
                      Luk
                    </motion.button>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
} 