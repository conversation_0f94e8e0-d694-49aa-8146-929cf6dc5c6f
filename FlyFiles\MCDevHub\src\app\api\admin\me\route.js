import { NextResponse } from 'next/server';
import { auth } from '@/lib/auth';

export const runtime = 'nodejs'; // Use Node.js runtime

export async function GET(request) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    // If no session or user is not admin, return unauthorized
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Uautoriseret adgang' },
        { status: 401 }
      );
    }

    // Create user object with admin info from session
    const user = {
      username: session.user.name,
      admintype: session.user.adminType,
      allowedcases: session.user.allowedCases,
      discordUserId: session.user.id
    };

    // Return user info
    return NextResponse.json({
      success: true,
      user
    });

  } catch (error) {
    console.error('Error getting admin info:', error);

    // Return error response
    return NextResponse.json(
      { success: false, message: 'Uautoriseret adgang' },
      { status: 401 }
    );
  }
}