import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';

export async function POST() {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      console.log('No user session found for refresh');
      return NextResponse.json({
        success: false,
        isAdmin: false,
        message: 'No active user session'
      }, { status: 200 });
    }

    // Get the Discord ID from the accounts table
    const { db } = await connectToDatabase();

    // First, get the actual Discord ID from the account
    const discordAccount = await db.collection("accounts").findOne({
      userId: session.user.id,
      provider: "discord"
    });

    let discordUserId = session.user.id;
    if (discordAccount && discordAccount.discordId) {
      discordUserId = discordAccount.discordId;
    } else if (discordAccount && discordAccount.providerAccountId) {
      discordUserId = discordAccount.providerAccountId;
    }

    console.log('Force refreshing admin status for Discord ID:', discordUserId);

    if (!discordUserId) {
      console.log('No Discord ID found in user metadata');
      return NextResponse.json({
        success: false,
        isAdmin: false,
        message: 'No Discord ID found'
      }, { status: 200 });
    }

    // Check if the user exists in adminusers collection with multiple approaches
    let adminUser = null;
    
    // Method 1: Direct match
    adminUser = await db.collection('adminusers').findOne({ discordUserId });
    
    // Method 2: String comparison
    if (!adminUser) {
      adminUser = await db.collection('adminusers').findOne({ discordUserId: discordUserId.toString() });
    }
    
    // Method 3: Numeric comparison
    if (!adminUser) {
      try {
        const numericId = parseFloat(discordUserId);
        if (!isNaN(numericId)) {
          adminUser = await db.collection('adminusers').findOne({ discordUserId: numericId });
        }
      } catch (e) {
        console.log('Error in numeric comparison');
      }
    }
    
    // Method 4: Case-insensitive regex
    if (!adminUser) {
      try {
        adminUser = await db.collection('adminusers').findOne({ 
          discordUserId: { $regex: new RegExp(`^${discordUserId}$`, 'i') } 
        });
      } catch (e) {
        console.log('Error in regex comparison');
      }
    }
    
    // Method 5: Alternative field names
    if (!adminUser) {
      adminUser = await db.collection('adminusers').findOne({
        $or: [
          { discord: discordUserId },
          { discordId: discordUserId },
          { discord_id: discordUserId },
          { discord_user_id: discordUserId }
        ]
      });
    }
    
    const isAdmin = !!adminUser;
    
    // Return detailed admin status
    return NextResponse.json({
      success: true,
      isAdmin,
      discordId: discordUserId,
      adminName: adminUser?.username || null,
      message: isAdmin ? 'User is an admin' : 'User is not an admin'
    }, { status: 200 });
    
  } catch (error) {
    console.error('Error refreshing admin status:', error);
    return NextResponse.json({ 
      success: false,
      isAdmin: false,
      error: 'Internal server error', 
      message: error.message 
    }, { status: 500 });
  }
} 