import { NextResponse } from 'next/server';

export const runtime = 'nodejs'; // Use Node.js runtime

export async function POST() {
  try {
    // Create a response object
    const response = NextResponse.json({
      success: true,
      message: 'Logget ud'
    });
    
    // Delete the admin token cookie using the response object
    response.cookies.delete('admin_token');
    
    return response;
  } catch (error) {
    console.error('Error logging out:', error);
    
    // Return error response
    return NextResponse.json(
      { success: false, message: 'Der opstod en fejl under logout' },
      { status: 500 }
    );
  }
} 