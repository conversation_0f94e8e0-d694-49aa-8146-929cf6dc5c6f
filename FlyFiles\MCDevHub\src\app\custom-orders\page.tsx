'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Calendar from '../components/Calendar';
import { FaDiscord } from 'react-icons/fa';

export default function CustomOrdersPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    discordUsername: '',
    serverType: '',
    orderType: 'plugin',
    description: '',
    budget: '',
    deadline: ''
  });

  const [discordChecked, setDiscordChecked] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Set page title
  useEffect(() => {
    // Set initial title
    document.title = "Specialbestillinger | MCDevHub";
    
    // Maintain title with an interval in case it gets overwritten
    const titleInterval = setInterval(() => {
      if (document.title !== "Specialbestillinger | MCDevHub") {
        document.title = "Specialbestillinger | MCDevHub";
      }
    }, 500);
    
    // Cleanup
    return () => clearInterval(titleInterval);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDiscordChecked(e.target.checked);
  };

  const handleDateSelect = (date: Date) => {
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    const formattedDate = `${day < 10 ? '0' + day : day}-${month < 10 ? '0' + month : month}-${year}`;
    
    setFormData(prev => ({
      ...prev,
      deadline: formattedDate
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!discordChecked) {
      alert('Du skal bekræfte, at du har tilsluttet dig vores Discord server.');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      const response = await fetch('/api/submit-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formType: 'custom-order',
          ...formData,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit form');
      }

      setSubmitted(true);
      setFormData({
        name: '',
        email: '',
        discordUsername: '',
        serverType: '',
        orderType: 'plugin',
        description: '',
        budget: '',
        deadline: ''
      });
    } catch (error) {
      console.error('Error submitting form:', error);
      setError('Der opstod en fejl. Prøv igen senere eller kontakt os via Discord.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <section className="bg-blue-600 text-white py-10 sm:py-12 md:py-16">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-3 sm:mb-4">Specialbestillinger</h1>
          <p className="text-lg sm:text-xl text-blue-100 max-w-md sm:max-w-lg md:max-w-2xl mx-auto">
            Få udviklet custom plugins, skripts og builds til din Minecraft server
          </p>
        </div>
      </section>
      
      {/* Order Process */}
      <section className="py-16 sm:py-20 md:py-24 bg-gradient-to-b from-blue-50 to-white">
        <div className="container mx-auto px-4 sm:px-6">
          <h2 className="text-3xl sm:text-4xl font-bold mb-12 sm:mb-16 text-center text-gray-900 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Sådan Fungerer Det
          </h2>
          
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
              <div className="group bg-white p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-blue-100 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-blue-50 group-hover:bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6 transition-colors duration-300">
                    <span className="text-3xl font-bold text-blue-600">1</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-800 mb-4 text-center transition-colors duration-300">
                    Indsend Forespørgsel
                  </h3>
                  <p className="text-gray-600 group-hover:text-gray-700 text-center leading-relaxed">
                    Udfyld formularen nedenfor med detaljerne om dit ønskede plugin, skript eller build.
                  </p>
                </div>
              </div>
              
              <div className="group bg-white p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-blue-100 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-blue-50 group-hover:bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6 transition-colors duration-300">
                    <span className="text-3xl font-bold text-blue-600">2</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-800 mb-4 text-center transition-colors duration-300">
                    Få et Tilbud
                  </h3>
                  <p className="text-gray-600 group-hover:text-gray-700 text-center leading-relaxed">
                    Vi vurderer din forespørgsel og sender dig et prisoverslag samt en forventet leveringstid.
                  </p>
                </div>
              </div>
              
              <div className="group bg-white p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-blue-100 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-blue-50 group-hover:bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6 transition-colors duration-300">
                    <span className="text-3xl font-bold text-blue-600">3</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-800 mb-4 text-center transition-colors duration-300">
                    Udvikling & Levering
                  </h3>
                  <p className="text-gray-600 group-hover:text-gray-700 text-center leading-relaxed">
                    Efter din godkendelse starter vi udviklingen og leverer det færdige produkt inden for den aftalte tid.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Order Form */}
      <section className="py-10 sm:py-12 md:py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-2xl sm:text-3xl font-bold mb-6 sm:mb-8 text-center text-gray-800">Send en Forespørgsel</h2>
            
            <div className="bg-blue-50 p-4 sm:p-6 rounded-xl mb-6 sm:mb-8 text-center">
              <p className="text-gray-700 mb-4 text-sm sm:text-base">
                For at bestille et plugin eller skript, skal du tilslutte dig vores Discord server.
                Dette gør det lettere for os at kommunikere med dig om dit projekt.
              </p>
              <a 
                href="https://discord.mcdevhub.dk" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="inline-block bg-indigo-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-md font-medium hover:bg-indigo-700 transition-colors text-sm sm:text-base"
              >
                Tilslut Discord Server
              </a>
            </div>
            <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
              {submitted ? (
                <div className="p-6 sm:p-8 text-center">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h3 className="text-xl sm:text-2xl font-bold text-gray-800 mb-3 sm:mb-4">Tak for din forespørgsel!</h3>
                  <p className="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base">
                    Vi har modtaget din forespørgsel og vil svare dig snarest muligt med et tilbud.
                    Vi kontakter dig på Discord for at aftale de nærmere detaljer.
                  </p>
                  <button
                    onClick={() => setSubmitted(false)}
                    className="bg-blue-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-md font-medium hover:bg-blue-700 transition-colors text-sm sm:text-base"
                  >
                    Send ny forespørgsel
                  </button>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="p-5 sm:p-8">
                  {error && (
                    <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm sm:text-base">
                      {error}
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                    <div>
                      <label htmlFor="name" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Navn</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        placeholder="Dit fulde navn"
                        className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800 bg-white text-sm sm:text-base"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Email</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        placeholder="<EMAIL>"
                        className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800 bg-white text-sm sm:text-base"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="discordUsername" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Discord Navn</label>
                      <input
                        type="text"
                        id="discordUsername"
                        name="discordUsername"
                        value={formData.discordUsername}
                        onChange={handleChange}
                        required
                        placeholder="f.eks. myckasp"
                        className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800 bg-white text-sm sm:text-base"
                      />
                    </div>
                    <div>
                      <label htmlFor="serverType" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Server Type</label>
                      <input
                        type="text"
                        id="serverType"
                        name="serverType"
                        value={formData.serverType}
                        onChange={handleChange}
                        placeholder="f.eks. Spigot, Paper, etc."
                        className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800 bg-white text-sm sm:text-base"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="orderType" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Bestillingstype</label>
                      <select
                        id="orderType"
                        name="orderType"
                        value={formData.orderType}
                        onChange={handleChange}
                        className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800 bg-white text-sm sm:text-base"
                      >
                        <option value="plugin">Plugin</option>
                        <option value="skript">Skript</option>
                        <option value="build">Build/Map</option>
                        <option value="other">Andet</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="budget" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Budget (DKK)</label>
                      <input
                        type="text"
                        id="budget"
                        name="budget"
                        value={formData.budget}
                        onChange={handleChange}
                        placeholder="Valgfrit"
                        className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800 bg-white text-sm sm:text-base"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="deadline" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Deadline</label>
                      <div className="relative">
                        <input
                          type="text"
                          id="deadline"
                          name="deadline"
                          value={formData.deadline}
                          readOnly
                          onClick={() => setShowCalendar(true)}
                          placeholder="Vælg dato"
                          className="deadline-input w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-0 text-gray-800 bg-white cursor-pointer text-sm sm:text-base"
                        />
                        <div 
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-gray-500"
                          onClick={() => setShowCalendar(true)}
                        >
                          <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                      </div>
                      {showCalendar && (
                        <Calendar 
                          onSelectDate={handleDateSelect} 
                          onClose={() => setShowCalendar(false)}
                          minDays={5}
                        />
                      )}
                    </div>
                    
                    <div className="sm:col-span-2">
                      <label htmlFor="description" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Beskrivelse af Projektet</label>
                      <textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        required
                        rows={5}
                        className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800 bg-white text-sm sm:text-base"
                        placeholder="Beskriv dit projekt så detaljeret som muligt..."
                      ></textarea>
                    </div>
                    
                    <div className="sm:col-span-2">
                      <div className="flex items-start">
                        <input
                          type="checkbox"
                          id="discordConfirmation"
                          checked={discordChecked}
                          onChange={handleCheckboxChange}
                          className="mt-1 h-4 w-4 rounded border-gray-300 text-blue-600"
                        />
                        <label htmlFor="discordConfirmation" className="ml-2 block text-xs sm:text-sm text-gray-700">
                          Jeg bekræfter, at jeg har tilsluttet mig Discord serveren og mit Discord navn er korrekt.
                        </label>
                      </div>
                    </div>
                    
                    <div className="sm:col-span-2 mt-2 sm:mt-4">
                      <div className="flex items-start">
                        <input
                          type="checkbox"
                          id="tosConfirmation"
                          required
                          className="mt-1 h-4 w-4 rounded border-gray-300 text-blue-600"
                        />
                        <label htmlFor="tosConfirmation" className="ml-2 block text-xs sm:text-sm text-gray-700">
                          Ved at indsende denne formular accepterer jeg MCDevHub's {' '}
                          <Link href="/tos" className="text-blue-600 hover:underline" target="_blank">
                            brugsvilkår
                          </Link>{' '}
                          og{' '}
                          <Link href="/privatlivspolitik" className="text-blue-600 hover:underline" target="_blank">
                            privatlivspolitik
                          </Link>.
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-6 sm:mt-8">
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`w-full ${isSubmitting ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'} text-white py-2 sm:py-3 px-4 sm:px-6 rounded-md font-medium transition-colors flex items-center justify-center text-sm sm:text-base`}
                    >
                      {isSubmitting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 sm:mr-3 h-4 w-4 sm:h-5 sm:w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span>Sender...</span>
                        </>
                      ) : (
                        <span>Send Forespørgsel</span>
                      )}
                    </button>
                  </div>
                  
                  <p className="mt-4 text-xs sm:text-sm text-gray-600 text-center">
                    Vi svarer normalt inden for 1-2 arbejdsdage.
                  </p>
                </form>
              )}
            </div>
          </div>
        </div>
      </section>
      
      {/* FAQ */}
      <section className="py-10 sm:py-12 md:py-16">
        <div className="container mx-auto px-4 sm:px-6">
          <h2 className="text-2xl sm:text-3xl font-bold mb-8 sm:mb-12 text-center text-gray-800">Ofte Stillede Spørgsmål</h2>
          
          <div className="max-w-3xl mx-auto space-y-4 sm:space-y-6">
            <div className="bg-white rounded-xl shadow-md p-5 sm:p-6">
              <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-800">Hvor lang tid tager det at udvikle et plugin?</h3>
              <p className="text-gray-600 text-sm sm:text-base">
                Udviklingstiden varierer afhængigt af kompleksiteten af projektet. Simple plugins, skripts eller builds kan tage 1-3 dage, mens mere komplekse 
                løsninger kan tage 1-2 uger eller længere. Vi giver dig altid en estimeret leveringstid, når vi sender dig et tilbud.
              </p>
            </div>
            
            <div className="bg-white rounded-xl shadow-md p-5 sm:p-6">
              <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-800">Hvad koster et specialudviklet plugin?</h3>
              <p className="text-gray-600 text-sm sm:text-base">
                Prisen afhænger af projektets kompleksitet, omfang og eventuelle specifikke krav. Enkle skripts starter typisk fra omkring 35 DKK, 
                mens mere avancerede plugins kan koste fra 100 DKK og opefter. Vi giver dig et præcist prisoverslag efter vi har gennemgået dine krav.
              </p>
            </div>
            
            <div className="bg-white rounded-xl shadow-md p-5 sm:p-6">
              <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-800">Tilbyder I support efter levering?</h3>
              <p className="text-gray-600 text-sm sm:text-base">
                Ja, vi yder support efter levering for at sikre, at alt fungerer som forventet. Vi tilbyder også opdateringer og vedligeholdelse 
                efter behov. Detaljer om support vil være inkluderet i dit tilbud.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 