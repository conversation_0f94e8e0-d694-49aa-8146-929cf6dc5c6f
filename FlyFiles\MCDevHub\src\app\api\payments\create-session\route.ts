import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { auth } from '@/lib/auth';

// Initialize Stripe
const stripeKey = process.env.STRIPE_SECRET_KEY || '';
const stripe = new Stripe(stripeKey, {});

// Stripe API call to create checkout session
export async function POST(req: NextRequest) {
  try {
    // Get payment details from request body
    const { productId, paymentMethod, price } = await req.json();

    if (!productId) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // Connect to database to get product details
    const { db } = await connectToDatabase();
    
    // Try to find the product with different ID formats
    let product = null;
    
    // First try with string ID
    product = await db.collection('products').findOne({ _id: productId });
    
    // If not found and it's a valid ObjectId format, try with ObjectId
    if (!product && ObjectId.isValid(productId)) {
      product = await db.collection('products').findOne({ _id: new ObjectId(productId) });
    }
    
    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }
    
    // Get the current user from NextAuth
    const userSession = await auth();

    if (!userSession?.user) {
      return NextResponse.json({ error: 'User is not logged in' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = userSession.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Check if the user has already purchased this product
    const existingPurchase = await db.collection('user_purchases').findOne({
      userId: discordId,
      productId: productId
    });
    
    if (existingPurchase) {
      return NextResponse.json({ 
        error: 'Already purchased',
        message: 'You already own this product' 
      }, { status: 409 }); // 409 Conflict
    }

    // Create line items for the checkout
    let lineItems = [];
    
    // The amount to pay (convert to cents/øre for Stripe)
    const amount = Math.round(price * 100); // Ensure we're using the passed price
    
    lineItems.push({
      price_data: {
        currency: 'dkk',
        product_data: {
          name: product.projectName,
          description: `Køb af ${product.projectName} på MCDevHub`,
        },
        unit_amount: amount,
      },
      quantity: 1,
    });

    // Default to Stripe if no payment method is specified
    const paymentMethodTypes = paymentMethod === 'mobilepay' ? ['mobilepay'] : ['card'];
    
    // Create checkout session options
    const sessionOptions: Stripe.Checkout.SessionCreateParams = {
      payment_method_types: paymentMethodTypes as Stripe.Checkout.SessionCreateParams.PaymentMethodType[],
      line_items: lineItems,
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_BASE_URL}/payments/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/products/${productId}?payment_canceled=true`,
      metadata: {
        productId: product._id.toString(),
        customerId: discordId,
      },
    };

    // Create checkout session with Stripe
    const checkoutSession = await stripe.checkout.sessions.create(sessionOptions);

    return NextResponse.json({ url: checkoutSession.url });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json({
      error: 'Failed to create checkout session',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
} 