'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { FaArrowLeft, FaSpinner, FaSave, FaExclamationTriangle, FaImages, FaCheck, FaEdit, FaPlus, FaSync, FaTimes, FaTrash, FaUpload, FaFileUpload, FaFileAlt, FaPlug, FaPuzzlePiece, FaLink, FaTrashAlt, FaKeyboard, FaLongArrowAltRight, FaMousePointer } from 'react-icons/fa';
import { FaImage } from 'react-icons/fa6';
import { getImageUrl } from '@/lib/imageUtils';
import { AnimatePresence } from 'framer-motion';
import { motion } from 'framer-motion';

interface AdminUser {
  username: string;
  admintype: string;
  allowedcases: string;
  discordUserId?: string;
}

interface ProductFile {
  fileId: string;
  filename: string;
  contentType: string;
  url?: string;
  size?: number;
}

interface Addon {
  name: string;
  url?: string;
  version?: string;
  required: boolean;
  isDirectDownload?: boolean;
}

interface Product {
  _id: string;
  projectName: string;
  projectDescription: string;
  version: string;
  productType: string;
  price: number;
  discount?: number;
  createdBy: string;
  discordUserId?: string;
  createdAt: string;
  status: string;
  addons?: Addon[];
  screenshotUrls: Array<{
    fileId: string;
    filename: string;
    contentType: string;
    url?: string;
    publicId?: string;
  }>;
  fileUrls?: ProductFile[];
}

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  
  const [admin, setAdmin] = useState<AdminUser | null>(null);
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  // Add states for addon management
  const [showAddonSection, setShowAddonSection] = useState(false);
  const [showAddonForm, setShowAddonForm] = useState(false);
  const [currentAddon, setCurrentAddon] = useState<Addon>({
    name: '',
    url: '',
    version: '',
    required: false,
    isDirectDownload: false
  });
  const [editingAddonIndex, setEditingAddonIndex] = useState<number | null>(null);
  const [addonFormErrors, setAddonFormErrors] = useState({
    name: ''
  });
  const [addons, setAddons] = useState<Addon[]>([]);
  const [savingAddons, setSavingAddons] = useState(false);
  const [addonSuccess, setAddonSuccess] = useState(false);
  
  // File upload states
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [formErrors, setFormErrors] = useState({
    image: ''
  });
  // Add state for dimension error modal
  const [showDimensionErrorModal, setShowDimensionErrorModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [dimensionErrorMessage, setDimensionErrorMessage] = useState('');
  
  // Add states for editable fields
  const [editableField, setEditableField] = useState<string | null>(null);
  const [editableFields, setEditableFields] = useState({
    projectName: '',
    projectDescription: '',
    version: '',
    productType: 'Plugin',
    price: 0,
    isPremium: false,
  });

  // Prevent page scrolling when modal is open
  useEffect(() => {
    if (showDeleteModal) {
      document.body.style.overflow = 'hidden';
      document.documentElement.style.paddingRight = `${window.innerWidth - document.documentElement.clientWidth}px`;
      document.body.style.paddingRight = `${window.innerWidth - document.documentElement.clientWidth}px`;
    } else {
      document.body.style.overflow = 'unset';
      document.documentElement.style.paddingRight = '0';
      document.body.style.paddingRight = '0';
    }

    // Cleanup function to reset styles when component unmounts
    return () => {
      document.body.style.overflow = 'unset';
      document.documentElement.style.paddingRight = '0';
      document.body.style.paddingRight = '0';
    };
  }, [showDeleteModal]);
  const [editedValues, setEditedValues] = useState<{
    projectName: string;
    productType: string;
    version: string;
    price: number;
    discount: number;
    projectDescription: string;
  }>({
    projectName: '',
    productType: '',
    version: '',
    price: 0,
    discount: 0,
    projectDescription: ''
  });
  const [savingInfo, setSavingInfo] = useState(false);
  const [infoSuccess, setInfoSuccess] = useState(false);
  
  // State for multiple image uploads
  const [newImages, setNewImages] = useState<File[]>([]);
  const [newImagePreviews, setNewImagePreviews] = useState<string[]>([]);
  const [uploadingNewImages, setUploadingNewImages] = useState(false);
  
  // State for file management
  const [newFiles, setNewFiles] = useState<File[]>([]);
  const [uploadingFiles, setUploadingFiles] = useState(false);
  const [deletingFileId, setDeletingFileId] = useState<string | null>(null);
  
  // Track which images have replacements (preview only)
  const [replacedImages, setReplacedImages] = useState<{ [index: number]: string }>({});
  
  // Add a state for the final price input
  const [finalPriceInput, setFinalPriceInput] = useState<number>(0);
  
  // States for description editing history and undo functionality
  const [descriptionEditing, setDescriptionEditing] = useState(false);
  const [descriptionHistory, setDescriptionHistory] = useState<string[]>([]);
  const [descriptionBeforeAutosave, setDescriptionBeforeAutosave] = useState<string | null>(null);
  const [descriptionEditVersion, setDescriptionEditVersion] = useState(0);
  
  // Cleanup function for URLs created with createObjectURL
  const cleanupObjectUrls = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  };

  useEffect(() => {
    // Cleanup function when component unmounts or changes
    return () => {
      cleanupObjectUrls();
    };
  }, []);
  
  // Original useEffect for admin info - update to clean memory
  useEffect(() => {
    let isMounted = true;
    
    const getAdminInfo = async () => {
      try {
        const response = await fetch('/api/admin/me');
        
        // Check if component is still mounted before updating state
        if (!isMounted) return;
        
        if (!response.ok) {
          if (response.status === 401) {
            router.push('/admin/login');
            return;
          }
          throw new Error('Kunne ikke hente admin information');
        }
        
        const data = await response.json();
        setAdmin(data.user);
        
        // Ensure only freelancers can access this page
        if (data.user.admintype !== 'Freelancer') {
          router.push('/admin/dashboard');
        } else {
          // Fetch product details
          fetchProductDetails(id, data.user.username);
        }
      } catch (error) {
        if (isMounted) {
          console.error('Error fetching admin info:', error);
          setError('Kunne ikke hente admin information');
          router.push('/admin/login');
        }
      }
    };

    getAdminInfo();
    
    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [id, router]);
  
  // Handle image preview when an image is selected - optimize to prevent memory leaks
  useEffect(() => {
    // Cleanup previous preview URL to prevent memory leaks
    cleanupObjectUrls();
    
    // Only create new URL if there's a selected image
    if (selectedImage) {
      const newPreviewUrl = URL.createObjectURL(selectedImage);
      setPreviewUrl(newPreviewUrl);
      
      // Cleanup function to revoke object URL when effect runs again or component unmounts
      return () => {
        URL.revokeObjectURL(newPreviewUrl);
      };
    }
  }, [selectedImage]);
  
  // Update the fetchProductDetails function to handle addons
  const fetchProductDetails = async (productId: string, username: string) => {
    let isMounted = true;
    
    try {
      setLoading(true);
      const response = await fetch(`/api/products/${productId}`);
      
      // If component unmounted during fetch, don't update state
      if (!isMounted) return;
      
      if (!response.ok) {
        throw new Error('Kunne ikke hente produktet');
      }
      
      const data = await response.json();
      
      // Check if this admin is the owner of the product
      if (data.createdBy !== username) {
        setError('Du kan kun redigere dine egne produkter');
        return;
      }
      
      setProduct(data);
      // Initialize editable values
      setEditedValues({
        projectName: data.projectName,
        productType: data.productType,
        version: data.version,
        price: data.price,
        discount: data.discount || 0,
        projectDescription: data.projectDescription
      });
      
      // Initialize addons with isDirectDownload property
      const normalizedAddons = (data.addons || []).map((addon: Addon) => ({
        ...addon,
        isDirectDownload: typeof addon.isDirectDownload === 'boolean' ? addon.isDirectDownload : false
      }));
      
      setAddons(normalizedAddons);
      
      setLoading(false);
    } catch (error) {
      if (isMounted) {
        console.error('Error fetching product:', error);
        setError('Kunne ikke hente produktet');
        setLoading(false);
      }
    }
    
    return () => {
      isMounted = false;
    };
  };
  
  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    setFormErrors({ image: '' });
    setShowDimensionErrorModal(false);
    const file = files[0];
    if (!file.type.startsWith('image/')) {
      setFormErrors({ image: 'Filen skal være et billede' });
      // Reset file input so user can upload again
      e.target.value = '';
      return;
    }
    const img = new Image();
    const objectUrl = URL.createObjectURL(file);
    img.onload = () => {
      // Require exactly 1920x1080 resolution
      if (img.width === 1920 && img.height === 1080) {
        setReplacedImages(prev => ({ ...prev, [index]: objectUrl }));
        // Reset file input so user can upload again
        e.target.value = '';
      } else {
        URL.revokeObjectURL(objectUrl);
        const errorMessage = `Billedet skal have en opløsning på præcis 1920x1080 pixels. Dit billede er ${img.width}x${img.height}`;
        setFormErrors({ image: errorMessage });
        setDimensionErrorMessage(errorMessage);
        setShowDimensionErrorModal(true);
        // Reset file input so user can upload again
        e.target.value = '';
      }
    };
    img.onerror = () => {
      URL.revokeObjectURL(objectUrl);
      setFormErrors({ image: 'Fejl ved indlæsning af billedet' });
      // Reset file input so user can upload again
      e.target.value = '';
    };
    img.src = objectUrl;
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset error and success states
    setError(null);
    setSuccess(false);
    
    // Validate that we have an image selected
    if (!selectedImage || selectedImageIndex === null) {
      setFormErrors({
        image: 'Vælg et billede at erstatte'
      });
      return;
    }
    
    setSaving(true);
    
    try {
      // Create a FormData object to send files
      const formData = new FormData();
      
      // Add product ID
      formData.append('productId', id);
      
      // Add the image
      formData.append('image', selectedImage);
      
      // Add the index of the image being replaced
      formData.append('imageIndex', selectedImageIndex.toString());
      
      // Send the form data to the API
      const response = await fetch('/api/admin/products/update-images', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Der opstod en fejl ved opdatering af produktbillede');
      }
      
      setSuccess(true);
      
      // Reset state
      setSelectedImage(null);
      setPreviewUrl(null);
      setSelectedImageIndex(null);
      
      // Reload product data
      if (admin) {
        fetchProductDetails(id, admin.username);
      }
      
      // Scroll to top to show success message
      window.scrollTo(0, 0);
    } catch (error) {
      console.error('Error updating product image:', error);
      setError(error instanceof Error ? error.message : 'Der opstod en fejl ved opdatering af produktbillede');
    } finally {
      setSaving(false);
    }
  };
  
  // Handle editing field
  const handleEditField = (field: string) => {
    setEditableField(field);
  };

  // Handle saving edited field
  const handleSaveField = async () => {
    if (!editableField || !product) return;
    
    setSavingInfo(true);
    setError(null);
    setInfoSuccess(false);
    
    try {
      // Ensure we have valid values before submitting
      if (editableField === 'projectName') {
        if (!editedValues.projectName || editedValues.projectName.trim() === '') {
          throw new Error('Produktnavn kan ikke være tomt');
        }
      }
      
      if (editableField === 'version') {
        if (!editedValues.version || editedValues.version.trim() === '') {
          throw new Error('Version kan ikke være tom');
        }
      }
      
      if (editableField === 'price') {
        if (isNaN(editedValues.price) || editedValues.price < 0) {
          throw new Error('Pris skal være et positivt tal');
        }
      }

      if (editableField === 'projectDescription') {
        console.log('Updating product description:', editedValues.projectDescription);
      }
      
      // Create data to update with proper values
      const fieldValue = editableField === 'price' 
        ? Number(editedValues.price) 
        : editedValues[editableField as keyof typeof editedValues] || '';
      
      // Log what we're sending to help debug
      console.log('Sending update for field:', editableField, 'with value:', fieldValue);
      
      // Create update data
      const updateData = {
        productId: id,
        field: editableField,
        value: fieldValue
      };
      
      // Send update request
      const response = await fetch('/api/admin/products/update-info', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });
      
      // Get and log the response data for debugging
      const responseData = await response.json();
      console.log('API response:', responseData);
      
      if (!response.ok) {
        console.error('Server returned error:', responseData);
        throw new Error(responseData.message || 'Der opstod en fejl ved opdatering af produktinformation');
      }
      
      console.log('Update successful:', responseData);
      
      // Update local product state
      setProduct(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          [editableField]: editableField === 'price' 
            ? Number(editedValues.price) 
            : editedValues[editableField as keyof typeof editedValues]
        };
      });
      
      setInfoSuccess(true);
      
      // Clear editable field after a short delay
      setTimeout(() => {
        setEditableField(null);
        setInfoSuccess(false);
      }, 2000);
      
    } catch (error) {
      console.error('Error updating product info:', error);
      setError(error instanceof Error ? error.message : 'Der opstod en fejl ved opdatering af produktinformation');
    } finally {
      setSavingInfo(false);
    }
  };
  
  // Initialize edited values when product is loaded
  useEffect(() => {
    if (product) {
      const productDiscount = product.discount || 0;
      const calculatedFinalPrice = product.price > 0 
        ? Math.round(product.price * (1 - productDiscount / 100)) 
        : 0;
        
      setEditedValues({
        projectName: product.projectName,
        productType: product.productType,
        version: product.version,
        price: product.price,
        discount: productDiscount,
        projectDescription: product.projectDescription || ''
      });
      
      setFinalPriceInput(calculatedFinalPrice);
    }
  }, [product]);
  
  // Helper function to calculate discount percentage from final price
  const calculateDiscountPercentage = (originalPrice: number, finalPrice: number): number => {
    if (originalPrice <= 0 || finalPrice >= originalPrice) return 0;
    return Math.round(((originalPrice - finalPrice) / originalPrice) * 100);
  };
  
  // Helper function to calculate final price from discount percentage
  const calculateFinalPrice = (originalPrice: number, discountPercentage: number): number => {
    if (originalPrice <= 0 || discountPercentage <= 0) return originalPrice;
    return Math.round(originalPrice * (1 - discountPercentage / 100));
  };
  
  // Handle adding new images
  const handleNewImageSelect = (e: { target: { files: FileList | null } }) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    // Clear previous errors
    setFormErrors({ image: '' });
    
    // Process each file
    Array.from(files).forEach(file => {
      // Validate file is an image
      if (!file.type.startsWith('image/')) {
        setFormErrors({ image: 'Alle filer skal være billeder' });
        return;
      }
      
      // Create image to check dimensions
      const img = new Image();
      const objectUrl = URL.createObjectURL(file);
      
      img.onload = () => {
        // Check dimensions
        if (img.width === 1920 && img.height === 1080) {
          // Add to new images
          setNewImages(prev => [...prev, file]);
          setNewImagePreviews(prev => [...prev, objectUrl]);
        } else {
          URL.revokeObjectURL(objectUrl);
          const errorMessage = `Billedet "${file.name}" har forkerte dimensioner: ${img.width}x${img.height}. Skal være 1920x1080.`;
          setFormErrors({ image: errorMessage });
          setDimensionErrorMessage(errorMessage);
          setShowDimensionErrorModal(true);
        }
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(objectUrl);
        setFormErrors({ image: 'Fejl ved indlæsning af billedet' });
      };
      
      img.src = objectUrl;
    });
  };
  
  // Handle uploading new images
  const handleUploadNewImages = async () => {
    if (newImages.length === 0) return;
    
    setUploadingNewImages(true);
    setError(null);
    setSuccess(false);
    
    try {
      // Create FormData
      const formData = new FormData();
      
      // Add product ID
      formData.append('productId', id);
      
      // Add new flag to indicate these are additional images
      formData.append('isNewImages', 'true');
      
      // Add all new images
      newImages.forEach((image, i) => {
        formData.append(`image${i}`, image);
      });
      
      // Send request
      const response = await fetch('/api/admin/products/upload-additional-images', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Der opstod en fejl ved upload af nye billeder');
      }
      
      setSuccess(true);
      
      // Clear state
      setNewImages([]);
      setNewImagePreviews(prev => {
        // Revoke all URLs to prevent memory leaks
        prev.forEach(url => URL.revokeObjectURL(url));
        return [];
      });
      
      // Reload product data
      if (admin) {
        fetchProductDetails(id, admin.username);
      }
      
      // Scroll to top to show success message
      window.scrollTo(0, 0);
    } catch (error) {
      console.error('Error uploading new images:', error);
      setError(error instanceof Error ? error.message : 'Der opstod en fejl ved upload af nye billeder');
    } finally {
      setUploadingNewImages(false);
    }
  };
  
  // Handle file selection
  const handleFileSelect = (e: { target: { files: FileList | null } }) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setNewFiles(prev => [...prev, ...files]);
    }
  };
  
  // Handle file upload
  const handleUploadFiles = async () => {
    if (newFiles.length === 0) return;
    
    setUploadingFiles(true);
    setError(null);
    
    try {
      // Create FormData
      const formData = new FormData();
      
      // Add product ID
      formData.append('productId', id);
      
      // Add all files
      newFiles.forEach((file, i) => {
        formData.append(`file${i}`, file);
      });
      
      // Send request
      const response = await fetch('/api/admin/products/upload-files', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Der opstod en fejl ved upload af filer');
      }
      
      setSuccess(true);
      
      // Clear state
      setNewFiles([]);
      
      // Reload product data
      if (admin) {
        fetchProductDetails(id, admin.username);
      }
      
      // Scroll to top to show success message
      window.scrollTo(0, 0);
    } catch (error) {
      console.error('Error uploading files:', error);
      setError(error instanceof Error ? error.message : 'Der opstod en fejl ved upload af filer');
    } finally {
      setUploadingFiles(false);
    }
  };
  
  // Handle deleting a file
  const handleDeleteFile = async (fileId: string) => {
    if (!fileId) return;
    
    setDeletingFileId(fileId);
    setError(null);
    
    try {
      // Send delete request
      const response = await fetch(`/api/admin/products/delete-file`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productId: id,
          fileId
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Der opstod en fejl ved sletning af filen');
      }
      
      // Update local state to remove the deleted file
      setProduct(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          fileUrls: prev.fileUrls?.filter(file => file.fileId !== fileId) || []
        };
      });
      
      // Reload product data
      if (admin) {
        fetchProductDetails(id, admin.username);
      }
    } catch (error) {
      console.error('Error deleting file:', error);
      setError(error instanceof Error ? error.message : 'Der opstod en fejl ved sletning af filen');
    } finally {
      setDeletingFileId(null);
    }
  };
  
  // Remove a replacement preview for a specific image
  const handleCancelReplacement = (index: number) => {
    setReplacedImages(prev => {
      // Clean up object URL
      if (prev[index]) URL.revokeObjectURL(prev[index]);
      const copy = { ...prev };
      delete copy[index];
      return copy;
    });
    // Reset file input so user can upload again
    const input = document.getElementById(`replace-input-${index}`) as HTMLInputElement | null;
    if (input) input.value = '';
  };

  // Save a single replacement image
  const handleSaveSingleReplacement = async (index: number) => {
    // TODO: Implement upload logic for only replacedImages[index]
    alert(`Gemmer ændring for billede #${index + 1} (implementér upload til backend her)`);
  };

  // Add scroll-lock effect for loading overlay
  useEffect(() => {
    if (loading) {
      document.body.style.overflow = 'hidden';
      window.scrollTo(0, 0);
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [loading]);

  // Diagnostic function to check product schema
  const checkProductSchema = async () => {
    try {
      const response = await fetch(`/api/admin/products/test-schema?id=${id}`);
      const data = await response.json();
      console.log('Product schema check:', data);
      
      if (data.product) {
        // Alert the available fields to make debugging easier for admins
        alert(
          `Available fields in product: ${data.keys.join(', ')}\n\n` +
          `Description field exists as: ${
            data.product.description ? 'description' :
            data.product.projectDescription ? 'projectDescription' : 
            'Neither field exists'
          }`
        );
      }
    } catch (error) {
      console.error('Error checking product schema:', error);
      alert('Failed to check product schema');
    }
  };

  // Add functions for addon management
  const handleAddonChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setCurrentAddon(prev => ({
        ...prev,
        [name]: checkbox.checked
      }));
    } else {
      setCurrentAddon(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    // Clear error when typing
    if (name === 'name') {
      setAddonFormErrors(prev => ({
        ...prev,
        name: ''
      }));
    }
  };
  
  const handleSaveAddon = () => {
    // Validate
    if (!currentAddon.name.trim()) {
      setAddonFormErrors(prev => ({
        ...prev,
        name: 'Navn er påkrævet'
      }));
      return;
    }
    
    // Check if product exists
    if (!product) {
      console.error("Cannot save addon: Product is undefined");
      setError('Kunne ikke gemme addon: Produkt ikke fundet');
      return;
    }
    
    let updatedAddons = [...addons];
    
    // Check if editing existing or adding new
    if (editingAddonIndex !== null) {
      // Update existing addon
      updatedAddons[editingAddonIndex] = currentAddon;
    } else {
      // Add new addon
      updatedAddons = [...updatedAddons, currentAddon];
    }
    
    // Store the updated addons for the API call
    const addonsToSave = [...updatedAddons];
    
    // Update local addons state
    setAddons(updatedAddons);
    
    // Reset form
    setCurrentAddon({
      name: '',
      url: '',
      version: '',
      required: false,
      isDirectDownload: false
    });
    setEditingAddonIndex(null);
    setShowAddonForm(false);

    // Automatically save changes to database using the local copy
    console.log("Auto-saving addons:", addonsToSave);
    
    // Directly call the API instead of using handleSaveAllAddons
    saveAddonsToAPI(addonsToSave);
  };
  
  // Helper function to save addons directly to API
  const saveAddonsToAPI = async (addonsToSave) => {
    if (!product) {
      console.error("Cannot save addons: Product is undefined");
      return;
    }
    
    setSavingAddons(true);
    setAddonSuccess(false);
    
    try {
      console.log("Making API call to save addons:", addonsToSave);
      
      const response = await fetch(`/api/admin/products/update-addons`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: product._id,
          addons: addonsToSave
        }),
      });
      
      console.log("API response status:", response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("API error response:", errorText);
        throw new Error('Kunne ikke gemme addons');
      }
      
      const responseData = await response.json();
      console.log("API success response:", responseData);
      
      setAddonSuccess(true);
      
      // Update product with new addons
      setProduct(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          addons: addonsToSave
        };
      });
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setAddonSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving addons:', error);
      setError('Kunne ikke gemme addons');
    } finally {
      setSavingAddons(false);
    }
  };
  
  const handleEditAddon = (index: number) => {
    // Ensure all properties including isDirectDownload are defined
    const addonToEdit = addons[index];
    setCurrentAddon({
      name: addonToEdit.name,
      url: addonToEdit.url || '',
      version: addonToEdit.version || '',
      required: Boolean(addonToEdit.required),
      isDirectDownload: typeof addonToEdit.isDirectDownload === 'boolean' ? addonToEdit.isDirectDownload : false
    });
    setEditingAddonIndex(index);
    setShowAddonForm(true);
  };
  
  const handleRemoveAddon = (index: number) => {
    // Check if product exists
    if (!product) {
      console.error("Cannot remove addon: Product is undefined");
      setError('Kunne ikke fjerne addon: Produkt ikke fundet');
      return;
    }
    
    const updatedAddons = [...addons];
    updatedAddons.splice(index, 1);
    
    // Store the updated addons for the API call
    const addonsToSave = [...updatedAddons];
    
    // Update local state
    setAddons(updatedAddons);
    
    // Directly save changes to database
    console.log("Auto-saving after addon removal:", addonsToSave);
    saveAddonsToAPI(addonsToSave);
  };
  
  const handleSaveAllAddons = async () => {
    if (!product) return;
    
    setSavingAddons(true);
    setAddonSuccess(false);
    
    try {
      const response = await fetch(`/api/admin/products/update-addons`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: product._id,
          addons
        }),
      });
      
      if (!response.ok) {
        throw new Error('Kunne ikke gemme addons');
      }
      
      setAddonSuccess(true);
      
      // Update product with new addons
      if (product) {
        setProduct({
          ...product,
          addons
        });
      }
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setAddonSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving addons:', error);
      setError('Kunne ikke gemme addons');
    } finally {
      setSavingAddons(false);
    }
  };

  // Handle description undo
  const handleDescriptionUndo = () => {
    // Get editor reference for direct DOM manipulation
    const editor = document.getElementById('inline-description-editor') as HTMLElement;
    if (!editor) return;
    
    if (descriptionEditVersion > 0) {
      // Move back one step in history
      const newVersion = descriptionEditVersion - 1;
      setDescriptionEditVersion(newVersion);
      
      // Update editor content with the previous version
      editor.innerText = descriptionHistory[newVersion];
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90 backdrop-blur-sm">
        <div className="flex flex-col items-center max-w-sm mx-auto p-8 rounded-xl bg-white shadow-lg border border-gray-100">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mb-6" style={{ animationDuration: '0.4s' }}></div>
          <div className="text-blue-700 text-lg font-medium">Indlæser produkt...</div>
          <p className="text-gray-500 text-sm mt-2 text-center">Vent venligst mens vi henter produktinformation</p>
        </div>
      </div>
    );
  }

  if (error && !product) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 p-6 rounded-lg text-center">
            <FaExclamationTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Fejl ved indlæsning af produkt</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link href="/admin/products/my-products" className="inline-flex items-center text-blue-600 hover:underline">
              <FaArrowLeft className="mr-2" />
              Tilbage til mine produkter
            </Link>
          </div>
        </div>
      </div>
    );
  }
  
  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-gray-100 border border-gray-200 p-6 rounded-lg text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Produkt ikke fundet</h2>
            <p className="text-gray-600 mb-4">Vi kunne ikke finde det ønskede produkt.</p>
            <Link href="/admin/products/my-products" className="inline-flex items-center text-blue-600 hover:underline">
              <FaArrowLeft className="mr-2" />
              Tilbage til mine produkter
            </Link>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50 pt-20 px-4 pb-12">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <Link
            href="/admin/products/my-products"
            className="inline-flex items-center text-gray-600 hover:text-blue-600 transition-colors group"
          >
            <FaArrowLeft className="mr-2 transition-transform duration-300 ease-[cubic-bezier(0.25,0.1,0.25,1)] group-hover:-translate-x-1.5" />
            <span>Tilbage til mine produkter</span>
          </Link>
          {/* Delete Product Button */}
          <button
            type="button"
            className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors font-semibold shadow-sm"
            onClick={() => setShowDeleteModal(true)}
          >
            <FaTrash className="mr-2" />
            Slet produkt
          </button>
        </div>
        
        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
              <div 
                className="fixed inset-0 backdrop-blur-sm bg-black/30 transition-opacity" 
                aria-hidden="true" 
                onClick={() => !isDeleting && setShowDeleteModal(false)}
                style={{
                  backdropFilter: 'blur(4px)',
                  WebkitBackdropFilter: 'blur(4px)'
                }}
              ></div>
              <div className="inline-block bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                      <FaExclamationTriangle className="h-6 w-6 text-red-600" aria-hidden="true" />
                    </div>
                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                      <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                        Slet produkt
                      </h3>
                      <div className="mt-2">
                        <p className="text-sm text-gray-500">
                          Er du sikker på, at du vil slette dette produkt? Denne handling kan ikke fortrydes.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={async () => {
                      try {
                        setIsDeleting(true);
                        const res = await fetch(`/api/admin/products/delete/${id}`, {
                          method: 'DELETE'
                        });
                        if (res.ok) {
                          window.location.href = '/admin/products/my-products';
                        } else {
                          const data = await res.json();
                          alert(data.message || 'Kunne ikke slette produktet');
                          setShowDeleteModal(false);
                        }
                      } catch (err) {
                        console.error('Delete error:', err);
                        alert('Der opstod en fejl ved sletning af produktet');
                        setShowDeleteModal(false);
                      } finally {
                        setIsDeleting(false);
                      }
                    }}
                    disabled={isDeleting}
                  >
                    {isDeleting ? (
                      <>
                        <FaSpinner className="animate-spin -ml-1 mr-2 h-4 w-4" />
                        Sletter...
                      </>
                    ) : 'Slet'}
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    onClick={() => setShowDeleteModal(false)}
                    disabled={isDeleting}
                  >
                    Annuller
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Dimension Error Modal */}
        {showDimensionErrorModal && (
          <div className="fixed inset-0 z-50 overflow-y-auto backdrop-blur-sm" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
              <style jsx global>{`
                body {
                  overflow: hidden;
                  height: 100%;
                  padding-right: ${window.innerWidth - document.documentElement.clientWidth}px;
                }
              `}</style>
              <div 
                className="fixed inset-0 bg-gray-500/50 backdrop-blur-sm transition-opacity" 
                aria-hidden="true" 
                onClick={() => {
                  setShowDimensionErrorModal(false);
                }}
              ></div>
              <div className="inline-block align-bottom bg-white rounded-lg shadow-xl max-w-md w-full p-6 relative transform transition-all">
                <button
                  type="button"
                  onClick={() => {
                    setShowDimensionErrorModal(false);
                  }}
                  className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
                >
                  <FaTimes className="h-5 w-5" />
                </button>
                
                <h3 className="text-lg font-bold text-gray-900 mb-4">
                  {dimensionErrorMessage}
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-700">
                      {dimensionErrorMessage}
                    </p>
                  </div>
                  
                  <div className="pt-4">
                    <button
                      type="button"
                      onClick={() => {
                        setShowDimensionErrorModal(false);
                      }}
                      className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Forstået
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Page Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl shadow-2xl p-8 mb-8">
          <div className="max-w-2xl mx-auto text-center">
            <h1 className="text-3xl font-bold text-white flex items-center justify-center">
              <FaEdit className="mr-3" />
              Rediger Produkt: {product.projectName}
            </h1>
            <p className="text-blue-100 mt-3 text-lg">
              Opdater billeder og information for dit produkt
            </p>
          </div>
        </div>
        
        {/* Status Messages */}
        <div className="space-y-4 mb-8">
          {success && (
            <div className="bg-green-50 border border-green-200 rounded-xl p-5 flex items-start space-x-4">
              <FaCheck className="text-green-500 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-green-800 font-medium text-lg">Billeder opdateret!</h3>
                <p className="text-green-700 mt-1">
                  Dine produktbilleder er blevet opdateret.
                </p>
              </div>
            </div>
          )}
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-5 flex items-start space-x-4">
              <FaExclamationTriangle className="text-red-500 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-red-800 font-medium text-lg">Fejl ved opdatering</h3>
                <p className="text-red-700 mt-1">{error}</p>
              </div>
            </div>
          )}
        </div>
        
        {/* Product Information */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 border-b pb-4">
            Produktinformation
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="space-y-2">
              <p className="text-sm text-gray-500 uppercase tracking-wider">Navn</p>
              <div className="flex items-center gap-2">
                {editableField === 'projectName' ? (
                  <div className="flex-1">
                    <input
                      type="text"
                      value={editedValues.projectName}
                      onChange={(e) => setEditedValues({...editedValues, projectName: e.target.value})}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      autoFocus
                    />
                    <div className="flex space-x-2 mt-2">
                      <button
                        onClick={handleSaveField}
                        disabled={savingInfo}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                      >
                        {savingInfo ? <FaSpinner className="animate-spin mr-1" /> : <FaCheck className="mr-1" />}
                        Gem
                      </button>
                      <button
                        onClick={() => {
                          setEditableField(null);
                          setEditedValues({...editedValues, projectName: product?.projectName || ''});
                        }}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
                      >
                        <FaTimes className="mr-1" />
                        Annuller
                      </button>
                    </div>
                  </div>
                ) : (
                  <>
                    <p className="font-semibold text-gray-900 text-lg">{product?.projectName}</p>
                    <button 
                      onClick={() => handleEditField('projectName')} 
                      className="text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      <FaEdit className="w-4 h-4" />
                    </button>
                  </>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-500 uppercase tracking-wider">Type</p>
              <div className="flex items-center gap-2">
                {editableField === 'productType' ? (
                  <div className="flex-1">
                    <select
                      value={editedValues.productType}
                      onChange={(e) => setEditedValues({...editedValues, productType: e.target.value})}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      autoFocus
                    >
                      <option value="plugin">Plugin</option>
                      <option value="skript">Skript</option>
                      <option value="map">Map</option>
                      <option value="resourcepack">Resourcepack</option>
                      <option value="other">Andet</option>
                    </select>
                    <div className="flex space-x-2 mt-2">
                      <button
                        onClick={handleSaveField}
                        disabled={savingInfo}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                      >
                        {savingInfo ? <FaSpinner className="animate-spin mr-1" /> : <FaCheck className="mr-1" />}
                        Gem
                      </button>
                      <button
                        onClick={() => {
                          setEditableField(null);
                          setEditedValues({...editedValues, productType: product?.productType || ''});
                        }}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
                      >
                        <FaTimes className="mr-1" />
                        Annuller
                      </button>
                    </div>
                  </div>
                ) : (
                  <>
                    <p className="font-semibold text-gray-900 text-lg">
                      {product?.productType ? 
                        product.productType.charAt(0).toUpperCase() + product.productType.slice(1) 
                        : ''
                      }
                    </p>
                    <button 
                      onClick={() => handleEditField('productType')} 
                      className="text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      <FaEdit className="w-4 h-4" />
                    </button>
                  </>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-500 uppercase tracking-wider">Version</p>
              <div className="flex items-center gap-2">
                {editableField === 'version' ? (
                  <div className="flex-1">
                    <input
                      type="text"
                      value={editedValues.version}
                      onChange={(e) => setEditedValues({...editedValues, version: e.target.value})}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      autoFocus
                    />
                    <div className="flex space-x-2 mt-2">
                      <button
                        onClick={handleSaveField}
                        disabled={savingInfo}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                      >
                        {savingInfo ? <FaSpinner className="animate-spin mr-1" /> : <FaCheck className="mr-1" />}
                        Gem
                      </button>
                      <button
                        onClick={() => {
                          setEditableField(null);
                          setEditedValues({...editedValues, version: product?.version || ''});
                        }}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
                      >
                        <FaTimes className="mr-1" />
                        Annuller
                      </button>
                    </div>
                  </div>
                ) : (
                  <>
                    <p className="font-semibold text-gray-900 text-lg">{product?.version}</p>
                    <button 
                      onClick={() => handleEditField('version')} 
                      className="text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      <FaEdit className="w-4 h-4" />
                    </button>
                  </>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-500 uppercase tracking-wider">Pris</p>
              <div className="flex items-center gap-2">
                {editableField === 'price' ? (
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Oprindelig pris
                    </label>
                    <div className="flex items-center">
                    <input
                      type="number"
                      min="0"
                      step="1"
                      value={editedValues.price}
                        onChange={(e) => {
                          const newPrice = parseInt(e.target.value) || 0;
                          setEditedValues({...editedValues, price: newPrice});
                          
                          // Update final price when original price changes (maintain the same discount %)
                          const newFinalPrice = calculateFinalPrice(newPrice, editedValues.discount);
                          setFinalPriceInput(newFinalPrice);
                        }}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      autoFocus
                    />
                      <span className="ml-2 text-gray-500">DKK</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Angiv produktets ursprungelige pris
                    </p>
                    <div className="flex space-x-2 mt-4">
                      <button
                        onClick={handleSaveField}
                        disabled={savingInfo}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                      >
                        {savingInfo ? <FaSpinner className="animate-spin mr-1" /> : <FaCheck className="mr-1" />}
                        Gem
                      </button>
                      <button
                        onClick={() => {
                          setEditableField(null);
                          setEditedValues({...editedValues, price: product?.price || 0});
                          
                          // Reset final price
                          const originalPrice = product?.price || 0;
                          const originalDiscount = product?.discount || 0;
                          const calculatedFinalPrice = calculateFinalPrice(originalPrice, originalDiscount);
                          setFinalPriceInput(calculatedFinalPrice);
                        }}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
                      >
                        <FaTimes className="mr-1" />
                        Annuller
                      </button>
                    </div>
                  </div>
                ) : (
                  <>
                    <p className="font-semibold text-gray-900 text-lg">
                      {product?.price === 0 ? 'Gratis' : `${product?.price} DKK`}
                    </p>
                    <button 
                      onClick={() => handleEditField('price')} 
                      className="text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      <FaEdit className="w-4 h-4" />
                    </button>
                  </>
                )}
            </div>
          </div>
          
            {/* Add discount field */}
            <div className="space-y-2">
              <p className="text-sm text-gray-500 uppercase tracking-wider">Rabat</p>
              <div className="flex items-center gap-2">
                {editableField === 'discount' ? (
                  <div className="flex-1">
                    <div className="space-y-4">
                      {/* Discount percentage input */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Rabat procentvis
                        </label>
                        <div className="flex items-center">
                          <input
                            type="number"
                            min="0"
                            max="100"
                            step="1"
                            value={editedValues.discount}
                            onChange={(e) => {
                              const value = parseInt(e.target.value) || 0;
                              // Ensure discount is between 0 and 100
                              const limitedValue = Math.min(100, Math.max(0, value));
                              setEditedValues({...editedValues, discount: limitedValue});
                              
                              // Update final price when discount changes
                              const newFinalPrice = calculateFinalPrice(editedValues.price, limitedValue);
                              setFinalPriceInput(newFinalPrice);
                            }}
                            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                          />
                          <span className="ml-2 text-gray-500">%</span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Angiv rabat i procent (0-100%)
                        </p>
                      </div>
                      
                      {/* Horizontal divider with "or" text */}
            <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <div className="w-full border-t border-gray-200"></div>
                        </div>
                        <div className="relative flex justify-center">
                          <span className="bg-white px-2 text-sm text-gray-500">
                            eller
                          </span>
                        </div>
                      </div>
                      
                      {/* Final price input */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Slutpris
                        </label>
                        <div className="flex items-center">
                          <input
                            type="number"
                            min="0"
                            max={editedValues.price}
                            step="1"
                            value={finalPriceInput}
                            onChange={(e) => {
                              const value = parseInt(e.target.value) || 0;
                              // Ensure final price is not higher than original price
                              const limitedValue = Math.min(editedValues.price, Math.max(0, value));
                              setFinalPriceInput(limitedValue);
                              
                              // Update discount when final price changes
                              const newDiscount = calculateDiscountPercentage(editedValues.price, limitedValue);
                              setEditedValues({...editedValues, discount: newDiscount});
                            }}
                            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            disabled={editedValues.price <= 0}
                          />
                          <span className="ml-2 text-gray-500">DKK</span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Angiv den endelige pris efter rabat
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 mt-4">
                    <button
                      onClick={handleSaveField}
                      disabled={savingInfo}
                      className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                    >
                      {savingInfo ? <FaSpinner className="animate-spin mr-1" /> : <FaCheck className="mr-1" />}
                      Gem
                    </button>
                    <button
                      onClick={() => {
                        setEditableField(null);
                          const originalDiscount = product?.discount || 0;
                          setEditedValues({...editedValues, discount: originalDiscount});
                          const calculatedFinalPrice = product?.price ? 
                            calculateFinalPrice(product.price, originalDiscount) : 0;
                          setFinalPriceInput(calculatedFinalPrice);
                      }}
                      className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
                    >
                      <FaTimes className="mr-1" />
                      Annuller
                    </button>
                  </div>
                </div>
              ) : (
                  <>
                    <p className="font-semibold text-gray-900 text-lg flex items-center">
                      {product?.discount ? (
                        <>
                          <span className="text-amber-600">{product.discount}%</span>
                          {product.price > 0 && product.discount > 0 && (
                            <span className="ml-2 text-sm text-gray-500">
                              (Slutpris: {Math.round(product.price * (1 - product.discount / 100))} DKK)
                            </span>
                          )}
                        </>
                      ) : 'Ingen rabat'}
                    </p>
                  <button 
                      onClick={() => handleEditField('discount')} 
                      className="text-blue-600 hover:text-blue-700 transition-colors"
                  >
                    <FaEdit className="w-4 h-4" />
                  </button>
                  </>
                )}
                      </div>
            </div>
          </div>
          
          {/* Product Description */}
          <div className="mt-6">
            <p className="text-sm text-gray-500 uppercase tracking-wider mb-2 cursor-default">
              Beskrivelse
            </p>
            <div className="relative">
              <div 
                className="bg-gray-50 p-4 rounded-lg prose max-w-none"
                style={{ whiteSpace: 'pre-wrap' }}
              >
                <div
                  id="description-container"
                  className={`${descriptionEditing ? 'cursor-text' : 'cursor-pointer'} min-h-[200px] text-black relative group`}
                  onClick={(e) => {
                    // Only trigger if the container itself is clicked and not buttons
                    if (!descriptionEditing && (e.currentTarget === e.target || (e.target as HTMLElement).id === 'description-text-display')) {
                      // Get the editable div and focus it
                      const editor = document.getElementById('inline-description-editor');
                      const display = document.getElementById('description-text-display');
                      const buttonsEl = document.getElementById('description-edit-buttons');
                      
                      if (editor && display && buttonsEl) {
                        // Hide display, show editor
                        display.style.display = 'none';
                        editor.style.display = 'block';
                        buttonsEl.style.display = 'flex';
                        
                        // Focus the editor
                        editor.focus();
                        
                        // Position the cursor at the click position
                        const selection = window.getSelection();
                        const range = document.createRange();
                        
                        try {
                          // If the click is in the display element, try to determine exact position
                          if (e.target && e.target === display || (e.target as HTMLElement).id === 'description-text-display') {
                            // Create a range from click position
                            if (document.caretPositionFromPoint) {
                              // Firefox
                              const position = document.caretPositionFromPoint(e.clientX, e.clientY);
                              if (position) {
                                range.setStart(position.offsetNode, position.offset);
                              }
                            } else if ((document as any).caretRangeFromPoint) {
                              // Chrome
                              const position = (document as any).caretRangeFromPoint(e.clientX, e.clientY);
                              if (position) {
                                range.setStart(position.startContainer, position.startOffset);
                              }
                            }
                          }
                          
                          // If no specific position was determined, move existing text to editor and place cursor at end
                          if (!range.startContainer || range.startContainer.nodeType !== Node.TEXT_NODE) {
                            // Get editor text node (or create one if it doesn't exist)
                            if (editor.firstChild && editor.firstChild.nodeType === Node.TEXT_NODE) {
                              range.selectNodeContents(editor.firstChild);
                              range.collapse(false); // collapse to end
                            } else {
                              // If no text node exists yet, just set cursor at beginning
                              range.selectNodeContents(editor);
                              range.collapse(true);
                            }
                          }
                          
                          // Apply the selection
                          if (selection) {
                            selection.removeAllRanges();
                            selection.addRange(range);
                          }
                        } catch (err) {
                          console.error("Error positioning cursor:", err);
                          // Fallback to default focus
                          editor.focus();
                        }
                        
                        // Set up initial history state when beginning edit
                        const initialContent = editedValues.projectDescription || product?.projectDescription || '';
                        setDescriptionHistory([initialContent]);
                        setDescriptionEditVersion(0);
                        setDescriptionBeforeAutosave(initialContent);
                        
                        // Set editing mode
                        setDescriptionEditing(true);
                      }
                    }
                  }}
                >
                  {/* Center "click to edit" tooltip - only show when not editing */}
                  {!descriptionEditing && (
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                      <span className="text-sm text-blue-600 bg-blue-50 px-3 py-1.5 rounded-md shadow-sm">
                        Klik for at redigere
                      </span>
                    </div>
                  )}
                  
                  {/* Display text */}
                  <div id="description-text-display" className="min-h-[1em] p-2">
                    {editedValues.projectDescription || product?.projectDescription || (
                      <span className="text-gray-400 italic">
                        Ingen beskrivelse tilføjet. Klik for at tilføje en beskrivelse.
                      </span>
                    )}
                  </div>
                  
                  {/* Editable content - initially hidden */}
                  <div
                    contentEditable
                    id="inline-description-editor"
                    className="bg-white shadow-inner border-0 hover:border-0 focus:outline-none focus:ring-0 p-2 rounded-md hidden"
                    style={{
                      minHeight: '200px',
                      outline: 'none',
                      border: 'none',
                      boxShadow: 'none',
                      WebkitAppearance: 'none',
                      MozAppearance: 'none',
                      appearance: 'none'
                    }}
                    onFocus={(e) => {
                      // Remove outline on focus for all browsers
                      e.currentTarget.style.outline = 'none';
                      e.currentTarget.style.border = 'none';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                    onInput={(e) => {
                      // Add to history when content changes
                      const newContent = e.currentTarget.innerText;
                      
                      // Only add to history if content changed
                      if (descriptionHistory[descriptionEditVersion] !== newContent) {
                        // Truncate history if we're in the middle of undo history
                        const newHistory = descriptionHistory.slice(0, descriptionEditVersion + 1);
                        // Add new version
                        newHistory.push(newContent);
                        setDescriptionHistory(newHistory);
                        setDescriptionEditVersion(newHistory.length - 1);
                      }
                    }}
                    onBlur={(e) => {
                      // Don't hide if clicked on buttons
                      const relatedTarget = e.relatedTarget as HTMLElement;
                      if (relatedTarget && 
                          (relatedTarget.id === 'description-undo-btn' || 
                           relatedTarget.id === 'description-restore-btn' ||
                           relatedTarget.closest('#description-undo-btn') ||
                           relatedTarget.closest('#description-restore-btn'))) {
                        return;
                      }
                      
                      // Get references to elements
                      const editor = e.currentTarget;
                      const display = document.getElementById('description-text-display');
                      const buttonsEl = document.getElementById('description-edit-buttons');
                      
                      // Commit changes
                      const newDescription = editor.innerText || '';
                      
                      // Store the state before saving for potential restore
                      setDescriptionBeforeAutosave(descriptionHistory[0]);
                      
                      if (newDescription !== editedValues.projectDescription) {
                        setEditedValues({...editedValues, projectDescription: newDescription});
                        
                        // Call API to save
                        const saveDescription = async () => {
                          setSavingInfo(true);
                          try {
                            // Create update data
                            const updateData = {
                              productId: id,
                              field: 'projectDescription',
                              value: newDescription
                            };
                            
                            // Send update request
                            const response = await fetch('/api/admin/products/update-info', {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json'
                              },
                              body: JSON.stringify(updateData)
                            });
                            
                            if (!response.ok) {
                              throw new Error('Der opstod en fejl ved opdatering af produktbeskrivelsen');
                            }
                            
                            // Update product description to match what was saved
                            setProduct(prev => {
                              if (!prev) return prev;
                              return {
                                ...prev,
                                projectDescription: newDescription
                              };
                            });
                            
                            setInfoSuccess(true);
                            setTimeout(() => {
                              setInfoSuccess(false);
                            }, 2000);
                            
                          } catch (error) {
                            console.error('Error updating description:', error);
                            setError(error instanceof Error ? error.message : 'Der opstod en fejl ved opdatering af produktbeskrivelsen');
                          } finally {
                            setSavingInfo(false);
                          }
                        };
                        
                        saveDescription();
                      }
                      
                      // Hide editor and show display text again
                      if (display) {
                        editor.style.display = 'none';
                        display.style.display = 'block';
                        
                        // If there's content, show it, otherwise show placeholder
                        if (newDescription) {
                          display.innerHTML = newDescription;
                        } else {
                          display.innerHTML = '<span class="text-gray-400 italic">Ingen beskrivelse tilføjet. Klik for at tilføje en beskrivelse.</span>';
                        }
                      }
                      
                      // Hide buttons
                      if (buttonsEl) {
                        buttonsEl.style.display = 'none';
                      }
                      
                      // End editing mode
                      setDescriptionEditing(false);
                    }}
                    onKeyDown={(e) => {
                      // Handle CTRL+Z / CMD+Z for undo
                      if ((e.key === 'z' || e.key === 'Z') && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        handleDescriptionUndo();
                      }
                      
                      // Allow common keyboard shortcuts
                      if ((e.key === 's' || e.key === 'S') && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        e.currentTarget.blur(); // Save by triggering blur
                      }
                      
                      // Escape key to cancel
                      if (e.key === 'Escape') {
                        e.preventDefault();
                        
                        const editor = e.currentTarget;
                        const display = document.getElementById('description-text-display');
                        const buttonsEl = document.getElementById('description-edit-buttons');
                        
                        // Reset to initial state
                        const initialContent = descriptionHistory[0] || '';
                        editor.innerText = initialContent;
                        
                        // Hide editor, show display
                        if (display) {
                          editor.style.display = 'none';
                          display.style.display = 'block';
                          
                          // If there's content, show it, otherwise show placeholder
                          if (initialContent) {
                            display.innerHTML = initialContent;
                          } else {
                            display.innerHTML = '<span class="text-gray-400 italic">Ingen beskrivelse tilføjet. Klik for at tilføje en beskrivelse.</span>';
                          }
                        }
                        
                        // Hide buttons
                        if (buttonsEl) {
                          buttonsEl.style.display = 'none';
                        }
                        
                        // End editing mode
                        setDescriptionEditing(false);
                      }
                    }}
                    suppressContentEditableWarning={true}
                  >
                    {editedValues.projectDescription || product?.projectDescription || ''}
                  </div>
                  
                  {/* Edit buttons - initially hidden */}
                  <div 
                    id="description-edit-buttons" 
                    className="hidden mt-3 space-x-2 justify-end"
                  >
                    <button
                      id="description-restore-btn"
                      className="px-3 py-1.5 text-sm bg-yellow-50 text-yellow-700 border border-yellow-200 rounded-md hover:bg-yellow-100 transition-colors flex items-center"
                      onClick={() => {
                        // Restore to state before last autosave
                        if (descriptionBeforeAutosave !== null) {
                          const editor = document.getElementById('inline-description-editor') as HTMLElement;
                          if (editor) {
                            editor.innerText = descriptionBeforeAutosave;
                            
                            // Update history
                            setDescriptionHistory([descriptionBeforeAutosave, ...descriptionHistory.slice(1)]);
                          }
                        }
                      }}
                    >
                      <FaSync className="mr-1 h-3.5 w-3.5" />
                      Gendan
                    </button>
                    <button
                      id="description-undo-btn"
                      className="px-3 py-1.5 text-sm bg-gray-50 text-gray-700 border border-gray-200 rounded-md hover:bg-gray-100 transition-colors flex items-center"
                      onClick={() => handleDescriptionUndo()}
                    >
                      <FaTimes className="mr-1 h-3.5 w-3.5" />
                      Fortryd
                    </button>
                  </div>
                </div>
              </div>
              
              {/* Saving indicator */}
              {savingInfo && (
                <div className="absolute top-2 right-2 flex items-center space-x-2 bg-white bg-opacity-90 px-2 py-1 rounded-md shadow-sm z-10">
                  <FaSpinner className="animate-spin text-blue-500 h-4 w-4" />
                  <span className="text-xs text-blue-500">Gemmer...</span>
                </div>
              )}
              
              {/* Success indicator */}
              {infoSuccess && (
                <div className="absolute top-2 right-2 flex items-center space-x-2 bg-green-50 text-green-600 px-2 py-1 rounded-md shadow-sm z-10">
                  <FaCheck className="h-4 w-4" />
                  <span className="text-xs">Beskrivelse gemt!</span>
                </div>
              )}
            </div>
          </div>
          
          {/* Success message for info updates */}
          {infoSuccess && (
            <div className="mt-4 rounded-md bg-green-50 p-4 border border-green-200">
              <div className="flex items-center">
                <FaCheck className="text-green-500 mr-2" />
                <span className="text-green-700">Information opdateret!</span>
              </div>
            </div>
          )}
        </div>
        
        {/* Current Screenshots */}
        <div 
          className="bg-white rounded-3xl shadow-md p-6 mb-8 border-2 border-dashed border-gray-200 hover:border-blue-400 transition-colors"
          onDragOver={(e) => {
            e.preventDefault();
            e.currentTarget.classList.add('ring-2', 'ring-blue-500');
          }}
          onDragLeave={(e) => {
            e.preventDefault();
            e.currentTarget.classList.remove('ring-2', 'ring-blue-500');
          }}
          onDrop={(e) => {
            e.preventDefault();
            e.currentTarget.classList.remove('ring-2', 'ring-blue-500');
            if (e.dataTransfer.files.length > 0) {
              handleNewImageSelect({ target: { files: e.dataTransfer.files } });
            }
          }}
          onPaste={(e) => {
            const items = e.clipboardData.items;
            for (let i = 0; i < items.length; i++) {
              if (items[i].type.indexOf('image') !== -1) {
                const blob = items[i].getAsFile();
                if (blob) {
                  const fileList = new DataTransfer();
                  fileList.items.add(blob);
                  handleNewImageSelect({ target: { files: fileList.files } });
                }
                break;
              }
            }
          }}
        >
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-xl font-bold text-gray-800">Nuværende Billeder</h2>
              <p className="text-gray-500">Billeder skal have en opløsning på præcis 1920x1080 pixels.</p>
            </div>
            <div className="flex items-center space-x-4">
              <input 
                id="screenshot-upload-input"
                type="file" 
                accept="image/*"
                multiple
                className="sr-only"
                onChange={handleNewImageSelect}
              />
              <label 
                htmlFor="screenshot-upload-input"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center cursor-pointer"
              >
                <FaPlus className="mr-2" />
                Tilføj billeder
              </label>
              <div className="flex items-center space-x-1 text-gray-500 text-xs">
                <FaMousePointer />
                <FaLongArrowAltRight />
                <FaImage />
                <span>Træk & slip</span>
                <span>eller</span>
                <FaKeyboard />
                <span>CTRL+V</span>
              </div>
            </div>
          </div>
          {/* Display existing screenshots */}
          {product?.screenshotUrls && product.screenshotUrls.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
              {product.screenshotUrls.map((screenshot, index) => (
                <div key={screenshot.fileId} className="border border-gray-200 rounded-3xl overflow-hidden">
                  <div className="relative w-full aspect-[16/9] bg-black group rounded-3xl">
                    <img 
                      src={replacedImages[index] ? replacedImages[index] : getImageUrl(screenshot)} 
                      alt={`Screenshot ${index + 1}`}
                      className="absolute inset-0 w-full h-full object-cover rounded-3xl"
                    />
                    {/* Overlay on hover - only covers the image */}
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer z-10 rounded-3xl">
                      <FaImage className="text-white text-4xl" />
                    </div>
                    {/* Delete button */}
                    <button
                      type="button"
                      className="absolute top-2 right-2 bg-white/80 hover:bg-white text-red-600 rounded-full p-1 z-20 border border-gray-300 shadow"
                      title="Slet billede"
                      onClick={async (e) => {
                        e.stopPropagation();
                        try {
                          // Delete from Cloudinary
                          const response = await fetch(`/api/delete-image`, {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ publicId: screenshot.publicId })
                          });
                          if (!response.ok) {
                            throw new Error('Failed to delete image from Cloudinary');
                          }
                          // Update state to remove the image
                          setProduct(prev => ({
                            ...prev,
                            screenshotUrls: prev.screenshotUrls.filter((_, i) => i !== index)
                          }));
                        } catch (error) {
                          console.error('Error deleting image:', error);
                        }
                      }}
                    >
                      <FaTrash className="w-4 h-4" />
                    </button>
                    {/* Save and Cancel buttons if this image has a pending replacement */}
                    {replacedImages[index] && (
                      <>
                        {/* Save button */}
                        <button
                          type="button"
                          className="absolute top-2 left-2 bg-green-600 hover:bg-green-700 text-white rounded-full p-2 z-20 border-2 border-white shadow-lg transition-all duration-150"
                          title="Gem erstatning"
                          onClick={e => {
                            e.stopPropagation();
                            handleSaveSingleReplacement(index);
                          }}
                        >
                          <FaSave className="w-5 h-5" />
                        </button>
                        {/* Cancel button */}
                        <button
                          type="button"
                          className="absolute top-2 right-2 bg-white/80 hover:bg-white text-red-600 rounded-full p-1 z-20 border border-gray-300 shadow"
                          title="Annuller erstatning"
                          onClick={e => {
                            e.stopPropagation();
                            handleCancelReplacement(index);
                          }}
                        >
                          <FaTimes className="w-4 h-4" />
                        </button>
                      </>
                    )}
                    {/* Hidden file input for accessibility */}
                    <input
                      id={`replace-input-${index}`}
                      type="file"
                      accept="image/*"
                      className="sr-only"
                      tabIndex={0}
                      aria-label={`Erstat billede ${index + 1}`}
                      onChange={(e) => handleImageSelect(e, index)}
                    />
                  </div>
                  <div className="p-3 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                    <div className="text-xs text-gray-500 truncate max-w-[65%]">
                      {screenshot.filename}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 mb-4">Ingen skærmbilleder tilgængelige</p>
          )}
          
          {/* Display newly selected images that need to be uploaded */}
          {newImages.length > 0 && (
            <div className="mt-6 border-t border-gray-200 pt-6">
              <h3 className="text-lg font-semibold mb-4">Nye billeder der vil blive uploadet ({newImagePreviews.length})</h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                {newImagePreviews.map((previewUrl, index) => (
                  <div key={index} className="border border-gray-200 rounded-xl overflow-hidden">
                    <div className="relative aspect-[16/9] bg-gray-100">
                      <img 
                        src={previewUrl} 
                        alt={`New image ${index + 1}`} 
                        className="w-full h-full object-cover"
                      />
                      <button
                        onClick={() => {
                          // Remove image from both preview and newImages state
                          setNewImagePreviews(prev => prev.filter((_, i) => i !== index));
                          setNewImages(prev => prev.filter((_, i) => i !== index));
                          URL.revokeObjectURL(previewUrl);
                        }}
                        className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md border border-gray-200"
                        title="Fjern billede"
                      >
                        <FaTimes className="text-red-600 w-4 h-4" />
                      </button>
                    </div>
                    <div className="p-2 bg-gray-50 text-xs text-gray-600 truncate">
                      {newImages[index]?.name || 'Nyt billede'}
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="flex justify-end mt-4">
                <button
                  type="button"
                  onClick={() => {
                    // Clean up object URLs to prevent memory leaks
                    newImagePreviews.forEach(url => URL.revokeObjectURL(url));
                    setNewImages([]);
                    setNewImagePreviews([]);
                  }}
                  className="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Annuller
                </button>
                <button
                  type="button"
                  onClick={handleUploadNewImages}
                  disabled={uploadingNewImages}
                  className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                    uploadingNewImages ? 'bg-blue-400 cursor-default' : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                >
                  {uploadingNewImages ? (
                    <>
                      <FaSpinner className="inline animate-spin mr-2" />
                      Uploader...
                    </>
                  ) : (
                    <>
                      <FaUpload className="inline mr-2" />
                      Upload billeder
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
        
        {/* Files Section */}
        <div 
          className="bg-white rounded-3xl shadow-md p-6 mb-8 border-2 border-dashed border-gray-200 hover:border-blue-400 transition-colors"
          onDragOver={(e) => {
            e.preventDefault();
            e.currentTarget.classList.add('ring-2', 'ring-blue-500');
          }}
          onDragLeave={(e) => {
            e.preventDefault();
            e.currentTarget.classList.remove('ring-2', 'ring-blue-500');
          }}
          onDrop={(e) => {
            e.preventDefault();
            e.currentTarget.classList.remove('ring-2', 'ring-blue-500');
            if (e.dataTransfer.files.length > 0) {
              handleFileSelect({ target: { files: e.dataTransfer.files } });
            }
          }}
          onPaste={(e) => {
            const items = e.clipboardData.items;
            for (let i = 0; i < items.length; i++) {
              if (items[i].type.indexOf('application') !== -1) {
                const blob = items[i].getAsFile();
                if (blob) {
                  const fileList = new DataTransfer();
                  fileList.items.add(blob);
                  handleFileSelect({ target: { files: fileList.files } });
                }
                break;
              }
            }
          }}
        >
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-xl font-bold text-gray-800">Filer</h2>
              <p className="text-gray-500">Upload filer til dit produkt (zip, rar, jar, sk, txt)</p>
            </div>
            <div className="flex items-center space-x-4">
              <input 
                type="file" 
                multiple
                className="sr-only"
                onChange={handleFileSelect}
                id="file-upload-input"
                accept=".zip,.rar,.jar,.sk,.txt"
              />
              <label 
                htmlFor="file-upload-input"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center cursor-pointer"
              >
                <FaFileUpload className="mr-2" />
                <span>Upload Filer</span>
              </label>
              <div className="flex items-center space-x-1 text-gray-500 text-xs">
                <FaMousePointer />
                <FaLongArrowAltRight />
                <FaFileAlt />
                <span>Træk & slip</span>
                <span>eller</span>
                <FaKeyboard />
                <span>CTRL+V</span>
              </div>
            </div>
          </div>
          
          {/* Display existing files */}
          {product?.fileUrls && product.fileUrls.length > 0 ? (
            <div className="space-y-3 mb-6">
              {product.fileUrls.map((file) => (
                <div key={file.fileId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex items-center">
                    <FaFileAlt className="text-gray-500 w-5 h-5 mr-3" />
                    <div>
                      <p className="font-medium text-gray-800">{file.filename}</p>
                      <p className="text-xs text-gray-500">
                        {file.contentType}
                        {file.size && ` • ${Math.round(file.size / 1024)} KB`}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <a 
                      href={file.url || '#'} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="px-3 py-1 text-xs font-medium rounded bg-gray-200 text-gray-800 hover:bg-gray-300"
                    >
                      Åbn
                    </a>
                    <button
                      type="button"
                      onClick={() => handleDeleteFile(file.fileId)}
                      disabled={deletingFileId === file.fileId}
                      className={`px-3 py-1 text-xs font-medium rounded text-white ${
                        deletingFileId === file.fileId 
                          ? 'bg-red-400 cursor-default' 
                          : 'bg-red-600 hover:bg-red-700'
                      }`}
                    >
                      {deletingFileId === file.fileId ? (
                        <FaSpinner className="animate-spin w-3 h-3" />
                      ) : (
                        <FaTrash className="w-3 h-3" />
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 mb-6">Ingen filer uploadet endnu</p>
          )}
          
          {/* New files to upload */}
          {newFiles.length > 0 && (
            <div className="mt-6 border-t pt-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Nye filer der vil blive uploadet ({newFiles.length})</h3>
              
              <div className="space-y-3 mb-6">
                {newFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex items-center">
                      <FaFileAlt className="text-gray-500 w-5 h-5 mr-3" />
                      <div>
                        <p className="font-medium text-gray-800">{file.name}</p>
                        <p className="text-xs text-gray-500">
                          {file.type || 'Ukendt type'}
                          {` • ${Math.round(file.size / 1024)} KB`}
                        </p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        setNewFiles(prev => prev.filter((_, i) => i !== index));
                      }}
                      className="px-3 py-1 text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700"
                    >
                      <FaTimes className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
              
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setNewFiles([])}
                  className="mr-3 inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
                >
                  Annuller
                </button>
                <button
                  type="button"
                  onClick={handleUploadFiles}
                  disabled={uploadingFiles}
                  className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
                    uploadingFiles ? 'bg-blue-400 cursor-default' : 'bg-blue-600 hover:bg-blue-700'
                  } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors`}
                >
                  {uploadingFiles ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Uploader...
                    </>
                  ) : (
                    <>
                      <FaUpload className="mr-2" />
                      Upload Filer
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
        
        {/* Addons & Dependencies Section */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
          <div 
            className="bg-gradient-to-r from-blue-600 to-indigo-700 p-4 sm:px-6 flex justify-between items-center cursor-pointer"
            onClick={() => setShowAddonSection(!showAddonSection)}
          >
            <motion.div 
              className="flex items-center text-white"
              initial={{ opacity: 1 }}
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="p-2 bg-white/15 rounded-lg mr-3">
                <FaPlug className="w-5 h-5 text-white" />
              </div>
              <h2 className="text-xl font-bold">Addons & Dependencies</h2>
            </motion.div>
            <div className="text-white">
              <motion.div
                animate={{ rotate: showAddonSection ? 45 : 0 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <FaPlus className="w-5 h-5" />
              </motion.div>
            </div>
          </div>
          
          <AnimatePresence>
          {showAddonSection && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="p-6"
              >
                <motion.div
                  initial={{ y: -20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.3 }}
                  className="flex justify-between items-center mb-4"
                >
                <p className="text-gray-600">
                  Administrer addons og dependencies som dit produkt kræver for at fungere
                </p>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  type="button"
                  onClick={() => setShowAddonForm(true)}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <FaPlus className="mr-1.5 h-3.5 w-3.5" />
                  Tilføj Addon
                  </motion.button>
                </motion.div>
              
              {/* Addons List */}
              {addons.length > 0 ? (
                <div className="space-y-3 mb-6">
                  {addons.map((addon, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg border border-gray-200">
                      <div className="flex items-center">
                        <div className="bg-blue-100 p-2 rounded-full mr-3">
                          <FaPuzzlePiece className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-800 flex items-center">
                            {addon.name}
                            {addon.required && (
                              <span className="ml-2 text-xs bg-blue-50 text-blue-700 px-2 py-0.5 rounded-full">
                                Påkrævet
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-500">
                            {addon.version && <span className="mr-3">v{addon.version}</span>}
                            {addon.url && (
                              <span className="flex items-center text-blue-600">
                                <FaLink className="mr-1 h-3 w-3" />
                                <a href={addon.url} target="_blank" rel="noopener noreferrer" className="truncate hover:underline">
                                  {addon.url.length > 30 ? `${addon.url.substring(0, 30)}...` : addon.url}
                                </a>
                                  {addon.isDirectDownload && (
                                    <span className="ml-2 text-xs bg-green-50 text-green-700 px-2 py-0.5 rounded-full">
                                      Direkte Download
                                    </span>
                                  )}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          type="button"
                          onClick={() => handleEditAddon(index)}
                          className="p-1.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                        >
                          <span className="sr-only">Rediger</span>
                          <FaEdit className="h-4 w-4" />
                        </button>
                        <button
                          type="button"
                          onClick={() => handleRemoveAddon(index)}
                          className="p-1.5 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors"
                        >
                          <span className="sr-only">Fjern</span>
                          <FaTimes className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg border border-dashed border-gray-300 mb-6">
                  <FaPuzzlePiece className="h-10 w-10 mx-auto mb-3 text-gray-300" />
                  <p className="mb-1">Ingen addons eller dependencies tilføjet</p>
                  <p className="text-sm">Tilføj addons som dit produkt skal bruge for at virke korrekt</p>
                </div>
              )}
              
              {/* Save Addons Button */}
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={handleSaveAllAddons}
                  disabled={savingAddons}
                  className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
                      savingAddons ? 'bg-blue-400 cursor-default' : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  }`}
                >
                  {savingAddons ? (
                    <>
                      <FaSpinner className="animate-spin -ml-1 mr-2 h-4 w-4" />
                      Gemmer...
                    </>
                  ) : addonSuccess ? (
                    <>
                      <FaCheck className="-ml-1 mr-2 h-4 w-4" />
                      Gemt!
                    </>
                  ) : (
                    <>
                      <FaSave className="-ml-1 mr-2 h-4 w-4" />
                      Gem Ændringer
                    </>
                  )}
                </button>
              </div>
              
              {/* Addon Form Modal */}
              {showAddonForm && (
                <div className="fixed inset-0 z-50 bg-black/30 backdrop-blur-sm" aria-labelledby="modal-title" role="dialog" aria-modal="true">
                  <div className="flex min-h-screen items-center justify-center p-4 text-center">
                    <style jsx global>{`
                      body {
                        overflow: hidden;
                        padding-right: ${window.innerWidth - document.documentElement.clientWidth}px;
                      }
                    `}</style>
                    
                    {/* Modal Backdrop */}
                    <div 
                      className="fixed inset-0 bg-black/30 transition-opacity" 
                      aria-hidden="true"
                      onClick={() => {
                        setShowAddonForm(false);
                        setEditingAddonIndex(null);
                        setCurrentAddon({
                          name: '',
                          url: '',
                          version: '',
                            required: false,
                            isDirectDownload: false
                        });
                      }}
                    />
                    
                    {/* Modal Content */}
                    <div className="relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left shadow-xl transition-all">
                      {/* Close Button */}
                      <button
                        type="button"
                        onClick={() => {
                          setShowAddonForm(false);
                          setEditingAddonIndex(null);
                          setCurrentAddon({
                            name: '',
                            url: '',
                            version: '',
                              required: false,
                              isDirectDownload: false
                          });
                        }}
                        className="absolute right-4 top-4 rounded-lg p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700"
                      >
                        <FaTimes className="h-5 w-5" />
                      </button>
                      
                      {/* Modal Header */}
                      <div className="mb-6">
                        <h3 className="text-xl font-semibold text-gray-900">
                          {editingAddonIndex !== null ? 'Rediger Addon' : 'Tilføj Ny Addon'}
                        </h3>
                      </div>
                      
                      {/* Form Fields */}
                      <div className="space-y-5">
                        {/* Name Field */}
                        <div>
                          <label htmlFor="addon-name" className="mb-1 block text-sm font-medium text-gray-700">
                            Navn <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            id="addon-name"
                            name="name"
                            value={currentAddon.name}
                            onChange={handleAddonChange}
                            className={`block w-full rounded-lg border border-gray-200 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 ${
                              addonFormErrors.name ? 'border-red-300' : ''
                            }`}
                              placeholder="F.eks. Vault, WorldEdit"
                          />
                          {addonFormErrors.name && (
                              <p className="mt-1 text-xs text-red-500">{addonFormErrors.name}</p>
                          )}
                        </div>
                        
                        {/* Version Field */}
                        <div>
                          <label htmlFor="addon-version" className="mb-1 block text-sm font-medium text-gray-700">
                            Version (valgfri)
                          </label>
                          <input
                            type="text"
                            id="addon-version"
                            name="version"
                            value={currentAddon.version}
                            onChange={handleAddonChange}
                            className="block w-full rounded-lg border border-gray-200 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                            placeholder="f.eks. 1.0.0"
                          />
                        </div>
                        
                        {/* URL Field */}
                        <div>
                          <label htmlFor="addon-url" className="mb-1 block text-sm font-medium text-gray-700">
                            URL (valgfri)
                          </label>
                          <input
                            type="url"
                            id="addon-url"
                            name="url"
                            value={currentAddon.url}
                            onChange={handleAddonChange}
                            className="block w-full rounded-lg border border-gray-200 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                            placeholder="https://example.com/addon"
                          />
                        </div>
                        
                        {/* Required Checkbox */}
                        <div className="flex items-center rounded-lg bg-gray-50 p-3">
                          <input
                            id="addon-required"
                            name="required"
                            type="checkbox"
                            checked={currentAddon.required}
                            onChange={handleAddonChange}
                            className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <label htmlFor="addon-required" className="ml-2 text-sm text-gray-700">
                            Påkrævet for at produktet virker
                          </label>
                        </div>
                          
                          {/* Direct Download Checkbox */}
                          <div className="flex items-center rounded-lg bg-gray-50 p-3 mt-2">
                            <input
                              id="addon-direct-download"
                              name="isDirectDownload"
                              type="checkbox"
                              checked={currentAddon.isDirectDownload}
                              onChange={handleAddonChange}
                              className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <label htmlFor="addon-direct-download" className="ml-2 text-sm text-gray-700">
                              URL er direkte download link
                            </label>
                          </div>
                        
                        {/* Submit Button */}
                        <div className="pt-2">
                          <button
                            type="button"
                            onClick={handleSaveAddon}
                            className="flex w-full items-center justify-center rounded-lg bg-blue-600 px-4 py-2.5 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300"
                          >
                            {editingAddonIndex !== null ? 'Opdater Addon' : 'Tilføj Addon'}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              </motion.div>
          )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}