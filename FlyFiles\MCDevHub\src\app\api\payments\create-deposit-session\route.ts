import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import <PERSON><PERSON> from 'stripe';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2025-04-30.basil' as any,
});

export async function POST(req: NextRequest) {
  try {
    // Check user authentication
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = session.user.id;
    
    // Parse request body
    const { amount, paymentMethod } = await req.json();
    
    // Validate amount
    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount', details: 'Amount must be a positive number' },
        { status: 400 }
      );
    }
    
    // Connect to database
    const { db } = await connectToDatabase();
    const usersCollection = db.collection('users');
    
    // Find user in MongoDB
    const user = await usersCollection.findOne({ auth_id: userId });
    
    if (!user) {
      // Create user if not found using NextAuth session data
      const discordId = session.user.id;

      const newUser = {
        auth_id: userId,
        discord_id: discordId,
        username: session.user.name || 'User',
        avatar: session.user.image,
        email: session.user.email,
        balance: 0,
        created_at: new Date(),
        updated_at: new Date(),
      };

      await usersCollection.insertOne(newUser);
    }
    
    // Create a unique reference for this deposit
    const reference = `deposit_${userId}_${Date.now()}`;
    
    // Store deposit intent in database
    const depositsCollection = db.collection('deposits');
    const depositResult = await depositsCollection.insertOne({
      userId,
      amount,
      status: 'pending',
      paymentMethod,
      reference,
      createdAt: new Date(),
    });
    
    let paymentUrl = '';
    
    // Get the base URL for redirects
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://mcdevhub.dk';
    
    // Handle payment method selection
    if (paymentMethod === 'stripe') {
      // Create Stripe checkout session
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price_data: {
              currency: 'dkk',
              product_data: {
                name: 'MCDevHub Balance Indsætning',
                description: 'Tilføj penge til din MCDevHub konto',
              },
              unit_amount: amount * 100, // Convert to cents/øre
            },
            quantity: 1,
          },
        ],
        mode: 'payment',
        success_url: `${baseUrl}/payments/success?reference=${reference}&session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${baseUrl}/profile?tab=transactions&deposit_cancelled=true`,
        client_reference_id: reference,
        metadata: {
          userId,
          type: 'deposit',
          depositId: depositResult.insertedId.toString(),
        },
      });
      
      paymentUrl = session.url || '';
      
    } else if (paymentMethod === 'mobilepay') {
      // In a real implementation, you would create a MobilePay payment session here
      // This is a placeholder for MobilePay integration
      const mobilePayEndpoint = process.env.MOBILEPAY_API_ENDPOINT || 'https://api.mobilepay.dk/v1/payments';
      
      try {
        // Simulating MobilePay integration - in production you would call their API
        // For now, redirect to a temporary page that explains MobilePay isn't fully implemented
        paymentUrl = `${baseUrl}/payments/mobilepay-redirect?reference=${reference}`;
        
      } catch (error) {
        console.error('MobilePay API error:', error);
        return NextResponse.json(
          { error: 'MobilePay payment failed', details: 'Could not create MobilePay payment' },
          { status: 500 }
        );
      }
    } else {
      return NextResponse.json(
        { error: 'Invalid payment method', details: 'Supported methods are stripe and mobilepay' },
        { status: 400 }
      );
    }
    
    if (!paymentUrl) {
      return NextResponse.json(
        { error: 'Payment session creation failed', details: 'Could not generate payment URL' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ url: paymentUrl });
    
  } catch (error) {
    console.error('Create deposit session error:', error);
    return NextResponse.json(
      { error: 'Server error', details: 'Could not process deposit request' },
      { status: 500 }
    );
  }
} 