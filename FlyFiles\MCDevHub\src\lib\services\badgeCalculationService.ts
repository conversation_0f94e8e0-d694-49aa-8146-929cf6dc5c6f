import { Mongo<PERSON><PERSON>, Db } from 'mongodb';
import { 
  IBadgeCriteriaResult, 
  IUserBadgeData, 
  BADGE_DEFINITIONS,
  PRODUCT_CATEGORIES,
  EARLY_BIRD_CUTOFF_DATE
} from '../models/badge';

/**
 * Badge calculation service for determining badge eligibility
 */
export class BadgeCalculationService {
  private db: Db;

  constructor(db: Db) {
    this.db = db;
  }

  /**
   * Gather all user data needed for badge calculations
   */
  async gatherUserData(discordUserId: string): Promise<IUserBadgeData> {
    // Check if user is freelancer (exists in adminusers collection)
    const adminUser = await this.db.collection('adminusers').findOne({
      discordUserId: discordUserId
    });
    
    const isFreelancer = !!adminUser;
    
    // Get user creation date
    let createdAt: Date | undefined;
    if (adminUser?.createdAt) {
      createdAt = new Date(adminUser.createdAt);
    } else {
      // Try to get from regular users collection
      const regularUser = await this.db.collection('users').findOne({
        discordUserId: discordUserId
      });
      if (regularUser?.createdAt) {
        createdAt = new Date(regularUser.createdAt);
      }
    }

    // Get purchase data
    const purchases = await this.db.collection('user_purchases')
      .find({ userId: discordUserId })
      .toArray();
    
    const totalPurchases = purchases.length;
    const purchaseCategories = [...new Set(purchases.map(p => p.productType).filter(Boolean))];
    const balancePurchases = purchases.filter(p => p.paymentMethod === 'Balance').length;

    // Get sales data (for freelancers)
    let totalIncome = 0;
    let totalProducts = 0;
    let activeProductsByCategory: Record<string, number> = {};
    
    if (isFreelancer) {
      // Get transaction data for income
      const transactions = await this.db.collection('transactions')
        .find({ sellerId: discordUserId })
        .toArray();
      
      totalIncome = transactions.reduce((sum, t) => sum + (t.amount || 0), 0) * 0.85; // 15% platform fee
      
      // Get product data
      const products = await this.db.collection('products')
        .find({ discordUserId: discordUserId })
        .toArray();
      
      totalProducts = products.length;
      
      // Count active products by category
      const activeProducts = products.filter(p => p.status === 'active' || p.status === 'approved');
      activeProducts.forEach(product => {
        const category = product.productType || 'other';
        activeProductsByCategory[category] = (activeProductsByCategory[category] || 0) + 1;
      });
    }

    // Get verification status
    const verifiedUser = await this.db.collection('verified').findOne({
      discordId: discordUserId,
      isVerified: true
    });
    const isVerified = !!verifiedUser;

    // Get social links status
    const userWithSocials = adminUser || await this.db.collection('users').findOne({
      discordUserId: discordUserId
    });
    
    const hasGithub = !!(userWithSocials?.githubUsername);
    const hasYoutube = !!(userWithSocials?.youtubeUrl);
    const hasEmail = !!(userWithSocials?.email);

    // Get favorites data
    const userFavorites = await this.db.collection('users').findOne(
      { discordUserId: discordUserId },
      { projection: { favorites: 1 } }
    );
    const totalFavorites = userFavorites?.favorites?.length || 0;

    // Get how many times freelancer's products have been favorited
    let productsFavorited = 0;
    if (isFreelancer) {
      const allUsers = await this.db.collection('users')
        .find({ 'favorites.0': { $exists: true } })
        .toArray();
      
      const freelancerProducts = await this.db.collection('products')
        .find({ discordUserId: discordUserId })
        .project({ _id: 1 })
        .toArray();
      
      const freelancerProductIds = freelancerProducts.map(p => p._id.toString());
      
      allUsers.forEach(user => {
        if (user.favorites) {
          user.favorites.forEach(fav => {
            if (freelancerProductIds.includes(fav.productId)) {
              productsFavorited++;
            }
          });
        }
      });
    }

    return {
      discordUserId,
      isFreelancer,
      createdAt,
      totalPurchases,
      purchaseCategories,
      balancePurchases,
      totalIncome,
      totalProducts,
      activeProductsByCategory,
      isVerified,
      hasGithub,
      hasYoutube,
      hasEmail,
      totalFavorites,
      productsFavorited
    };
  }

  /**
   * Check if user meets criteria for Loyal Customer badge
   */
  checkLoyalCustomer(userData: IUserBadgeData): IBadgeCriteriaResult {
    return {
      eligible: (userData.totalPurchases || 0) >= 10,
      progress: userData.totalPurchases || 0,
      maxProgress: 10
    };
  }

  /**
   * Check if user meets criteria for Collector badge
   */
  checkCollector(userData: IUserBadgeData): IBadgeCriteriaResult {
    const uniqueCategories = userData.purchaseCategories?.length || 0;
    return {
      eligible: uniqueCategories >= 3,
      progress: uniqueCategories,
      maxProgress: 3
    };
  }

  /**
   * Check if user meets criteria for Early Bird badge
   */
  checkEarlyBird(userData: IUserBadgeData): IBadgeCriteriaResult {
    if (!userData.createdAt) {
      return { eligible: false };
    }
    
    return {
      eligible: userData.createdAt < EARLY_BIRD_CUTOFF_DATE
    };
  }

  /**
   * Check if user meets criteria for Rising Star badge (freelancers only)
   */
  checkRisingStar(userData: IUserBadgeData): IBadgeCriteriaResult {
    if (!userData.isFreelancer) {
      return { eligible: false };
    }
    
    return {
      eligible: (userData.totalIncome || 0) >= 1000,
      progress: userData.totalIncome || 0,
      maxProgress: 1000
    };
  }

  /**
   * Check if user meets criteria for Product Pioneer badge (freelancers only)
   */
  checkProductPioneer(userData: IUserBadgeData): IBadgeCriteriaResult {
    if (!userData.isFreelancer) {
      return { eligible: false };
    }
    
    return {
      eligible: (userData.totalProducts || 0) >= 5,
      progress: userData.totalProducts || 0,
      maxProgress: 5
    };
  }

  /**
   * Check if user meets criteria for Category Master badge (freelancers only)
   */
  checkCategoryMaster(userData: IUserBadgeData): IBadgeCriteriaResult {
    if (!userData.isFreelancer || !userData.activeProductsByCategory) {
      return { eligible: false };
    }
    
    const maxInCategory = Math.max(...Object.values(userData.activeProductsByCategory));
    return {
      eligible: maxInCategory >= 5,
      progress: maxInCategory,
      maxProgress: 5
    };
  }

  /**
   * Check if user meets criteria for Verified Creator badge
   */
  checkVerifiedCreator(userData: IUserBadgeData): IBadgeCriteriaResult {
    return {
      eligible: userData.isVerified || false
    };
  }

  /**
   * Check if user meets criteria for Social Connector badge
   */
  checkSocialConnector(userData: IUserBadgeData): IBadgeCriteriaResult {
    const socialLinksCount = [userData.hasGithub, userData.hasYoutube, userData.hasEmail]
      .filter(Boolean).length;
    
    return {
      eligible: socialLinksCount === 3,
      progress: socialLinksCount,
      maxProgress: 3
    };
  }

  /**
   * Check if user meets criteria for Customer Favorite badge (freelancers only)
   */
  checkCustomerFavorite(userData: IUserBadgeData): IBadgeCriteriaResult {
    if (!userData.isFreelancer) {
      return { eligible: false };
    }
    
    return {
      eligible: (userData.productsFavorited || 0) >= 50,
      progress: userData.productsFavorited || 0,
      maxProgress: 50
    };
  }

  /**
   * Check if user meets criteria for Wishlist Curator badge (regular users)
   */
  checkWishlistCurator(userData: IUserBadgeData): IBadgeCriteriaResult {
    return {
      eligible: (userData.totalFavorites || 0) >= 20,
      progress: userData.totalFavorites || 0,
      maxProgress: 20
    };
  }

  /**
   * Check if user meets criteria for Diverse Taste badge (regular users)
   */
  checkDiverseTaste(userData: IUserBadgeData): IBadgeCriteriaResult {
    const mainCategories = ['plugin', 'script', 'service', 'map', 'build'];
    const userCategories = userData.purchaseCategories || [];
    const mainCategoriesPurchased = mainCategories.filter(cat => userCategories.includes(cat));
    
    return {
      eligible: mainCategoriesPurchased.length === mainCategories.length,
      progress: mainCategoriesPurchased.length,
      maxProgress: mainCategories.length
    };
  }

  /**
   * Check if user meets criteria for Balance Master badge
   */
  checkBalanceMaster(userData: IUserBadgeData): IBadgeCriteriaResult {
    return {
      eligible: (userData.balancePurchases || 0) >= 5,
      progress: userData.balancePurchases || 0,
      maxProgress: 5
    };
  }

  /**
   * Check if user meets criteria for Community Champion badge
   */
  checkCommunityChampion(userData: IUserBadgeData): IBadgeCriteriaResult {
    const criteria = {
      purchases: (userData.totalPurchases || 0) >= 50,
      favorites: (userData.totalFavorites || 0) >= 10,
      verified: userData.isVerified || false,
      socialLinks: userData.hasGithub && userData.hasYoutube && userData.hasEmail
    };
    
    const metCriteria = Object.values(criteria).filter(Boolean).length;
    
    return {
      eligible: metCriteria === 4,
      progress: metCriteria,
      maxProgress: 4
    };
  }

  /**
   * Calculate all badge eligibility for a user
   */
  async calculateAllBadges(discordUserId: string): Promise<Record<string, IBadgeCriteriaResult>> {
    const userData = await this.gatherUserData(discordUserId);
    
    return {
      loyal_customer: this.checkLoyalCustomer(userData),
      collector: this.checkCollector(userData),
      early_bird: this.checkEarlyBird(userData),
      rising_star: this.checkRisingStar(userData),
      product_pioneer: this.checkProductPioneer(userData),
      category_master: this.checkCategoryMaster(userData),
      verified_creator: this.checkVerifiedCreator(userData),
      social_connector: this.checkSocialConnector(userData),
      customer_favorite: this.checkCustomerFavorite(userData),
      wishlist_curator: this.checkWishlistCurator(userData),
      diverse_taste: this.checkDiverseTaste(userData),
      balance_master: this.checkBalanceMaster(userData),
      community_champion: this.checkCommunityChampion(userData)
    };
  }
}
