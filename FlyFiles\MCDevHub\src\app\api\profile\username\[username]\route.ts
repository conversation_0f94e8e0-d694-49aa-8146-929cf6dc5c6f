import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb.js';
import { auth } from '@/lib/auth.js';
import { ObjectId } from 'mongodb';
import { getProfileDataAccess, filterProfileData } from '@/lib/profilePermissions';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ username: string }> }
) {
  try {
    const params = await context.params;
    const { username } = params;

    if (!username) {
      return NextResponse.json({ error: 'Username is required' }, { status: 400 });
    }

    // Get auth session (optional for profile viewing)
    const session = await auth().catch(() => null);

    // Connect to database
    const { db } = await connectToDatabase();

    // Look up Discord account by username
    const discordAccount = await db.collection('accounts').findOne(
      {
        provider: 'discord',
        username: new RegExp(`^${username}$`, 'i') // Case-insensitive match
      },
      {
        projection: {
          userId: 1,
          username: 1,
          avatar: 1,
          avatarUrl: 1,
          discordId: 1,
          email: 1
        }
      }
    );

    if (!discordAccount) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const discordUserId = discordAccount.discordId;

    // Look up user in adminusers and users collections
    let user, isFreelancer = false;

    // Try adminusers first
    user = await db.collection('adminusers').findOne(
      { discordUserId: discordUserId },
      {
        projection: {
          password: 0,
          allowedcases: 0
        }
      }
    );

    if (user) {
      isFreelancer = user.admintype === 'Freelancer';
    } else {
      // Try regular users collection
      user = await db.collection('users').findOne(
        { discordUserId: discordUserId },
        {
          projection: {
            auth_id: 0,
            balance: 0,
            notifications: 0
          }
        }
      );
    }

    // If no user found, create basic user object
    if (!user) {
      user = {
        username: discordAccount.username,
        discordUserId: discordUserId,
        avatar: discordAccount.avatar,
        avatarUrl: discordAccount.avatarUrl,
        email: discordAccount.email,
        createdAt: new Date(),
        badges: []
      };
    }

    // Get purchases, favorites, and verification data
    let purchases = [], favorites = [], verificationStatus = null;

    try {
      // Get purchases
      purchases = await db.collection('user_purchases')
        .find(
          { userId: discordUserId },
          {
            projection: {
              paymentMethod: 0,
              transactionId: 0,
              stripeSessionId: 0
            }
          }
        )
        .sort({ purchaseDate: -1 })
        .limit(50)
        .toArray();

      // Enhance purchases with product thumbnails
      purchases = await Promise.all(purchases.map(async (purchase: any) => {
        try {
          let product = null;
          const productId = purchase.productId;

          // Try to find the product
          product = await db.collection('products').findOne({ _id: productId });

          if (!product && ObjectId.isValid(productId)) {
            product = await db.collection('products').findOne({ _id: new ObjectId(productId) });
          }

          if (product && product.screenshotUrls && product.screenshotUrls.length > 0) {
            const screenshot = product.screenshotUrls.find((ss: any) => ss.url) || product.screenshotUrls[0];

            return {
              ...purchase,
              thumbnailUrl: screenshot.url || null
            };
          }

          return purchase;
        } catch (error) {
          return purchase;
        }
      }));

      // Get favorites
      const userWithFavorites = await db.collection('users').findOne(
        { discordUserId: discordUserId },
        { projection: { favorites: 1 } }
      );
      favorites = userWithFavorites?.favorites || [];

      // Get verification status
      verificationStatus = await db.collection('verified').findOne(
        { discordId: discordUserId },
        { projection: { isVerified: 1, verifiedAt: 1 } }
      );
    } catch (dataError) {
      // Continue with empty data rather than failing
      purchases = [];
      favorites = [];
      verificationStatus = null;
    }

    // Apply permissions and build response
    const currentUserId = session?.user?.id;
    const isAdmin = session?.user?.isAdmin || false;
    const access = getProfileDataAccess(currentUserId, discordUserId, isAdmin);

    const fullProfile = {
      user: {
        username: user.username || discordAccount.username,
        discordUserId: discordUserId,
        avatar: user.avatar || discordAccount.avatar,
        avatarUrl: user.avatarUrl || discordAccount.avatarUrl,
        bannerImage: user.bannerImage,
        description: user.description,
        createdAt: user.createdAt,
        badges: user.badges || [],
        isFreelancer,
        email: user.email || discordAccount.email,
        youtubeUrl: user.youtubeUrl,
        githubUsername: user.githubUsername,
        openForTasks: user.openForTasks
      },
      purchases,
      favorites,
      verification: {
        isVerified: verificationStatus?.isVerified || user.isVerified || false,
        verifiedAt: verificationStatus?.verifiedAt || user.verifiedAt || null
      },
      isOwnProfile: currentUserId === discordUserId
    };

    const filteredProfile = filterProfileData(fullProfile, access);

    return NextResponse.json(filteredProfile);

  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
