import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import bcrypt from 'bcrypt';

export async function POST(request) {
  try {
    // Parse the request body
    const body = await request.json();
    const { token, username, password } = body;

    // Validate the request
    if (!token || !username || !password) {
      return NextResponse.json(
        { message: 'Token, brugernavn og adgangskode er påkrævet' },
        { status: 400 }
      );
    }

    // Connect to the database
    const { db } = await connectToDatabase();

    // Check if the invitation exists and is valid
    const invitation = await db.collection('invitations').findOne({
      token,
      used: { $ne: true },
      expiresAt: { $gt: new Date() }
    });

    if (!invitation) {
      return NextResponse.json(
        { message: 'Ugyldigt eller udløbet invitationslink' },
        { status: 404 }
      );
    }

    // Check if username already exists
    const existingUser = await db.collection('adminusers').findOne({ username });
    if (existingUser) {
      return NextResponse.json(
        { message: '<PERSON><PERSON><PERSON>avn er allerede i brug' },
        { status: 409 }
      );
    }

    // Hash the password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create the new admin user
    const newUser = {
      username,
      password: hashedPassword,
      admintype: invitation.admintype,
      allowedcases: invitation.allowedcases,
      discordUserId: invitation.discordUserId,
      createdAt: new Date(),
      createdBy: invitation.createdBy
    };

    // Insert the new user
    const result = await db.collection('adminusers').insertOne(newUser);

    if (!result.insertedId) {
      throw new Error('Kunne ikke oprette bruger');
    }

    // Mark the invitation as used
    await db.collection('invitations').updateOne(
      { _id: invitation._id },
      { 
        $set: { 
          used: true, 
          usedAt: new Date(),
          usedBy: username
        } 
      }
    );

    return NextResponse.json({
      message: 'Konto oprettet med succes',
      username: username
    });
  } catch (error) {
    console.error('Error creating account:', error);
    return NextResponse.json(
      { message: 'Der opstod en fejl ved oprettelse af konto' },
      { status: 500 }
    );
  }
} 