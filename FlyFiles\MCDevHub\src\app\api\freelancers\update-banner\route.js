import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { verifyAdminToken } from '@/lib/auth';
import { put } from '@vercel/blob';
import { randomUUID } from 'crypto';

export async function POST(request) {
  try {
    // Verify the admin user is a freelancer
    const admin = await verifyAdminToken();
    
    if (!admin || admin.admintype !== 'Freelancer') {
      return NextResponse.json(
        { message: 'Ikke autoriseret - Kun Freelancers kan opdatere deres banner' },
        { status: 403 }
      );
    }
    
    // Process the form data
    const formData = await request.formData();
    const bannerFile = formData.get('banner');
    
    if (!bannerFile) {
      return NextResponse.json(
        { message: 'Intet banner-billede uploadet' },
        { status: 400 }
      );
    }
    
    // Check file type
    if (!bannerFile.type.startsWith('image/')) {
      return NextResponse.json(
        { message: 'Filen skal være et billede' },
        { status: 400 }
      );
    }

    // Generate unique filename with original extension
    const fileExtension = bannerFile.name.split('.').pop();
    const fileName = `banner_${admin.username}_${randomUUID()}.${fileExtension}`;
    
    // Upload to Vercel Blob storage
    const blob = await put(fileName, bannerFile, {
      access: 'public',
      addRandomSuffix: false,
    });
    
    // Connect to the database
    const { db } = await connectToDatabase();
    
    // Update the admin user's banner image
    await db.collection('adminusers').updateOne(
      { username: admin.username },
      { 
        $set: { 
          bannerImage: blob.url,
          updatedAt: new Date()
        } 
      }
    );
    
    return NextResponse.json({
      message: 'Banner opdateret',
      bannerUrl: blob.url
    });
  } catch (error) {
    console.error('Error uploading banner:', error);
    return NextResponse.json(
      { message: 'Der opstod en fejl ved upload af banner' },
      { status: 500 }
    );
  }
} 