import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { MongoClient } from 'mongodb';

// MongoDB connection setup
if (!process.env.MONGODB_URI) {
  throw new Error('Please add your Mongo URI to .env.local');
}

const uri = process.env.MONGODB_URI;

// Timeout for the request in milliseconds (10 seconds)
const REQUEST_TIMEOUT = 10000;

// Helper function to add timeout to a promise
const timeoutPromise = (promise: Promise<any>, ms: number) => {
  return Promise.race([
    promise,
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error(`Request timed out after ${ms}ms`)), ms)
    )
  ]);
};

// DELETE /api/user/notifications/clear-all - Clear all notifications for a user
export async function DELETE(request: NextRequest) {
  // Add a timeout to the entire request
  return timeoutPromise(handleDeleteRequest(request), REQUEST_TIMEOUT)
    .catch(error => {
      console.error('Clear all notifications API request timed out:', error);
      return NextResponse.json({ 
        success: false, 
        error: 'Request timed out. Please try again later.' 
      }, { status: 504 });
    });
}

// Separate the actual handler logic for cleaner error handling
async function handleDeleteRequest(request: NextRequest) {
  try {
    console.log('Clear all notifications API: Request received');
    
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      console.log('Clear all notifications API: Unauthorized request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      console.log('Clear all notifications API: No Discord ID found');
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    console.log(`Clear all notifications API: Clearing notifications for user ${discordId.slice(0, 6)}...`);
    
    // Connect to MongoDB with timeout options
    const client = new MongoClient(uri, {
      serverSelectionTimeoutMS: 5000, // 5 seconds
      connectTimeoutMS: 5000, // 5 seconds
      socketTimeoutMS: 5000 // 5 seconds
    });
    
    try {
      await client.connect();
      const db = client.db("codehub");
      
      // Clear all notifications for the user
      const result = await db.collection('users').updateOne(
        { discordUserId: discordId },
        { 
          $set: { notifications: [] }
        }
      );
      
      await client.close();
      
      if (result.matchedCount === 0) {
        console.log(`Clear all notifications API: No user found with ID ${discordId.slice(0, 6)}`);
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      
      console.log(`Clear all notifications API: Successfully cleared notifications for user ${discordId.slice(0, 6)}`);
      
      return NextResponse.json({
        success: true,
        message: 'All notifications cleared successfully',
        deletedCount: result.modifiedCount > 0 ? 'all' : 0
      });
    } catch (dbError) {
      console.error('Clear all notifications API: Database error:', dbError);
      await client.close();
      return NextResponse.json({ 
        error: 'Database error occurred' 
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Clear all notifications API: Unexpected error:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
