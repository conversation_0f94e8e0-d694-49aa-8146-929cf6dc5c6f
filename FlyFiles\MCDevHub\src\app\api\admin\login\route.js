import { NextResponse } from 'next/server';
import { MongoClient } from 'mongodb';
import { cookies } from 'next/headers';
import { sign } from 'jsonwebtoken';
import bcrypt from 'bcrypt';

export const runtime = 'nodejs'; // Use Node.js runtime

// MongoDB configuration
if (!process.env.MONGODB_URI) {
  throw new Error('Please add your Mongo URI to .env.local');
}

// JWT Secret - make sure to add this to your .env.local file
if (!process.env.JWT_SECRET) {
  throw new Error('Please add JWT_SECRET to .env.local');
}

const uri = process.env.MONGODB_URI;
const options = {};

// Set up MongoDB client with proper connection pooling
let client;
let clientPromise;

if (process.env.NODE_ENV === 'development') {
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri, options);
    global._mongoClientPromise = client.connect();
  }
  clientPromise = global._mongoClientPromise;
} else {
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

export async function POST(request) {
  try {
    const { username, password } = await request.json();

    // Basic validation
    if (!username || !password) {
      return NextResponse.json(
        { success: false, message: 'Brugernavn og adgangskode er påkrævet' },
        { status: 400 }
      );
    }

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db("codehub");
    const adminsCollection = db.collection("adminusers");

    // Find admin by username
    const admin = await adminsCollection.findOne({ username });

    // Check if admin exists
    if (!admin) {
      return NextResponse.json(
        { success: false, message: 'Forkert brugernavn eller adgangskode' },
        { status: 401 }
      );
    }

    // Verify password with bcrypt
    const passwordMatch = await bcrypt.compare(password, admin.password);
    if (!passwordMatch) {
      return NextResponse.json(
        { success: false, message: 'Forkert brugernavn eller adgangskode' },
        { status: 401 }
      );
    }

    // Create a session token with admin info
    const token = sign(
      {
        id: admin._id.toString(),
        username: admin.username,
        admintype: admin.admintype,
        allowedcases: admin.allowedcases
      },
      process.env.JWT_SECRET,
      { expiresIn: '24h' } // Token valid for 24 hours
    );

    // Create a response with success message
    const response = NextResponse.json({
      success: true,
      message: 'Login succesfuld',
      user: {
        username: admin.username,
        admintype: admin.admintype,
        allowedcases: admin.allowedcases
      }
    });

    // Set the cookie in the response
    response.cookies.set('admin_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60 * 24, // 24 hours in seconds
      path: '/',
    });

    return response;
  } catch (error) {
    console.error('Error during login:', error);
    return NextResponse.json(
      { success: false, message: 'Der opstod en fejl under login' },
      { status: 500 }
    );
  }
} 