import { MongoClient, Db } from 'mongodb';
import { IBadge, BADGE_DEFINITIONS, BADGE_COLLECTIONS, IUserWithBadges, IAdminUserWithBadges } from '../models/badge';
import { BadgeCalculationService } from './badgeCalculationService';
import { addNotification } from '../notifications';

/**
 * Badge award service for managing badge awards and storage
 */
export class BadgeAwardService {
  private db: Db;
  private calculationService: BadgeCalculationService;

  constructor(db: Db) {
    this.db = db;
    this.calculationService = new BadgeCalculationService(db);
  }

  /**
   * Get user's current badges
   */
  async getUserBadges(discordUserId: string): Promise<IBadge[]> {
    // Check if user is freelancer first
    const adminUser = await this.db.collection(BADGE_COLLECTIONS.ADMIN_USERS).findOne({
      discordUserId: discordUserId
    });

    let user;
    if (adminUser) {
      user = adminUser;
    } else {
      user = await this.db.collection(BADGE_COLLECTIONS.USERS).findOne({
        discordUserId: discordUserId
      });
    }

    return user?.badges || [];
  }

  /**
   * Award a badge to a user
   */
  async awardBadge(discordUserId: string, badgeId: string): Promise<boolean> {
    const badge: IBadge = {
      id: badgeId,
      earnedAt: new Date(),
      notified: false
    };

    // Check if user is freelancer first
    const adminUser = await this.db.collection(BADGE_COLLECTIONS.ADMIN_USERS).findOne({
      discordUserId: discordUserId
    });

    let collection: string;
    let userExists: boolean;

    if (adminUser) {
      collection = BADGE_COLLECTIONS.ADMIN_USERS;
      userExists = true;
    } else {
      collection = BADGE_COLLECTIONS.USERS;
      const regularUser = await this.db.collection(BADGE_COLLECTIONS.USERS).findOne({
        discordUserId: discordUserId
      });
      userExists = !!regularUser;
    }

    // Create user document if it doesn't exist (for regular users)
    if (!userExists && collection === BADGE_COLLECTIONS.USERS) {
      const newUserDoc: IUserWithBadges = {
        discordUserId: discordUserId,
        badges: [badge],
        createdAt: new Date()
      };

      await this.db.collection(BADGE_COLLECTIONS.USERS).insertOne(newUserDoc);
    } else {
      // Add badge to existing user, avoiding duplicates
      const updateDoc = {
        $push: { badges: badge },
        $setOnInsert: { createdAt: new Date() }
      };

      await this.db.collection(collection).updateOne(
        {
          discordUserId: discordUserId,
          'badges.id': { $ne: badgeId } // Only add if badge doesn't already exist
        },
        updateDoc as any, // Type assertion for MongoDB update document
        { upsert: true }
      );
    }

    // Send notification about new badge
    const badgeDefinition = BADGE_DEFINITIONS[badgeId];
    if (badgeDefinition) {
      await addNotification(discordUserId, {
        title: 'Nyt Badge Optjent! 🎉',
        message: `Du har optjent "${badgeDefinition.name}" badge: ${badgeDefinition.description}`,
        type: 'badge',
        link: '/profile'
      });

      // Mark badge as notified
      await this.db.collection(collection).updateOne(
        { 
          discordUserId: discordUserId,
          'badges.id': badgeId
        },
        { 
          $set: { 'badges.$.notified': true }
        }
      );
    }

    return true;
  }

  /**
   * Check and award new badges for a user
   */
  async checkAndAwardBadges(discordUserId: string): Promise<string[]> {
    // Get current badges
    const currentBadges = await this.getUserBadges(discordUserId);
    const currentBadgeIds = currentBadges.map(b => b.id);

    // Calculate badge eligibility
    const eligibility = await this.calculationService.calculateAllBadges(discordUserId);

    // Find new badges to award
    const newBadges: string[] = [];
    
    for (const [badgeId, criteria] of Object.entries(eligibility)) {
      if (criteria.eligible && !currentBadgeIds.includes(badgeId)) {
        await this.awardBadge(discordUserId, badgeId);
        newBadges.push(badgeId);
      }
    }

    return newBadges;
  }

  /**
   * Get badge progress for a user (for displaying progress bars)
   */
  async getBadgeProgress(discordUserId: string): Promise<Record<string, any>> {
    const currentBadges = await this.getUserBadges(discordUserId);
    const currentBadgeIds = currentBadges.map(b => b.id);
    const eligibility = await this.calculationService.calculateAllBadges(discordUserId);

    const progress: Record<string, any> = {};

    for (const [badgeId, criteria] of Object.entries(eligibility)) {
      const badgeDefinition = BADGE_DEFINITIONS[badgeId];
      const isEarned = currentBadgeIds.includes(badgeId);
      const earnedAt = isEarned ? currentBadges.find(b => b.id === badgeId)?.earnedAt : null;

      progress[badgeId] = {
        ...badgeDefinition,
        earned: isEarned,
        earnedAt: earnedAt,
        eligible: criteria.eligible,
        progress: criteria.progress || 0,
        maxProgress: criteria.maxProgress || 100,
        progressPercentage: criteria.maxProgress ? 
          Math.min(100, ((criteria.progress || 0) / criteria.maxProgress) * 100) : 
          (criteria.eligible ? 100 : 0)
      };
    }

    return progress;
  }

  /**
   * Get user's earned badges with definitions
   */
  async getUserBadgesWithDefinitions(discordUserId: string): Promise<any[]> {
    const badges = await this.getUserBadges(discordUserId);
    
    return badges.map(badge => ({
      ...badge,
      ...BADGE_DEFINITIONS[badge.id]
    })).filter(badge => badge.name); // Filter out badges with missing definitions
  }

  /**
   * Trigger badge check on user action (to be called from various endpoints)
   */
  async triggerBadgeCheck(discordUserId: string, action?: string): Promise<void> {
    try {
      const newBadges = await this.checkAndAwardBadges(discordUserId);
      
      if (newBadges.length > 0) {
        console.log(`Awarded ${newBadges.length} new badges to user ${discordUserId}: ${newBadges.join(', ')}`);
      }
    } catch (error) {
      console.error('Error checking badges for user:', discordUserId, error);
    }
  }

  /**
   * Get badge statistics for admin dashboard
   */
  async getBadgeStatistics(): Promise<Record<string, any>> {
    const stats: Record<string, any> = {};

    // Get all users with badges
    const adminUsers = await this.db.collection(BADGE_COLLECTIONS.ADMIN_USERS)
      .find({ badges: { $exists: true, $ne: [] } })
      .toArray();
    
    const regularUsers = await this.db.collection(BADGE_COLLECTIONS.USERS)
      .find({ badges: { $exists: true, $ne: [] } })
      .toArray();

    const allUsers = [...adminUsers, ...regularUsers];

    // Count badge awards
    for (const badgeId of Object.keys(BADGE_DEFINITIONS)) {
      let count = 0;
      allUsers.forEach(user => {
        if (user.badges && user.badges.some(b => b.id === badgeId)) {
          count++;
        }
      });
      
      stats[badgeId] = {
        ...BADGE_DEFINITIONS[badgeId],
        awardedCount: count
      };
    }

    return stats;
  }

  /**
   * Bulk check badges for all users (for maintenance/migration)
   */
  async bulkCheckAllUsers(): Promise<void> {
    console.log('Starting bulk badge check for all users...');

    // Get all admin users
    const adminUsers = await this.db.collection(BADGE_COLLECTIONS.ADMIN_USERS)
      .find({})
      .toArray();

    // Get all regular users
    const regularUsers = await this.db.collection(BADGE_COLLECTIONS.USERS)
      .find({})
      .toArray();

    const allUsers = [...adminUsers, ...regularUsers];
    let processed = 0;

    for (const user of allUsers) {
      if (user.discordUserId) {
        try {
          await this.checkAndAwardBadges(user.discordUserId);
          processed++;
          
          if (processed % 10 === 0) {
            console.log(`Processed ${processed}/${allUsers.length} users`);
          }
        } catch (error) {
          console.error(`Error processing user ${user.discordUserId}:`, error);
        }
      }
    }

    console.log(`Bulk badge check completed. Processed ${processed} users.`);
  }
}
