import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { username: string } }
) {
  console.log('=== Profile Username API Route (Full Version) ===');
  try {
    const { username } = params;
    console.log('1. Received username:', username);
    
    if (!username) {
      console.log('ERROR: Username is missing');
      return NextResponse.json({ error: 'Username is required' }, { status: 400 });
    }

    // Import dependencies dynamically to catch import errors
    let connectToDatabase, auth, ObjectId, getProfileDataAccess, filterProfileData;
    
    try {
      console.log('2. Importing dependencies...');
      ({ connectToDatabase } = await import('@/lib/mongodb.js'));
      ({ auth } = await import('@/lib/auth.js'));
      ({ ObjectId } = await import('mongodb'));
      ({ getProfileDataAccess, filterProfileData } = await import('@/lib/profilePermissions'));
      console.log('3. All dependencies imported successfully');
    } catch (importError) {
      console.error('Import error:', importError);
      return NextResponse.json({
        error: 'Failed to import dependencies',
        details: importError instanceof Error ? importError.message : 'Unknown import error'
      }, { status: 500 });
    }

    // Get auth session
    console.log('4. Getting auth session...');
    let session;
    try {
      session = await auth();
      console.log('5. Auth session:', session ? 'Found' : 'None');
    } catch (authError) {
      console.error('Auth error:', authError);
      // Continue without session - auth is optional for profile viewing
      session = null;
    }
    
    // Connect to database
    console.log('6. Connecting to database...');
    let db;
    try {
      ({ db } = await connectToDatabase());
      console.log('7. Database connected successfully');
    } catch (dbError) {
      console.error('Database connection error:', dbError);
      return NextResponse.json({
        error: 'Database connection failed',
        details: dbError instanceof Error ? dbError.message : 'Unknown database error'
      }, { status: 500 });
    }
    
    // Look up Discord account by username
    console.log('8. Looking up Discord account by username...');
    let discordAccount;
    try {
      discordAccount = await db.collection('accounts').findOne(
        {
          provider: 'discord',
          username: new RegExp(`^${username}$`, 'i') // Case-insensitive match
        },
        {
          projection: {
            userId: 1,
            username: 1,
            avatar: 1,
            avatarUrl: 1,
            discordId: 1,
            email: 1
          }
        }
      );
      
      console.log('9. Discord account lookup result:', discordAccount ? 'Found' : 'Not found');
      if (discordAccount) {
        console.log('   - Username:', discordAccount.username);
        console.log('   - Discord ID:', discordAccount.discordId);
      }
    } catch (lookupError) {
      console.error('Discord account lookup error:', lookupError);
      return NextResponse.json({
        error: 'Failed to lookup Discord account',
        details: lookupError instanceof Error ? lookupError.message : 'Unknown lookup error'
      }, { status: 500 });
    }

    if (!discordAccount) {
      console.log('ERROR: Discord account not found for username:', username);
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const discordUserId = discordAccount.discordId;
    console.log('10. Using Discord User ID:', discordUserId);
    
    // Look up user in adminusers and users collections
    console.log('11. Looking up user data...');
    let user, isFreelancer = false;
    
    try {
      // Try adminusers first
      user = await db.collection('adminusers').findOne(
        { discordUserId: discordUserId },
        {
          projection: {
            username: 1,
            discordUserId: 1,
            avatar: 1,
            avatarUrl: 1,
            bannerImage: 1,
            description: 1,
            isVerified: 1,
            verifiedAt: 1,
            email: 1,
            youtubeUrl: 1,
            githubUsername: 1,
            createdAt: 1,
            openForTasks: 1,
            admintype: 1,
            badges: 1,
            password: 0,
            allowedcases: 0
          }
        }
      );

      if (user) {
        isFreelancer = user.admintype === 'Freelancer';
        console.log('12. Found in adminusers - Admin type:', user.admintype);
      } else {
        // Try regular users collection
        user = await db.collection('users').findOne(
          { discordUserId: discordUserId },
          {
            projection: {
              username: 1,
              discordUserId: 1,
              createdAt: 1,
              badges: 1,
              auth_id: 0,
              balance: 0,
              notifications: 0
            }
          }
        );
        console.log('13. Regular users lookup result:', user ? 'Found' : 'Not found');
      }

      // If no user found, create basic user object
      if (!user) {
        console.log('14. Creating basic user object from Discord account...');
        user = {
          username: discordAccount.username,
          discordUserId: discordUserId,
          avatar: discordAccount.avatar,
          avatarUrl: discordAccount.avatarUrl,
          email: discordAccount.email,
          createdAt: new Date(),
          badges: []
        };
      }
    } catch (userLookupError) {
      console.error('User lookup error:', userLookupError);
      return NextResponse.json({
        error: 'Failed to lookup user data',
        details: userLookupError instanceof Error ? userLookupError.message : 'Unknown user lookup error'
      }, { status: 500 });
    }

    // Get purchases, favorites, and verification data
    console.log('15. Fetching additional user data...');
    let purchases = [], favorites = [], verificationStatus = null;

    try {
      // Get purchases
      purchases = await db.collection('user_purchases')
        .find(
          { userId: discordUserId },
          {
            projection: {
              productId: 1,
              productName: 1,
              productType: 1,
              amount: 1,
              purchaseDate: 1,
              seller: 1,
              paymentMethod: 0,
              transactionId: 0,
              stripeSessionId: 0
            }
          }
        )
        .sort({ purchaseDate: -1 })
        .limit(50)
        .toArray();

      // Enhance purchases with product thumbnails
      purchases = await Promise.all(purchases.map(async (purchase: any) => {
        try {
          let product = null;
          const productId = purchase.productId;

          // Try to find the product
          product = await db.collection('products').findOne({ _id: productId });

          if (!product && ObjectId.isValid(productId)) {
            product = await db.collection('products').findOne({ _id: new ObjectId(productId) });
          }

          if (product && product.screenshotUrls && product.screenshotUrls.length > 0) {
            const screenshot = product.screenshotUrls.find((ss: any) => ss.url) || product.screenshotUrls[0];

            return {
              ...purchase,
              thumbnailUrl: screenshot.url || null
            };
          }

          return purchase;
        } catch (error) {
          console.error(`Error fetching product details for purchase ${purchase._id}:`, error);
          return purchase;
        }
      }));

      // Get favorites
      const userWithFavorites = await db.collection('users').findOne(
        { discordUserId: discordUserId },
        { projection: { favorites: 1 } }
      );
      favorites = userWithFavorites?.favorites || [];

      // Get verification status
      verificationStatus = await db.collection('verified').findOne(
        { discordId: discordUserId },
        { projection: { isVerified: 1, verifiedAt: 1 } }
      );

      console.log('16. Additional data fetched - Purchases:', purchases.length, 'Favorites:', favorites.length);
    } catch (dataError) {
      console.error('Additional data fetch error:', dataError);
      // Continue with empty data rather than failing
      purchases = [];
      favorites = [];
      verificationStatus = null;
    }

    // Apply permissions and build response
    console.log('17. Applying permissions and building response...');
    try {
      const currentUserId = session?.user?.id;
      const isAdmin = session?.user?.isAdmin || false;
      const access = getProfileDataAccess(currentUserId, discordUserId, isAdmin);

      const fullProfile = {
        user: {
          username: user.username || discordAccount.username,
          discordUserId: discordUserId,
          avatar: user.avatar || discordAccount.avatar,
          avatarUrl: user.avatarUrl || discordAccount.avatarUrl,
          bannerImage: user.bannerImage,
          description: user.description,
          createdAt: user.createdAt,
          badges: user.badges || [],
          isFreelancer,
          email: user.email || discordAccount.email,
          youtubeUrl: user.youtubeUrl,
          githubUsername: user.githubUsername,
          openForTasks: user.openForTasks
        },
        purchases,
        favorites,
        verification: {
          isVerified: verificationStatus?.isVerified || user.isVerified || false,
          verifiedAt: verificationStatus?.verifiedAt || user.verifiedAt || null
        },
        isOwnProfile: currentUserId === discordUserId
      };

      const filteredProfile = filterProfileData(fullProfile, access);
      console.log('18. Profile data prepared successfully');
      
      return NextResponse.json(filteredProfile);
    } catch (permissionError) {
      console.error('Permission/filtering error:', permissionError);
      return NextResponse.json({
        error: 'Failed to process profile permissions',
        details: permissionError instanceof Error ? permissionError.message : 'Unknown permission error'
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('=== UNEXPECTED ERROR in Profile Username API Route ===');
    console.error('Error details:', error);
    console.error('Error message:', error instanceof Error ? error.message : 'Unknown error');
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
