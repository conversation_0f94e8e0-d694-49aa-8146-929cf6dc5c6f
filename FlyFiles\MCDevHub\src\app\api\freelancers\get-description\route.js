import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { verifyAdminToken } from '@/lib/auth';

export async function GET() {
  try {
    // Verify the admin user is a freelancer
    const admin = await verifyAdminToken();
    
    if (!admin) {
      console.log('No admin user found');
      return NextResponse.json(
        { message: 'Ikke autoriseret' },
        { status: 401 }
      );
    }
    
    console.log('Admin user found:', admin.username);
    
    // Connect to database
    const { db } = await connectToDatabase();
    
    // Fetch the description directly from MongoDB
    const result = await db.collection('adminusers').findOne(
      { username: admin.username },
      { projection: { description: 1 } }
    );
    
    console.log('Found description for:', admin.username, result ? 'Yes' : 'No');
    
    // Return the description (may be undefined)
    return NextResponse.json({
      description: result?.description || '',
      username: admin.username
    });
    
  } catch (error) {
    console.error('Error getting freelancer description:', error);
    return NextResponse.json(
      { message: 'Der opstod en fejl ved hentning af beskrivelse' },
      { status: 500 }
    );
  }
} 