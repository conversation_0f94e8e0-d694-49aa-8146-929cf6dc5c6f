import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import { BadgeAwardService } from '@/lib/services/badgeAwardService';
import { getProfileDataAccess } from '@/lib/profilePermissions';

/**
 * GET /api/badges/user/[userId] - Get badges for a specific user
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const params = await context.params;
    const { userId } = params;
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Get the current user from NextAuth
    const session = await auth();
    const currentUserId = session?.user?.id;
    const isAdmin = session?.user?.isAdmin || false;

    // Get access permissions
    const access = getProfileDataAccess(currentUserId, userId, isAdmin);

    // Connect to database
    const { db } = await connectToDatabase();
    const badgeService = new BadgeAwardService(db);

    // Get query parameters
    const url = new URL(request.url);
    const includeProgress = url.searchParams.get('includeProgress') === 'true';

    // Check if user can view badge progress (only for own profile)
    const canViewProgress = access.canViewBadgeProgress && includeProgress;

    if (canViewProgress) {
      // Get badge progress (for profile owner only)
      const progress = await badgeService.getBadgeProgress(userId);
      
      return NextResponse.json({
        success: true,
        badges: progress,
        showProgress: true
      });
    } else {
      // Get only earned badges (public view)
      const badges = await badgeService.getUserBadgesWithDefinitions(userId);

      // Convert array to object format expected by BadgeDisplay component
      const badgeObject: Record<string, any> = {};
      badges.forEach((badge: any) => {
        badgeObject[badge.id] = {
          ...badge,
          earned: true, // These are already earned badges
          // Remove progress information for public viewing
          progress: undefined,
          maxProgress: undefined,
          progressPercentage: undefined
        };
      });

      return NextResponse.json({
        success: true,
        badges: badgeObject,
        showProgress: false
      });
    }

  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
