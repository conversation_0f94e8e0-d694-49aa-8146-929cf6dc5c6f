import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { auth } from '@/lib/auth';
import { triggerBadgeCheck } from '@/lib/utils/badgeUtils';
import { v4 as uuidv4 } from 'uuid';

export async function POST(req: NextRequest) {
  try {
    // Get product ID from request body
    const { productId } = await req.json();

    if (!productId) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // Connect to database
    const { db } = await connectToDatabase();
    
    // Get the current user from NextAuth
    const userSession = await auth();

    if (!userSession?.user) {
      return NextResponse.json({ error: 'User is not logged in' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = userSession.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Try to find the product with different ID formats
    let product = null;
    
    // First try with string ID
    product = await db.collection('products').findOne({ _id: productId });
    
    // If not found and it's a valid ObjectId format, try with ObjectId
    if (!product && ObjectId.isValid(productId)) {
      product = await db.collection('products').findOne({ _id: new ObjectId(productId) });
    }
    
    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }
    
    // Check if the user has already purchased this product
    const existingPurchase = await db.collection('user_purchases').findOne({
      userId: discordId,
      productId: product._id.toString()
    });
    
    if (existingPurchase) {
      return NextResponse.json({ 
        error: 'Already purchased',
        message: 'You already own this product' 
      }, { status: 409 }); // 409 Conflict
    }
    
    // Get user's balance
    const userBalance = await db.collection('user_balances').findOne({
      userId: discordId
    });
    
    const balance = userBalance?.balance || 0;
    
    // Check if user has enough balance
    if (balance < product.price) {
      return NextResponse.json({ 
        error: 'Insufficient balance',
        message: 'Not enough balance to make this purchase' 
      }, { status: 400 });
    }
    
    // Deduct from user's balance
    await db.collection('user_balances').updateOne(
      { userId: discordId },
      { 
        $inc: { balance: -product.price },
        $set: { lastUpdated: new Date() }
      },
      { upsert: true }
    );
    
    // Create unique transaction ID
    const transactionId = new ObjectId().toString();
    
    // Get buyer username from NextAuth user data
    const buyerName = userSession.user.name || "Unknown User";
    
    // Get the thumbnail URL from the product's screenshots if available
    let thumbnailUrl = null;
    if (product.screenshotUrls && product.screenshotUrls.length > 0) {
      // Find the first screenshot that has a URL
      const screenshot = product.screenshotUrls.find(ss => ss.url) || product.screenshotUrls[0];
      thumbnailUrl = screenshot.url || null;
    }
    
    // Create purchase record
    await db.collection('user_purchases').insertOne({
      userId: discordId,
      productId: product._id.toString(),
      productName: product.projectName,
      productType: product.productType || null,
      amount: product.price,
      purchaseDate: new Date(),
      transactionId: transactionId,
      paymentMethod: 'Balance',
      seller: product.createdBy || 'MCDevHub',
      thumbnailUrl: thumbnailUrl
    });
    
    // If we have a valid seller ID, create a transaction and update their balance
    if (product.discordUserId) {
      const sellerId = product.discordUserId;
      
      // Record transaction
      await db.collection('transactions').insertOne({
        sellerId: sellerId,
        buyerId: discordId,
        buyerName: buyerName,
        productId: product._id.toString(),
        productName: product.projectName,
        amount: product.price,
        createdAt: new Date(),
        transactionId: transactionId
      });
      
      // Update seller's balance
      // Adjust commission as needed (currently 85% to seller, 15% platform fee)
      const sellerShare = product.price * 0.85;
      
      // Update seller balance
      await db.collection('user_balances').updateOne(
        { userId: sellerId },
        { 
          $inc: { balance: sellerShare },
          $set: { lastUpdated: new Date() }
        },
        { upsert: true }
      );
      
      console.log(`Updated balance for seller ${sellerId}, added ${sellerShare} DKK`);
    }

    // Trigger badge check for buyer after successful purchase
    triggerBadgeCheck(discordId, 'balance_purchase').catch(error => {
      console.error('Error checking badges for buyer:', error);
    });

    // Trigger badge check for seller after successful sale
    if (product.discordUserId) {
      triggerBadgeCheck(product.discordUserId, 'sale').catch(error => {
        console.error('Error checking badges for seller:', error);
      });
    }

    // Return success response
    return NextResponse.json({
      success: true,
      productId: product._id,
      productName: product.projectName,
      amount: product.price,
      message: 'Purchase completed successfully'
    });
  } catch (error) {
    console.error('Error processing balance purchase:', error);
    return NextResponse.json({
      error: 'Failed to process purchase',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
} 