import { auth } from '@/lib/auth.js';

/**
 * Profile permission levels
 */
export enum ProfilePermissionLevel {
  PUBLIC = 'public',           // Anyone can view
  AUTHENTICATED = 'authenticated', // Only logged-in users can view
  OWNER = 'owner',            // Only profile owner can view/edit
  ADMIN = 'admin'             // Only admins can view/edit
}

/**
 * Profile data access levels
 */
export interface ProfileDataAccess {
  canViewBasicInfo: boolean;      // Username, avatar, creation date
  canViewPurchases: boolean;      // Purchase history
  canViewFavorites: boolean;      // Favorite products
  canViewNotifications: boolean;  // Private notifications
  canViewPrivateProfile: boolean; // Private profile settings
  canEditProfile: boolean;        // Can modify profile
  canViewBadgeProgress: boolean;  // Can see badge progress
  canViewSocialLinks: boolean;    // Can see social media links
  canViewAvailability: boolean;   // Can see freelancer availability
}

/**
 * Check if user has permission to access another user's profile data
 */
export async function checkProfileAccess(
  targetUserId: string,
  requestedAccess: keyof ProfileDataAccess
): Promise<boolean> {
  try {
    const session = await auth();
    const currentUserId = session?.user?.id;
    const isAdmin = session?.user?.isAdmin;
    
    // Get access permissions for the current user viewing the target profile
    const access = getProfileDataAccess(currentUserId, targetUserId, isAdmin);
    
    return access[requestedAccess];
  } catch (error) {
    console.error('Error checking profile access:', error);
    return false;
  }
}

/**
 * Get profile data access permissions based on viewer and target user
 */
export function getProfileDataAccess(
  viewerUserId: string | null | undefined,
  targetUserId: string,
  isViewerAdmin: boolean = false
): ProfileDataAccess {
  const isOwnProfile = viewerUserId === targetUserId;
  const isLoggedIn = !!viewerUserId;
  
  // Profile owner has full access
  if (isOwnProfile) {
    return {
      canViewBasicInfo: true,
      canViewPurchases: true,
      canViewFavorites: true,
      canViewNotifications: true,
      canViewPrivateProfile: true,
      canEditProfile: true,
      canViewBadgeProgress: true,
      canViewSocialLinks: true,
      canViewAvailability: true
    };
  }
  
  // Admin has elevated access (but not full access to private data)
  if (isViewerAdmin) {
    return {
      canViewBasicInfo: true,
      canViewPurchases: true,
      canViewFavorites: true,
      canViewNotifications: false, // Notifications remain private
      canViewPrivateProfile: false, // Private settings remain private
      canEditProfile: false, // Admins can't edit other profiles
      canViewBadgeProgress: false, // Badge progress is private
      canViewSocialLinks: true,
      canViewAvailability: true
    };
  }
  
  // Logged-in users have public access
  if (isLoggedIn) {
    return {
      canViewBasicInfo: true,
      canViewPurchases: true,
      canViewFavorites: true,
      canViewNotifications: false,
      canViewPrivateProfile: false,
      canEditProfile: false,
      canViewBadgeProgress: false,
      canViewSocialLinks: true,
      canViewAvailability: true
    };
  }
  
  // Anonymous users have limited public access
  return {
    canViewBasicInfo: true,
    canViewPurchases: false, // Anonymous users can't see purchases
    canViewFavorites: false, // Anonymous users can't see favorites
    canViewNotifications: false,
    canViewPrivateProfile: false,
    canEditProfile: false,
    canViewBadgeProgress: false,
    canViewSocialLinks: true,
    canViewAvailability: true
  };
}

/**
 * Filter profile data based on access permissions
 */
export function filterProfileData(
  profileData: any,
  access: ProfileDataAccess
): any {
  const filtered: any = {};
  
  // Always include basic info if allowed
  if (access.canViewBasicInfo) {
    filtered.user = {
      username: profileData.user?.username,
      discordUserId: profileData.user?.discordUserId,
      avatar: profileData.user?.avatar,
      avatarUrl: profileData.user?.avatarUrl,
      bannerImage: profileData.user?.bannerImage,
      description: profileData.user?.description,
      createdAt: profileData.user?.createdAt,
      badges: profileData.user?.badges || [],
      isFreelancer: profileData.user?.isFreelancer
    };
    
    // Add social links if allowed
    if (access.canViewSocialLinks) {
      filtered.user.email = profileData.user?.email;
      filtered.user.youtubeUrl = profileData.user?.youtubeUrl;
      filtered.user.githubUsername = profileData.user?.githubUsername;
    }
    
    // Add availability if allowed
    if (access.canViewAvailability) {
      filtered.user.openForTasks = profileData.user?.openForTasks;
    }
  }
  
  // Include purchases if allowed
  if (access.canViewPurchases) {
    filtered.purchases = profileData.purchases || [];
  } else {
    filtered.purchases = [];
  }
  
  // Include favorites if allowed
  if (access.canViewFavorites) {
    filtered.favorites = profileData.favorites || [];
  } else {
    filtered.favorites = [];
  }
  
  // Include notifications only for profile owner
  if (access.canViewNotifications) {
    filtered.notifications = profileData.notifications || [];
  }
  
  // Include verification status (public information)
  filtered.verification = profileData.verification || {
    isVerified: false,
    verifiedAt: null
  };
  
  // Include ownership flag
  filtered.isOwnProfile = access.canEditProfile;
  
  return filtered;
}

/**
 * Validate that a user can perform a specific action on a profile
 */
export async function validateProfileAction(
  targetUserId: string,
  action: 'view' | 'edit' | 'delete'
): Promise<{ allowed: boolean; reason?: string }> {
  try {
    const session = await auth();
    const currentUserId = session?.user?.id;
    const isAdmin = session?.user?.isAdmin;
    
    // Check if user is logged in for any action
    if (!currentUserId) {
      return { allowed: false, reason: 'Du skal være logget ind for at udføre denne handling' };
    }
    
    const isOwnProfile = currentUserId === targetUserId;
    
    switch (action) {
      case 'view':
        // Anyone can view public profiles
        return { allowed: true };
        
      case 'edit':
        // Only profile owner can edit
        if (!isOwnProfile) {
          return { allowed: false, reason: 'Du kan kun redigere din egen profil' };
        }
        return { allowed: true };
        
      case 'delete':
        // Only profile owner or admin can delete
        if (!isOwnProfile && !isAdmin) {
          return { allowed: false, reason: 'Du kan kun slette din egen profil' };
        }
        return { allowed: true };
        
      default:
        return { allowed: false, reason: 'Ukendt handling' };
    }
  } catch (error) {
    console.error('Error validating profile action:', error);
    return { allowed: false, reason: 'Der opstod en fejl ved validering af tilladelser' };
  }
}

/**
 * Sanitize user input for profile updates
 */
export function sanitizeProfileInput(input: any): any {
  const sanitized: any = {};
  
  // Only allow specific fields to be updated
  const allowedFields = [
    'description',
    'githubUsername',
    'youtubeUrl',
    'email',
    'openForTasks',
    'bannerImage'
  ];
  
  allowedFields.forEach(field => {
    if (input[field] !== undefined) {
      // Basic sanitization
      if (typeof input[field] === 'string') {
        sanitized[field] = input[field].trim().substring(0, 1000); // Limit length
      } else if (typeof input[field] === 'boolean') {
        sanitized[field] = input[field];
      }
    }
  });
  
  return sanitized;
}
