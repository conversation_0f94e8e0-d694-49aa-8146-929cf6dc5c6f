/**
 * Image utility functions
 */

/**
 * Get the full URL for an image from the backend
 * @param path The image path or object from the database
 * @returns The complete URL to the image
 */
export function getImageUrl(pathOrObject: string | { url?: string, fileId?: string } | null | undefined): string {
  console.log("getImageUrl input:", pathOrObject);
  
  // Handle null or undefined input
  if (pathOrObject === null || pathOrObject === undefined) {
    console.log("Input is null or undefined, using placeholder");
    return '/static/placeholders/image-placeholder.jpg';
  }
  
  // Handle object format (used in newer entries with Cloudinary)
  if (typeof pathOrObject === 'object' && pathOrObject !== null) {
    // If the object has a url property (Cloudinary URLs will have this directly)
    if (pathOrObject.url) {
      console.log("Using direct URL:", pathOrObject.url);
      return pathOrObject.url;
    }
    
    // Fall back to fileId if no url is present
    if (pathOrObject.fileId) {
      console.log("Using fileId:", pathOrObject.fileId);
      return getImageUrl(pathOrObject.fileId);
    }
    
    console.log("No valid URL or fileId found, using placeholder");
    // Return placeholder if no valid data
    return '/static/placeholders/image-placeholder.jpg';
  }
  
  // From here on, pathOrObject is definitely a string
  const path = pathOrObject as string;
  console.log("Processing string path:", path);
  
  // Return as-is if it's already a full URL (including Cloudinary URLs)
  if (path.startsWith('http://') || path.startsWith('https://')) {
    console.log("Using full URL as-is:", path);
    return path;
  }
  
  // If it's a Cloudinary public ID (not a full URL but from Cloudinary)
  if (path.includes('cloudinary') || path.startsWith('screenshots/') || path.startsWith('files/')) {
    // Construct Cloudinary URL using environment variable
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || process.env.CLOUDINARY_CLOUD_NAME || 'dwqxk2tip';
    const url = `https://res.cloudinary.com/${cloudName}/image/upload/${path}`;
    console.log("Using Cloudinary URL:", url);
    return url;
  }
  
  // Handle legacy API URL paths
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api.mcdevhub.dk';
  
  let finalUrl;
  // Handle different path formats for the old API
  if (path.startsWith('/')) {
    finalUrl = `${apiUrl}${path}`;
  } else {
    finalUrl = `${apiUrl}/public/${path}`;
  }
  
  console.log("Using API URL:", finalUrl);
  return finalUrl;
}

/**
 * Get optimized image component props for next/image
 * @param path The image path from the database
 * @param width Desired width
 * @param height Desired height
 * @returns Props for the next/image component
 */
export function getNextImageProps(path: string | { url?: string, fileId?: string } | null | undefined, width: number, height: number) {
  return {
    src: getImageUrl(path),
    width,
    height,
    alt: 'Image',
    loading: 'lazy',
    sizes: `(max-width: 768px) 100vw, ${width}px`,
  };
} 