import { v2 as cloudinary } from 'cloudinary';
import { Readable } from 'stream';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'your-cloud-name',
  api_key: process.env.CLOUDINARY_API_KEY || 'your-api-key',
  api_secret: process.env.CLOUDINARY_API_SECRET || 'your-api-secret',
  secure: true
});

/**
 * Upload an image to Cloudinary
 * @param buffer - The image buffer
 * @param folder - The folder to upload to (e.g., 'screenshots', 'files')
 * @param filename - The filename
 * @param contentType - The content type (MIME type)
 * @returns The upload result
 */
export async function uploadToCloudinary(
  buffer: Buffer, 
  folder: string, 
  filename: string, 
  contentType: string
): Promise<{ url: string; path: string; width: number; height: number; format: string } | { error: string }> {
  try {
    // Create a promise wrapper for the upload
    return new Promise((resolve, reject) => {
      // Upload stream to Cloudinary
      const uploadStream = cloudinary.uploader.upload_stream(
        {
          folder: folder,
          public_id: filename.replace(/\.[^/.]+$/, ""), // Remove extension for public_id
          resource_type: 'auto',
          overwrite: true, // Allow overwriting existing files
        },
        (error, result) => {
          if (error) {
            console.error('Cloudinary upload error:', error);
            reject({ error: error.message });
          } else {
            resolve({
              url: result.secure_url,
              path: result.public_id,
              width: result.width,
              height: result.height,
              format: result.format,
            });
          }
        }
      );

      // Convert buffer to stream and pipe to Cloudinary
      const readableStream = new Readable();
      readableStream.push(buffer);
      readableStream.push(null);
      readableStream.pipe(uploadStream);
    });
  } catch (error) {
    console.error('Error in Cloudinary upload:', error);
    return { error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Delete an image from Cloudinary
 * @param publicId - The public ID of the image
 * @returns The delete result
 */
export async function deleteFromCloudinary(
  publicId: string
): Promise<{ result: string } | { error: string }> {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    return { result: result.result };
  } catch (error) {
    console.error('Error deleting from Cloudinary:', error);
    return { error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

export default cloudinary; 