import { NextResponse } from 'next/server';
import { verifyAdminToken } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

/**
 * API endpoint to update product information
 * Accepts: productId, field, value
 * Fields that can be updated: projectName, productType, version, price
 */
export async function POST(request) {
  try {
    // Verify the admin user using the correct function
    const adminUser = await verifyAdminToken();
    if (!adminUser) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse the request body
    const data = await request.json();
    console.log('Received data:', data); // Debug log

    const { productId, field, value } = data;

    console.log('Processing update:', { productId, field, value }); // Debug log

    // Validate the required fields
    if (!productId) {
      console.error('Missing productId');
      return NextResponse.json({ message: 'Missing productId' }, { status: 400 });
    }

    if (!field) {
      console.error('Missing field');
      return NextResponse.json({ message: 'Missing field' }, { status: 400 });
    }

    // Validate the field is allowed to be updated
    const allowedFields = ['projectName', 'productType', 'version', 'price', 'projectDescription', 'discount'];
    if (!allowedFields.includes(field)) {
      console.error('Invalid field:', field);
      return NextResponse.json({ message: 'Invalid field to update' }, { status: 400 });
    }

    // Special validation for empty values
    if ((field === 'projectName' || field === 'version') && (value === undefined || value === null || value === '')) {
      console.error(`Empty value for ${field}`);
      return NextResponse.json({ 
        message: field === 'projectName' ? 'Product name cannot be empty' : 'Version cannot be empty' 
      }, { status: 400 });
    }

    // Connect to the database
    const { db } = await connectToDatabase();

    // Fetch the product to verify ownership
    let product;
    try {
      product = await db.collection('products').findOne({
        _id: new ObjectId(productId)
      });
    } catch (error) {
      console.error('Error finding product:', error);
      return NextResponse.json({ 
        message: 'Invalid product ID format',
        error: error.message 
      }, { status: 400 });
    }

    if (!product) {
      console.error('Product not found:', productId);
      return NextResponse.json({ message: 'Product not found' }, { status: 404 });
    }

    // Verify the admin user owns this product or is an admin
    if (product.createdBy !== adminUser.username && adminUser.admintype !== 'Admin') {
      console.error('Unauthorized user:', adminUser.username);
      return NextResponse.json({ message: 'Unauthorized to edit this product' }, { status: 403 });
    }

    // Type checking and validation for specific fields
    let processedValue = value;
    
    if (field === 'price') {
      // Ensure price is a number
      processedValue = Number(value);
      if (isNaN(processedValue) || processedValue < 0) {
        console.error('Invalid price value:', value);
        return NextResponse.json({ message: 'Price must be a positive number' }, { status: 400 });
      }
    }
    
    if (field === 'discount') {
      // Ensure discount is a number between 0 and 100
      processedValue = Number(value);
      if (isNaN(processedValue) || processedValue < 0 || processedValue > 100) {
        console.error('Invalid discount value:', value);
        return NextResponse.json({ message: 'Discount must be a number between 0 and 100' }, { status: 400 });
      }
    }
    
    if (field === 'productType' && !['plugin', 'skript', 'map', 'resourcepack', 'other'].includes(value)) {
      console.error('Invalid product type:', value);
      return NextResponse.json({ message: 'Invalid product type' }, { status: 400 });
    }

    // Create update object
    const updateObj = {};
    
    // Special handling for description/projectDescription
    if (field === 'projectDescription') {
      console.log('Handling description update - checking if description or projectDescription field exists');
      
      // Check which field exists in the database
      if (product.hasOwnProperty('description')) {
        console.log('Product has description field, updating it');
        updateObj['description'] = processedValue;
      } else {
        console.log('Product has projectDescription field, updating it');
        updateObj['projectDescription'] = processedValue;
      }
    } else {
      // For all other fields, use the field as is
      updateObj[field] = processedValue;
    }

    // Always update the updatedAt timestamp
    updateObj['updatedAt'] = new Date();

    console.log('Updating product with:', updateObj); // Debug log

    // Update the product
    const result = await db.collection('products').updateOne(
      { _id: new ObjectId(productId) },
      { $set: updateObj }
    );

    console.log('Update result:', result); // Debug log

    if (result.matchedCount === 0) {
      console.error('No product matched for update');
      return NextResponse.json({ message: 'Product not found' }, { status: 404 });
    }

    if (result.modifiedCount === 0) {
      console.warn('Product not modified - value might be the same');
      // Still return success if the document matched but wasn't modified (could be the same value)
      return NextResponse.json({ 
        message: 'No changes made - value might be the same as before',
        field,
        updatedValue: processedValue
      });
    }

    return NextResponse.json({ 
      message: 'Product updated successfully',
      field,
      updatedValue: processedValue
    });
  } catch (error) {
    console.error('Error updating product information:', error);
    return NextResponse.json({ 
      message: 'An error occurred while updating product information',
      error: error.message 
    }, { status: 500 });
  }
} 