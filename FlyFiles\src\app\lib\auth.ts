import GoogleProvider from "next-auth/providers/google";
import { getUsersCollection } from "./mongodb";
import { User } from "./types";

export const authOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  pages: {
    signIn: '/login',
  },
  callbacks: {
    async signIn({ user, account }: any) {
      if (account?.provider === 'google') {
        try {
          const users = await getUsersCollection();
          
          // Check if user exists
          const existingUser = await users.findOne({ email: user.email });
          
          if (!existingUser) {
            // Create new user with free plan
            const newUser: Omit<User, '_id'> = {
              email: user.email!,
              name: user.name!,
              image: user.image,
              plan: 'free',
              createdAt: new Date(),
              updatedAt: new Date(),
              usage: {
                monthly: 0,
                weekly: 0,
              },
            };
            
            await users.insertOne(newUser);
          } else {
            // Update existing user's last login
            await users.updateOne(
              { email: user.email },
              { $set: { updatedAt: new Date() } }
            );
          }
          
          return true;
        } catch (error) {
          console.error('Error in signIn callback:', error);
          return false;
        }
      }
      return true;
    },
    
    async session({ session }: any) {
      if (session.user?.email) {
        try {
          const users = await getUsersCollection();
          const dbUser = await users.findOne({ email: session.user.email });
          
          if (dbUser) {
            session.user = {
              ...session.user,
              id: dbUser._id.toString(),
              plan: dbUser.plan,
              usage: dbUser.usage,
            };
          }
        } catch (error) {
          console.error('Error in session callback:', error);
        }
      }
      return session;
    },
    
    async jwt({ token, account }: any) {
      if (account) {
        token.accessToken = account.access_token;
      }
      return token;
    },
  },
  session: {
    strategy: 'jwt' as const,
  },
};

// Extend the built-in session types
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      plan?: string;
      usage?: {
        monthly: number;
        weekly: number;
        session?: number;
      };
    };
  }
} 