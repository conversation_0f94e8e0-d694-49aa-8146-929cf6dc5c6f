"use client"

import { useSession, signIn, signOut } from "next-auth/react"
import Link from "next/link"
import { Button } from "./ui/button"
import { ThemeToggle } from "./ui/theme-toggle"
import { User, LogOut, Settings, Upload } from "lucide-react"

export function Navigation() {
  const { data: session, status } = useSession()

  return (
    <nav className="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">F</span>
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                FlyFiles
              </span>
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <Link 
              href="/pricing" 
              className="text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white px-3 py-2 text-sm font-medium"
            >
              Priser
            </Link>

            {status === "loading" ? (
              <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />
            ) : session ? (
              <div className="flex items-center space-x-3">
                <Link href="/dashboard">
                  <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                    <Upload className="h-4 w-4" />
                    <span>Dashboard</span>
                  </Button>
                </Link>
                
                <div className="flex items-center space-x-2">
                  <img
                    src={session.user?.image || '/default-avatar.png'}
                    alt={session.user?.name || 'User'}
                    className="w-8 h-8 rounded-full"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {session.user?.name}
                  </span>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => signOut()}
                  className="flex items-center space-x-2"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Log ud</span>
                </Button>
              </div>
            ) : (
              <Button
                onClick={() => signIn('google')}
                className="flex items-center space-x-2"
              >
                <User className="h-4 w-4" />
                <span>Log ind</span>
              </Button>
            )}

            <ThemeToggle />
          </div>
        </div>
      </div>
    </nav>
  )
} 