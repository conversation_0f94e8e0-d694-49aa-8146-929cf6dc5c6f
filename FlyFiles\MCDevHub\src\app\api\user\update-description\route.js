import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';

export const runtime = 'nodejs'; // Use Node.js runtime

export async function POST(request) {
  try {
    // Get the user's Discord ID from their NextAuth session
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Du er ikke logget ind' },
        { status: 401 }
      );
    }

    // Get Discord ID from session
    const discordUserId = session.user.id;
    
    if (!discordUserId) {
      return NextResponse.json(
        { success: false, message: 'Discord ID ikke fundet' },
        { status: 400 }
      );
    }
    
    // Parse request body
    const { description } = await request.json();
    
    if (description === undefined) {
      return NextResponse.json(
        { success: false, message: 'Manglende beskrivelse' },
        { status: 400 }
      );
    }
    
    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Update the admin user's description
    const result = await db.collection('adminusers').updateOne(
      { discordUserId },
      { $set: { description } }
    );
    
    if (result.matchedCount === 0) {
      return NextResponse.json(
        { success: false, message: 'Ingen admin bruger fundet med dette Discord ID' },
        { status: 404 }
      );
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Beskrivelse opdateret',
      description
    });
    
  } catch (error) {
    console.error('Error updating description by Discord ID:', error);
    
    // Return error response
    return NextResponse.json(
      { success: false, message: 'Der opstod en fejl ved opdatering af beskrivelsen' },
      { status: 500 }
    );
  }
} 