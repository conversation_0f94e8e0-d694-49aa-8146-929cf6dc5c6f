import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import { BadgeAwardService } from '@/lib/services/badgeAwardService';

/**
 * GET /api/badges/stats - Get badge statistics (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user?.isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Connect to database
    const { db } = await connectToDatabase();
    const badgeService = new BadgeAwardService(db);

    // Get badge statistics
    const stats = await badgeService.getBadgeStatistics();

    return NextResponse.json({
      success: true,
      stats: stats
    });

  } catch (error) {
    console.error('Error fetching badge statistics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/badges/stats - Trigger bulk badge check for all users (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user?.isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Connect to database
    const { db } = await connectToDatabase();
    const badgeService = new BadgeAwardService(db);

    // Start bulk check in background (don't wait for completion)
    badgeService.bulkCheckAllUsers().catch(error => {
      console.error('Error in bulk badge check:', error);
    });

    return NextResponse.json({
      success: true,
      message: 'Bulk badge check started in background'
    });

  } catch (error) {
    console.error('Error starting bulk badge check:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
