import { MongoClient, ObjectId } from 'mongodb';
import { v4 as uuidv4 } from 'uuid';

// MongoDB connection setup
if (!process.env.MONGODB_URI) {
  throw new Error('Please add your Mongo URI to .env.local');
}

const uri = process.env.MONGODB_URI;
const options = {};

export interface Notification {
  _id: string;
  title: string;
  message: string;
  date: string;
  read?: boolean;
  readAt?: string;
  type?: string;
  link?: string;
}

/**
 * Add a notification to a user
 * @param discordUserId The Discord ID of the user
 * @param notification The notification to add
 * @returns Promise resolving to success state
 */
export async function addNotification(
  discordUserId: string,
  notification: Omit<Notification, '_id' | 'date' | 'read'>
): Promise<{ success: boolean; error?: string }> {
  if (!discordUserId) {
    return { success: false, error: 'Discord user ID is required' };
  }

  // Connect to MongoDB
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db("codehub");
    
    // Create the notification object
    const newNotification: Notification = {
      _id: new ObjectId().toString(), // Use MongoDB ObjectId as string
      title: notification.title,
      message: notification.message,
      date: new Date().toISOString(),
      read: false,
      ...(notification.type && { type: notification.type }),
      ...(notification.link && { link: notification.link }),
    };
    
    // Add the notification to the user's notifications array
    // Using a raw document update to bypass the TypeScript error
    const updateDoc = {
      $push: { notifications: newNotification }
    };
    
    const result = await db.collection('users').updateOne(
      { discordUserId },
      updateDoc as any,
      { upsert: true } // Create the user if they don't exist
    );
    
    if (result.matchedCount === 0 && result.upsertedCount === 0) {
      return { success: false, error: 'Failed to add notification' };
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error adding notification:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  } finally {
    await client.close();
  }
}

/**
 * Get notifications for a user
 * @param discordUserId The Discord ID of the user
 * @param options Optional parameters like limit
 * @returns Promise resolving to notifications array
 */
export async function getNotifications(
  discordUserId: string,
  options: { limit?: number } = {}
): Promise<{ success: boolean; notifications?: Notification[]; error?: string }> {
  if (!discordUserId) {
    return { success: false, error: 'Discord user ID is required' };
  }

  // Set a default limit if not provided
  const limit = options.limit || 100;

  // Connect to MongoDB with timeout options
  const client = new MongoClient(uri, {
    serverSelectionTimeoutMS: 5000, // 5 seconds
    connectTimeoutMS: 5000, // 5 seconds
    socketTimeoutMS: 5000 // 5 seconds
  });
  
  try {
    await client.connect();
    console.log(`MongoDB connected for user ${discordUserId.slice(0, 6)}...`);
    
    const db = client.db("codehub");
    
    // Get the user document with a more efficient query that only retrieves the notifications field
    // and limits the number of notifications returned
    const user = await db.collection('users').findOne(
      { discordUserId },
      { 
        projection: { 
          notifications: { $slice: limit } // Only return the specified number of notifications
        }
      }
    );
    
    if (!user) {
      // Create a user document if it doesn't exist
      await db.collection('users').insertOne({
        discordUserId,
        notifications: []
      });
      console.log(`Created new user document for ${discordUserId.slice(0, 6)}`);
      return { success: true, notifications: [] };
    }
    
    // Extract and sort notifications
    const notifications = user.notifications || [];
    
    // Sort the notifications on the server to reduce client-side processing
    const sortedNotifications = [...notifications].sort((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });
    
    console.log(`Retrieved ${sortedNotifications.length} notifications for user ${discordUserId.slice(0, 6)}`);
    
    return { success: true, notifications: sortedNotifications };
  } catch (error) {
    console.error('Error getting notifications:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  } finally {
    try {
      await client.close();
      console.log('MongoDB connection closed');
    } catch (e) {
      console.error('Error closing MongoDB connection:', e);
    }
  }
}

/**
 * Mark a notification as read
 * @param discordUserId The Discord ID of the user
 * @param notificationId The ID of the notification
 * @returns Promise resolving to success state
 */
export async function markNotificationAsRead(
  discordUserId: string,
  notificationId: string
): Promise<{ success: boolean; modified?: boolean; error?: string }> {
  if (!discordUserId) {
    return { success: false, error: 'Discord user ID is required' };
  }

  if (!notificationId) {
    return { success: false, error: 'Notification ID is required' };
  }

  // Connect to MongoDB with timeout options
  const client = new MongoClient(uri, {
    serverSelectionTimeoutMS: 5000, // 5 seconds
    connectTimeoutMS: 5000, // 5 seconds
    socketTimeoutMS: 5000 // 5 seconds
  });
  
  try {
    await client.connect();
    console.log(`MongoDB connected for marking notification read, user ${discordUserId.slice(0, 6)}...`);
    
    const db = client.db("codehub");
    
    // Based on the MongoDB data you showed, notifications are stored with string IDs
    // so we'll query directly with the string ID
    console.log(`Attempting to mark notification ${notificationId} as read`);
    
    // First, check if the notification exists
    const user = await db.collection('users').findOne(
      { 
        discordUserId,
        "notifications._id": notificationId
      },
      {
        projection: { 
          "notifications.$": 1 
        }
      }
    );
    
    if (!user || !user.notifications || user.notifications.length === 0) {
      console.log(`No notification found with ID: ${notificationId} for user: ${discordUserId.slice(0, 6)}`);
      return { success: false, error: 'Notification not found' };
    }
    
    // If the notification is already read, return success but indicate nothing was modified
    if (user.notifications[0].read) {
      console.log(`Notification ${notificationId} is already marked as read`);
      return { success: true, modified: false };
    }
    
    // Update the notification to mark it as read
    const result = await db.collection('users').updateOne(
      { 
        discordUserId,
        "notifications._id": notificationId
      },
      {
        $set: { 
          "notifications.$.read": true, 
          "notifications.$.readAt": new Date().toISOString() 
        }
      }
    );
    
    if (result.matchedCount === 0) {
      console.log(`No notification found for ID: ${notificationId} and user: ${discordUserId.slice(0, 6)}`);
      return { success: false, error: 'Notification not found' };
    }
    
    if (result.modifiedCount === 0) {
      return { success: true, modified: false };
    }
    
    console.log(`Successfully marked notification ${notificationId} as read`);
    return { success: true, modified: true };
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  } finally {
    try {
      await client.close();
      console.log('MongoDB connection closed');
    } catch (e) {
      console.error('Error closing MongoDB connection:', e);
    }
  }
}