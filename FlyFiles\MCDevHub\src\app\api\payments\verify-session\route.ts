import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { auth } from '@/lib/auth';
import { triggerBadgeCheck } from '@/lib/utils/badgeUtils';

// Initialize Stripe
const stripeKey = process.env.STRIPE_SECRET_KEY || '';
const stripe = new Stripe(stripeKey, {});

export async function POST(req: NextRequest) {
  try {
    // Get session ID from request body
    const { sessionId } = await req.json();

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Connect to database to check if session was already processed
    const { db } = await connectToDatabase();
    
    // Check if this session has already been processed
    const existingTransaction = await db.collection('transactions').findOne({ 
      stripeSessionId: sessionId 
    });
    
    if (existingTransaction) {
      return NextResponse.json({ 
        success: true,
        alreadyProcessed: true,
        productId: existingTransaction.productId,
        productName: existingTransaction.productName,
        message: 'This payment has already been processed'
      });
    }

    // Retrieve session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    
    // Make sure payment was successful
    if (session.payment_status !== 'paid') {
      return NextResponse.json({ 
        error: 'Payment not completed',
        paymentStatus: session.payment_status
      }, { status: 400 });
    }
    
    // Get product ID from metadata
    const productId = session.metadata?.productId;
    const customerId = session.metadata?.customerId || 'guest';
    
    if (!productId) {
      return NextResponse.json({ 
        error: 'Product ID not found in session metadata',
        session: {
          id: session.id,
          metadata: session.metadata || {},
        }
      }, { status: 404 });
    }
    
    // Try to find the product with different ID formats
    let product = null;
    
    // First try with string ID
    product = await db.collection('products').findOne({ _id: productId });
    
    // If not found and it's a valid ObjectId format, try with ObjectId
    if (!product && ObjectId.isValid(productId)) {
      product = await db.collection('products').findOne({ _id: new ObjectId(productId) });
    }
    
    if (!product) {
      return NextResponse.json({ 
        error: 'Product not found',
        productId 
      }, { status: 404 });
    }
    
    // Get the line items to find the amount paid
    const lineItems = await stripe.checkout.sessions.listLineItems(sessionId);
    const amount = lineItems.data[0]?.amount_total ? lineItems.data[0].amount_total / 100 : null;

    // Get payment method details from the session
    let paymentMethod = 'Stripe';
    
    try {
      // Get more detailed payment information to detect Apple Pay or Google Pay
      const paymentIntentId = session.payment_intent as string;
      if (paymentIntentId) {
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId, {
          expand: ['payment_method']
        });
        
        // Check if payment_method exists and has a type
        if (paymentIntent.payment_method && typeof paymentIntent.payment_method !== 'string') {
          // Get the payment method type
          const paymentMethodType = paymentIntent.payment_method.type;
          
          // Check for wallet-specific data
          if (paymentMethodType === 'card') {
            const cardDetails = paymentIntent.payment_method.card;
            
            if (cardDetails) {
              // Check for Apple Pay
              if (cardDetails.wallet?.type === 'apple_pay') {
                paymentMethod = 'Apple Pay';
              } 
              // Check for Google Pay
              else if (cardDetails.wallet?.type === 'google_pay') {
                paymentMethod = 'Google Pay';
              }
              // Regular card payment
              else {
                paymentMethod = 'Kort';
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error retrieving payment method details:', error);
      // Fall back to basic detection if detailed info fails
      if (session.payment_method_types) {
        if (session.payment_method_types.includes('mobilepay')) {
          paymentMethod = 'MobilePay';
        } else if (session.payment_method_types.includes('card')) {
          paymentMethod = 'Kort';
        }
      }
    }

    // Get the current user from NextAuth
    const userSession = await auth();

    let discordId = null;

    // If user is logged in, use their ID
    if (userSession?.user) {
      discordId = userSession.user.id;
    }
    // Otherwise use the customerId from the Stripe session if it's not 'guest'
    else if (customerId !== 'guest') {
      discordId = customerId;
    }

    // Record the purchase if we have a user ID
    if (discordId) {
      try {
        // Check if user already purchased this product
        const existingPurchase = await db.collection('user_purchases').findOne({
          userId: discordId,
          productId: product._id.toString()
        });
        
        if (!existingPurchase) {
          // Get the thumbnail URL from the product's screenshots if available
          let thumbnailUrl = null;
          if (product.screenshotUrls && product.screenshotUrls.length > 0) {
            // Find the first screenshot that has a URL
            const screenshot = product.screenshotUrls.find(ss => ss.url) || product.screenshotUrls[0];
            thumbnailUrl = screenshot.url || null;
          }
          
          // Create purchase record with payment method and thumbnail
          await db.collection('user_purchases').insertOne({
            userId: discordId,
            productId: product._id.toString(),
            productName: product.projectName,
            productType: product.productType || null,
            amount: amount,
            purchaseDate: new Date(),
            transactionId: session.id,
            paymentMethod: paymentMethod,
            seller: product.createdBy || 'MCDevHub',
            thumbnailUrl: thumbnailUrl
          });
          
          console.log(`Saved purchase record for user ${discordId}, product ${product.projectName}, payment method ${paymentMethod}`);
          
          // Get the product creator's ID
          const sellerId = product.discordUserId;
          
          // If we have a valid seller ID, create a transaction and update their balance
          if (sellerId) {
            // Get buyer username from Supabase user data, or set to "Unknown" if not available
            let buyerName = "Unknown User";
            
            if (userSession?.user) {
              buyerName = userSession.user.name || "Unknown User";
            }
            
            // Record transaction
            await db.collection('transactions').insertOne({
              sellerId: sellerId,
              buyerId: discordId,
              buyerName: buyerName,
              productId: product._id.toString(),
              productName: product.projectName,
              amount: amount,
              createdAt: new Date(),
              stripeSessionId: session.id
            });
            
            // Update seller's balance
            // Adjust commission as needed (currently 85% to seller, 15% platform fee)
            const sellerShare = amount ? amount * 0.85 : 0;
            
            // Find and update balance, or create if not exists
            await db.collection('user_balances').updateOne(
              { userId: sellerId },
              { 
                $inc: { balance: sellerShare },
                $set: { lastUpdated: new Date() }
              },
              { upsert: true }
            );
            
            console.log(`Updated balance for seller ${sellerId}, added ${sellerShare} DKK`);
          }

          // Trigger badge check for buyer after successful purchase
          triggerBadgeCheck(discordId, 'purchase').catch(error => {
            console.error('Error checking badges for buyer:', error);
          });

          // Trigger badge check for seller after successful sale
          if (sellerId) {
            triggerBadgeCheck(sellerId, 'sale').catch(error => {
              console.error('Error checking badges for seller:', error);
            });
          }
        } else {
          console.log(`User ${discordId} already purchased product ${product.projectName}, skipping duplicate purchase`);
        }
      } catch (dbError) {
        console.error('Error saving purchase record:', dbError);
      }
    }
    
    // Return success with product details
    return NextResponse.json({
      success: true,
      productId: product._id,
      productName: product.projectName,
      amount,
    });
  } catch (error) {
    console.error('Error verifying payment session:', error);
    return NextResponse.json({
      error: 'Failed to verify payment session',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
} 