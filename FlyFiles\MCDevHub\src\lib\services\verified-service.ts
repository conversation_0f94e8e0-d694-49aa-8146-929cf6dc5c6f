import { Db } from 'mongodb';
import clientPromise from '../mongodb-client';
import { IVerified, VERIFIED_COLLECTION } from '../models/verified';

/**
 * Get MongoDB database instance
 * @returns MongoDB database instance
 */
async function getDatabase(): Promise<Db> {
  const client = await clientPromise;
  return client.db();
}

/**
 * Check if a user is verified by their Discord ID
 * @param discordId Discord user ID
 * @returns whether the user is verified
 */
export async function isUserVerified(discordId: string): Promise<boolean> {
  try {
    const db = await getDatabase();
    const collection = db.collection<IVerified>(VERIFIED_COLLECTION);
    
    const user = await collection.findOne({ discordId });
    return user?.isVerified === true;
  } catch (error) {
    console.error('Error checking if user is verified:', error);
    return false;
  }
}

/**
 * Get user verification status
 * @param discordId Discord user ID
 * @returns The verification record or null if not found
 */
export async function getUserVerificationStatus(discordId: string): Promise<IVerified | null> {
  try {
    const db = await getDatabase();
    const collection = db.collection<IVerified>(VERIFIED_COLLECTION);
    
    return await collection.findOne({ discordId });
  } catch (error) {
    console.error('Error getting user verification status:', error);
    return null;
  }
}

/**
 * Set user verification status
 * @param discordId Discord user ID
 * @param discordName Discord username
 * @param isVerified Whether the user is verified
 * @returns success status
 */
export async function setUserVerification(
  discordId: string, 
  discordName: string, 
  isVerified: boolean
): Promise<boolean> {
  try {
    const db = await getDatabase();
    const collection = db.collection<IVerified>(VERIFIED_COLLECTION);
    
    const now = new Date();
    
    const result = await collection.updateOne(
      { discordId },
      { 
        $set: { 
          discordName,
          isVerified,
          verifiedAt: isVerified ? now : undefined,
          updatedAt: now
        },
        $setOnInsert: { 
          createdAt: now 
        }
      },
      { upsert: true }
    );
    
    return result.acknowledged;
  } catch (error) {
    console.error('Error setting user verification status:', error);
    return false;
  }
} 