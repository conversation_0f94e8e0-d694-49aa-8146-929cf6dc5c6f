import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY environment variable is not set');
}

// Create and export the Stripe instance
const stripeInstance = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-04-30.basil' as any, // Use 'any' to bypass TS errors for now
  appInfo: {
    name: 'MCDevHub',
    version: '1.0.0',
  },
});

export { stripeInstance as stripe }; 