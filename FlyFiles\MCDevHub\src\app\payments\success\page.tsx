'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { FaCheckCircle, FaS<PERSON>ner, FaExclamationTriangle } from 'react-icons/fa';

function PaymentSuccessContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const reference = searchParams.get('reference');
  const session_id = searchParams.get('session_id');
  
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState<string>('Verificerer din betaling...');

  useEffect(() => {
    // Only run once on component mount
    async function verifyAndProcessPayment() {
      try {
        // Verify the payment and update the balance
        const response = await fetch('/api/payments/process-payment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            reference,
            session_id,
          }),
        });

        const data = await response.json();

        if (response.ok) {
          setStatus('success');
          setMessage(data.message || 'Din betaling er modtaget og din balance er opdateret!');
          
          // Redirect to profile after 3 seconds
          setTimeout(() => {
            router.push('/profile?tab=transactions&deposit_success=true');
          }, 3000);
        } else {
          setStatus('error');
          setMessage(data.error || 'Der opstod en fejl ved behandling af din betaling.');
        }
      } catch (error) {
        setStatus('error');
        setMessage('Der opstod en uventet fejl. Kontakt venligst support hvis dit beløb er trukket.');
      }
    }

    verifyAndProcessPayment();
  }, [reference, session_id, router]);

  return (
    <div className="min-h-screen bg-gray-50 pt-16">
      <div className="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden p-6">
        <div className="flex justify-center mb-6">
          <div className={`w-16 h-16 rounded-full flex items-center justify-center
            ${status === 'loading' ? 'bg-blue-100' : 
              status === 'success' ? 'bg-green-100' : 'bg-red-100'}`}>
            {status === 'loading' ? (
              <FaSpinner className="text-blue-500 text-2xl animate-spin" />
            ) : status === 'success' ? (
              <FaCheckCircle className="text-green-500 text-2xl" />
            ) : (
              <FaExclamationTriangle className="text-red-500 text-2xl" />
            )}
          </div>
        </div>
        
        <h1 className="text-xl font-bold text-center text-gray-900 mb-2">
          {status === 'loading' ? 'Behandler betaling' : 
           status === 'success' ? 'Betaling gennemført' : 'Betaling fejlede'}
        </h1>
        
        <div className="text-center mb-6">
          <p className={`mb-4 ${
            status === 'loading' ? 'text-gray-600' : 
            status === 'success' ? 'text-green-600' : 'text-red-600'
          }`}>
            {message}
          </p>
          
          {status === 'success' && (
            <p className="text-gray-500 text-sm mt-4">
              Du vil blive omdirigeret til din profil om et øjeblik...
            </p>
          )}
          
          {status === 'error' && (
            <div className="mt-4">
              <button
                onClick={() => router.push('/profile?tab=transactions')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Gå til profil
              </button>
            </div>
          )}
        </div>
        
        {reference && (
          <div className="p-3 bg-gray-50 rounded-lg text-xs text-gray-500 mt-4">
            <p className="font-medium">Reference</p>
            <p className="font-mono text-xs mt-1 break-all">{reference}</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentSuccessContent />
    </Suspense>
  );
}