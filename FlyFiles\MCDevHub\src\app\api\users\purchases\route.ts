import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';
import { ObjectId } from 'mongodb';

export async function GET() {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }
    
    // Connect to database
    const { db } = await connectToDatabase();
    
    // Get user purchases
    const purchases = await db.collection('user_purchases')
      .find({ userId: discordId })
      .sort({ purchaseDate: -1 })
      .toArray();
      
    // Enhance purchases with product thumbnail information
    const enhancedPurchases = await Promise.all(purchases.map(async (purchase: any) => {
      try {
        // Try to find the product with different ID formats
        let product = null;
        const productId = purchase.productId;
        
        // First try with string ID
        product = await db.collection('products').findOne({ _id: productId });
        
        // If not found and it's a valid ObjectId format, try with ObjectId
        if (!product && ObjectId.isValid(productId)) {
          product = await db.collection('products').findOne({ _id: new ObjectId(productId) });
        }
        
        if (product && product.screenshotUrls && product.screenshotUrls.length > 0) {
          // Find the first screenshot that has a URL
          const screenshot = product.screenshotUrls.find((ss: any) => ss.url) || product.screenshotUrls[0];
          
          // Add thumbnail URL to the purchase object
          return {
            ...purchase,
            thumbnailUrl: screenshot.url || null
          };
        }
        
        return purchase;
      } catch (error) {
        console.error(`Error fetching product details for purchase ${purchase._id}:`, error);
        return purchase;
      }
    }));
    
    return NextResponse.json({
      purchases: enhancedPurchases
    });
    
  } catch (error) {
    console.error('Error fetching user purchases:', error);
    return NextResponse.json({
      error: 'Failed to fetch user purchases',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
} 