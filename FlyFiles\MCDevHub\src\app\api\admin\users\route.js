import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { verifyAdminToken } from '@/lib/auth';

export async function GET(request) {
  try {
    // Verify the admin user
    const admin = await verifyAdminToken();
    
    if (!admin) {
      return NextResponse.json(
        { message: 'Ikke autoriseret' },
        { status: 401 }
      );
    }
    
    // Only MyckasP is allowed to view all users
    if (admin.username !== 'MyckasP') {
      return NextResponse.json(
        { message: 'Ikke autoriseret - kun hovedadmin kan se alle brugere' },
        { status: 403 }
      );
    }
    
    // Connect to the database
    const { db } = await connectToDatabase();
    
    // Fetch all admin users
    const users = await db.collection('adminusers')
      .find({}, { projection: { password: 0 } }) // Exclude passwords
      .sort({ createdAt: -1 })
      .toArray();
    
    return NextResponse.json({
      users: users
    });
  } catch (error) {
    console.error('Error fetching admin users:', error);
    return NextResponse.json(
      { message: 'Der opstod en fejl ved hentning af brugere' },
      { status: 500 }
    );
  }
} 