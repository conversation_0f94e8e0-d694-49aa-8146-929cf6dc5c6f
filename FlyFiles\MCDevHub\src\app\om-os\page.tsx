import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FaUsers, FaLightbulb, FaShieldAlt, FaChevronDown, FaArrowRight } from 'react-icons/fa';

export default function AboutPage() {
  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-700 via-blue-800 to-indigo-900 text-white py-28 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/about-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="absolute -bottom-10 -left-10 right-0 w-[120%] h-40 bg-white/5 blur-3xl transform rotate-2 rounded-full opacity-60"></div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="inline-flex items-center justify-center mb-6 bg-white/10 p-4 rounded-full backdrop-blur-sm border border-white/20 shadow-lg">
            <FaUsers className="text-4xl text-white" />
          </div>
          <h1 className="text-4xl md:text-6xl font-extrabold mb-6 tracking-tight">Om MCDevHub</h1>
          <p className="text-xl text-blue-100 md:max-w-2xl mx-auto font-light">
            Danmarks førende markedsplads for Minecraft plugins, skripts og builds
          </p>
          <div className="flex justify-center mt-12">
            <div className="animate-bounce bg-white/10 rounded-full p-2 w-10 h-10 flex items-center justify-center backdrop-blur-sm border border-white/20">
              <FaChevronDown className="text-blue-100" />
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-white to-transparent"></div>
      </section>
      
      {/* Our Story */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-xl p-8 md:p-12 border border-gray-100 relative overflow-hidden">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
            <div className="absolute -right-20 -top-20 w-40 h-40 bg-blue-50 rounded-full opacity-70"></div>
            <div className="absolute -left-16 -bottom-16 w-48 h-48 bg-indigo-50 rounded-full opacity-70"></div>
            
            <div className="relative">
              <div className="inline-flex items-center justify-center mb-6 bg-blue-100 p-3 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900">Vores Historie</h2>
              
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-50 to-white p-6 rounded-xl border border-blue-100 shadow-sm">
                  <p className="text-gray-700 text-lg leading-relaxed">
                    MCDevHub blev grundlagt med en enkel mission: at give danske Minecraft server ejere adgang til højkvalitets plugins, skripts og builds.
                  </p>
                </div>
                
                <p className="text-gray-600 text-lg leading-relaxed">
                  Som tidligere server ejere oplevede vi selv behovet for pålidelige og veldesignede plugins, der kunne forbedre oplevelsen for vores spillere. Derfor besluttede vi at skabe en platform, hvor server ejere nemt kan finde de billigste og bedste løsninger til deres behov.
                </p>
                
                <div className="bg-gradient-to-r from-indigo-50 to-white p-6 rounded-xl border border-indigo-100 shadow-sm">
                  <p className="text-gray-700 text-lg leading-relaxed">
                    I dag fokuserer vi på at levere løsninger, der gør din Minecraft server unik og tager den til næste niveau.
                  </p>
                </div>
                
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <div className="flex items-center">
                    <div className="mr-4 bg-blue-100 rounded-full p-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <p className="text-blue-800 font-medium">Vores mission er at skabe værdi for ethvert dansk Minecraft-fællesskab</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Our Values */}
      <section className="py-24 bg-gradient-to-b from-white to-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center mb-4 bg-blue-100 p-3 rounded-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <h2 className="text-3xl md:text-4xl font-extrabold mb-4 text-gray-900">Vores Værdier</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Disse kerneværdier guider alt, hvad vi gør for at levere den bedste oplevelse for vores brugere.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
            {/* Quality Card */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="h-2 bg-gradient-to-r from-blue-400 to-blue-600"></div>
              <div className="p-8">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 w-16 h-16 rounded-xl flex items-center justify-center mb-6 group-hover:bg-blue-200 transition-all duration-300 shadow-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-gray-900 group-hover:text-blue-600 transition-colors duration-300">Kvalitet</h3>
                <p className="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                  Vi går aldrig på kompromis med kvaliteten. Alle vores plugins og skripts er grundigt testede for at sikre optimal ydelse og stabilitet.
                </p>
                <div className="mt-6 pt-6 border-t border-gray-100">
                  <div className="inline-flex items-center text-sm font-medium text-blue-600">
                    <span className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">Vi tester alt grundigt!</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Community Card */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="h-2 bg-gradient-to-r from-indigo-400 to-indigo-600"></div>
              <div className="p-8">
                <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 w-16 h-16 rounded-xl flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-all duration-300 shadow-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-gray-900 group-hover:text-indigo-600 transition-colors duration-300">Fællesskab</h3>
                <p className="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                  Vi tror på at bygge et stærkt fællesskab af Minecraft entusiaster i Danmark. Sammen kan vi gøre det danske Minecraft community endnu bedre.
                </p>
                <div className="mt-6 pt-6 border-t border-gray-100">
                  <div className="inline-flex items-center text-sm font-medium text-indigo-600">
                    <span className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">Fællesskab er vores styrke!</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Innovation Card */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="h-2 bg-gradient-to-r from-purple-400 to-purple-600"></div>
              <div className="p-8">
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 w-16 h-16 rounded-xl flex items-center justify-center mb-6 group-hover:bg-purple-200 transition-all duration-300 shadow-sm">
                  <FaLightbulb className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold mb-4 text-gray-900 group-hover:text-purple-600 transition-colors duration-300">Innovation</h3>
                <p className="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                  Vi stræber altid efter at være på forkant with udviklingen og tilbyde innovative løsninger, der kan gøre din Minecraft server unik.
                </p>
                <div className="mt-6 pt-6 border-t border-gray-100">
                  <div className="inline-flex items-center text-sm font-medium text-purple-600">
                    <span className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">Altid på forkant!</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/cta-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-white/5 rounded-full blur-3xl -mr-20 -mb-20 opacity-50"></div>
        <div className="absolute top-0 left-0 w-64 h-64 bg-white/5 rounded-full blur-3xl -ml-10 -mt-10 opacity-50"></div>
        
        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-3xl md:text-4xl font-extrabold mb-6 tracking-tight">Har du spørgsmål?</h2>
          <p className="text-xl text-blue-100 max-w-2xl mx-auto mb-10 font-light">
            Vi står altid klar til at hjælpe dig med dine spørgsmål eller særlige behov. Kontakt os for at høre mere om, hvordan vi kan hjælpe dig.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Link 
              href="/kontakt" 
              className="group inline-flex items-center gap-2 bg-white text-blue-600 px-6 py-3 rounded-full font-medium hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span>Kontakt Os</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 opacity-0 group-hover:opacity-100 transform group-hover:translate-x-1 transition-all duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
            <Link 
              href="/custom-order" 
              className="group inline-flex items-center gap-2 bg-blue-800/30 backdrop-blur-sm text-white px-6 py-3 rounded-full font-medium hover:bg-blue-700/40 transition-all duration-300 border border-white/10 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <span>Specialbestilling</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 opacity-0 group-hover:opacity-100 transform group-hover:translate-x-1 transition-all duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
} 