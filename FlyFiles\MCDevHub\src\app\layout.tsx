import './globals.css';
import type { Metadata } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Providers } from './providers';
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "MCDevHub | Dansk Minecraft Plugins, Skripts & Builds Markedsplads",
  description: "MCDevHub - Danmarks førende markedsplads for Minecraft plugins, skripts, resourcepacks og builds fra danske udviklere. Find og køb de bedste Minecraft løsninger til din server.",
  metadataBase: new URL('https://mcdevhub.dk'),
  keywords: ['MCDevHub', 'Minecraft', 'Dansk', 'Plugins', 'Skripts', 'Builds', 'Resourcepacks', 'Map', 'Server', 'Udvikling', 'Markedsplads'],
  authors: [{ name: 'MyckasP' }],
  creator: 'MyckasP',
  publisher: 'MyckasP',
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'MCDevHub | Dansk Minecraft Plugins, Skripts & Builds Markedsplads',
    description: 'MCDevHub - Danmarks førende markedsplads for Minecraft plugins, skripts, resourcepacks og builds fra danske udviklere.',
    url: 'https://mcdevhub.dk',
    siteName: 'MCDevHub',
    locale: 'da_DK',
    type: 'website',
    images: [
      {
        url: '/images/mcdevhub-og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'MCDevHub - Dansk Minecraft Markedsplads',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'MCDevHub | Dansk Minecraft Markedsplads',
    description: 'MCDevHub - Danmarks førende markedsplads for Minecraft plugins, skripts, resourcepacks og builds.',
    images: ['/images/mcdevhub-og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'xe1rownVzg03tWAgxSQjo4gqcHoTj7lKPEqvMplnov0', // Using the real verification code
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="da">
      <head>
        <link rel="sitemap" type="application/xml" href="/sitemap.xml" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="google-site-verification" content="xe1rownVzg03tWAgxSQjo4gqcHoTj7lKPEqvMplnov0" />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white`}>
        <Providers>
          <div className="flex flex-col min-h-screen">
            <Navbar />
            <main className="flex-grow">{children}</main>
            <Footer />
          </div>
        </Providers>
      </body>
    </html>
  );
}
