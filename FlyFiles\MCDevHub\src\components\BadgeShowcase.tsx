'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON>hy, FaSpinner } from 'react-icons/fa';

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  earnedAt: string;
}

interface BadgeShowcaseProps {
  discordUserId?: string;
  maxDisplay?: number;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const BadgeShowcase: React.FC<BadgeShowcaseProps> = ({
  discordUserId,
  maxDisplay = 6,
  size = 'medium',
  className = ''
}) => {
  const [badges, setBadges] = useState<Badge[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBadges();
  }, [discordUserId]);

  const fetchBadges = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/badges');
      
      if (!response.ok) {
        throw new Error('Failed to fetch badges');
      }

      const data = await response.json();
      setBadges(data.badges || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          container: 'w-8 h-8',
          icon: 'text-sm',
          tooltip: 'text-xs'
        };
      case 'large':
        return {
          container: 'w-16 h-16',
          icon: 'text-2xl',
          tooltip: 'text-sm'
        };
      default: // medium
        return {
          container: 'w-12 h-12',
          icon: 'text-lg',
          tooltip: 'text-xs'
        };
    }
  };

  const getRarityClasses = (rarity: string): string => {
    switch (rarity) {
      case 'legendary':
        return 'bg-gradient-to-br from-yellow-400 to-orange-500 border-yellow-500 shadow-yellow-200';
      case 'epic':
        return 'bg-gradient-to-br from-purple-400 to-pink-500 border-purple-500 shadow-purple-200';
      case 'rare':
        return 'bg-gradient-to-br from-blue-400 to-cyan-500 border-blue-500 shadow-blue-200';
      default: // common
        return 'bg-gradient-to-br from-gray-400 to-gray-500 border-gray-500 shadow-gray-200';
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-2 ${className}`}>
        <FaSpinner className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (error || badges.length === 0) {
    return null; // Don't show anything if there's an error or no badges
  }

  const sizeClasses = getSizeClasses();
  const displayBadges = badges.slice(0, maxDisplay);
  const remainingCount = badges.length - maxDisplay;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {displayBadges.map((badge, index) => (
        <div
          key={badge.id}
          className="relative group"
        >
          {/* Badge Icon */}
          <div
            className={`
              ${sizeClasses.container}
              ${getRarityClasses(badge.rarity)}
              rounded-full border-2 shadow-lg
              flex items-center justify-center
              transition-all duration-300 hover:scale-110 hover:shadow-xl
              cursor-pointer
            `}
          >
            <span className={`${sizeClasses.icon} text-white drop-shadow-sm`}>
              {badge.icon}
            </span>
            
            {/* Rarity indicator for legendary/epic badges */}
            {(badge.rarity === 'legendary' || badge.rarity === 'epic') && (
              <div className="absolute -top-1 -right-1">
                <FaTrophy className={`
                  text-xs text-white drop-shadow-sm
                  ${badge.rarity === 'legendary' ? 'text-yellow-200' : 'text-purple-200'}
                `} />
              </div>
            )}
          </div>

          {/* Tooltip */}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-10">
            <div className="bg-gray-900 text-white px-3 py-2 rounded-lg shadow-lg whitespace-nowrap">
              <div className={`font-semibold ${sizeClasses.tooltip}`}>
                {badge.name}
              </div>
              <div className={`text-gray-300 ${sizeClasses.tooltip}`}>
                {badge.description}
              </div>
              <div className={`text-gray-400 ${sizeClasses.tooltip} mt-1`}>
                Optjent: {new Date(badge.earnedAt).toLocaleDateString('da-DK')}
              </div>
              
              {/* Tooltip arrow */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
            </div>
          </div>
        </div>
      ))}

      {/* Show remaining count if there are more badges */}
      {remainingCount > 0 && (
        <div
          className={`
            ${sizeClasses.container}
            bg-gray-200 border-2 border-gray-300 rounded-full
            flex items-center justify-center
            text-gray-600 font-semibold ${sizeClasses.icon}
          `}
          title={`+${remainingCount} flere badges`}
        >
          +{remainingCount}
        </div>
      )}
    </div>
  );
};

export default BadgeShowcase;
