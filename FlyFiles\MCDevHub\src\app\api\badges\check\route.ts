import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import { BadgeAwardService } from '@/lib/services/badgeAwardService';

/**
 * POST /api/badges/check - Trigger badge check (can be called from other endpoints)
 * This endpoint can be called internally when users perform actions that might earn badges
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { discordUserId, action } = body;

    if (!discordUserId) {
      return NextResponse.json({ error: 'Discord user ID is required' }, { status: 400 });
    }

    // Connect to database
    const { db } = await connectToDatabase();
    const badgeService = new BadgeAwardService(db);

    // Trigger badge check
    await badgeService.triggerBadgeCheck(discordUserId, action);

    return NextResponse.json({
      success: true,
      message: 'Badge check completed'
    });

  } catch (error) {
    console.error('Error in badge check:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/badges/check - Check current user's badge eligibility without awarding
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the Discord ID from the user
    const discordId = session.user.id;
    
    if (!discordId) {
      return NextResponse.json({ error: 'No Discord ID found' }, { status: 400 });
    }

    // Connect to database
    const { db } = await connectToDatabase();
    const badgeService = new BadgeAwardService(db);

    // Get badge progress without awarding
    const progress = await badgeService.getBadgeProgress(discordId);

    // Separate earned and unearned badges
    const earnedBadges = Object.entries(progress).filter(([_, badge]) => badge.earned);
    const unearnedBadges = Object.entries(progress).filter(([_, badge]) => !badge.earned);

    return NextResponse.json({
      success: true,
      earnedCount: earnedBadges.length,
      totalCount: Object.keys(progress).length,
      earnedBadges: Object.fromEntries(earnedBadges),
      unearnedBadges: Object.fromEntries(unearnedBadges),
      progress: progress
    });

  } catch (error) {
    console.error('Error checking badge eligibility:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
