'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { FaYoutube, FaTwitch, FaServer, FaDiscord, FaCheckCircle } from 'react-icons/fa';

export default function PartnerProgramPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    discordUsername: '',
    platformType: 'youtube',
    platformLink: '',
    followers: '',
    serverInfo: '',
    pluginInterest: '',
    promotionPlans: ''
  });

  const [submitted, setSubmitted] = useState(false);
  const formRef = useRef<HTMLDivElement>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch('/api/submit-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formType: 'partner',
          ...formData,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit form');
      }

      setSubmitted(true);
    } catch (error) {
      console.error('Error submitting form:', error);
      // You might want to show an error message to the user here
    }
  };

  const scrollToForm = () => {
    formRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  };

  // Handle hash in URL for direct navigation
  useEffect(() => {
    if (window.location.hash === '#application-form') {
      setTimeout(() => {
        scrollToForm();
      }, 100);
    }
  }, []);

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-[url('/pattern-dots.svg')] bg-repeat"></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Partner Program</h1>
            <p className="text-xl text-blue-100 md:max-w-3xl mx-auto">
              Få plugins gratis til din platform ved at deltage i vores partnerskabsprogram og vise dem frem for dit publikum
            </p>
          </div>
        </div>
      </section>
      
      {/* Program Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <h2 className="text-3xl font-bold mb-10 text-center text-gray-800">Fordele ved at blive partner</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100 relative overflow-hidden group hover:shadow-lg transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] hover:-translate-y-2">
                <div className="absolute top-0 right-0 w-32 h-32 bg-red-100 rounded-full -mr-16 -mt-16 transition-all duration-700 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-125 group-hover:bg-red-200 group-hover:-rotate-12"></div>
                <div className="relative">
                  <div className="w-16 h-16 bg-red-100 rounded-xl flex items-center justify-center mb-6 text-red-600 group-hover:bg-red-600 group-hover:text-white transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110">
                    <FaYoutube className="w-8 h-8 transition-transform duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-red-600 transition-colors duration-500">Gratis plugins</h3>
                  <p className="text-gray-600 group-hover:text-gray-800 transition-colors duration-500">
                    Få adgang til vores plugins uden beregning til din platform eller server, så du kan vise dem frem for dit publikum.
                  </p>
                </div>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100 relative overflow-hidden group hover:shadow-lg transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] hover:-translate-y-2">
                <div className="absolute top-0 right-0 w-32 h-32 bg-blue-100 rounded-full -mr-16 -mt-16 transition-all duration-700 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-125 group-hover:bg-blue-200 group-hover:-rotate-12"></div>
                <div className="relative">
                  <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6 text-blue-600 group-hover:bg-blue-600 group-hover:text-white transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110">
                    <FaDiscord className="w-8 h-8 transition-transform duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-500">Prioriteret support</h3>
                  <p className="text-gray-600 group-hover:text-gray-800 transition-colors duration-500">
                    Få direkte adgang til vores udviklingsteam med prioriteret support og mulighed for at påvirke fremtidige opdateringer.
                  </p>
                </div>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100 relative overflow-hidden group hover:shadow-lg transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] hover:-translate-y-2">
                <div className="absolute top-0 right-0 w-32 h-32 bg-orange-100 rounded-full -mr-16 -mt-16 transition-all duration-700 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-125 group-hover:bg-orange-200 group-hover:-rotate-12"></div>
                <div className="relative">
                  <div className="w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center mb-6 text-orange-600 group-hover:bg-orange-600 group-hover:text-white transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110">
                    <FaServer className="w-8 h-8 transition-transform duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-orange-600 transition-colors duration-500">Officiel MCDevHub partner status</h3>
                  <p className="text-gray-600 group-hover:text-gray-800 transition-colors duration-500">
                    Bliv fremhævet på vores hjemmeside og sociale medier som officiel MCDevHub partner med link til din platform.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Eligibility Requirements */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-10 text-center text-gray-800">Hvem kan blive partner?</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
              <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 relative overflow-hidden group hover:shadow-xl transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] hover:-translate-y-2">
                <div className="absolute top-0 right-0 w-32 h-32 bg-red-100 rounded-full -mr-16 -mt-16 transition-all duration-700 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-125 group-hover:bg-red-200 group-hover:-rotate-12"></div>
                <h3 className="text-xl font-bold mb-6 text-gray-800 flex items-center relative">
                  <span className="bg-red-100 p-3 rounded-lg mr-4 text-red-600 transition-colors duration-500 group-hover:bg-red-600 group-hover:text-white">
                    <FaYoutube className="w-6 h-6 transition-transform duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110" />
                  </span>
                  Content Creators
                </h3>
                <ul className="space-y-4 relative">
                  <li className="flex items-start group">
                    <FaCheckCircle className="text-green-500 mt-1 mr-3 flex-shrink-0 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                    <p className="text-gray-600 transition-colors duration-300 group-hover:text-gray-800">YouTube-kanal med minimum 500 abonnenter og regelmæssigt Minecraft-indhold</p>
                  </li>
                  <li className="flex items-start group">
                    <FaCheckCircle className="text-green-500 mt-1 mr-3 flex-shrink-0 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                    <p className="text-gray-600 transition-colors duration-300 group-hover:text-gray-800">Twitch-kanal med minimum 200 følgere og regelmæssige Minecraft-streams</p>
                  </li>
                  <li className="flex items-start group">
                    <FaCheckCircle className="text-green-500 mt-1 mr-3 flex-shrink-0 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                    <p className="text-gray-600 transition-colors duration-300 group-hover:text-gray-800">Konsistent aktivitet med mindst 2-3 uploads eller streams om måneden</p>
                  </li>
                  <li className="flex items-start group">
                    <FaCheckCircle className="text-green-500 mt-1 mr-3 flex-shrink-0 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                    <p className="text-gray-600 transition-colors duration-300 group-hover:text-gray-800">Mulighed for at inkludere plugins i dit indhold</p>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 relative overflow-hidden group hover:shadow-xl transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] hover:-translate-y-2">
                <div className="absolute top-0 right-0 w-32 h-32 bg-orange-100 rounded-full -mr-16 -mt-16 transition-all duration-700 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-125 group-hover:bg-orange-200 group-hover:-rotate-12"></div>
                <h3 className="text-xl font-bold mb-6 text-gray-800 flex items-center relative">
                  <span className="bg-orange-100 p-3 rounded-lg mr-4 text-orange-600 transition-colors duration-500 group-hover:bg-orange-600 group-hover:text-white">
                    <FaServer className="w-6 h-6 transition-transform duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] group-hover:scale-110" />
                  </span>
                  Server Ejere
                </h3>
                <ul className="space-y-4 relative">
                  <li className="flex items-start group">
                    <FaCheckCircle className="text-green-500 mt-1 mr-3 flex-shrink-0 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                    <p className="text-gray-600 transition-colors duration-300 group-hover:text-gray-800">Aktiv Minecraft server med minimum 30 daglige spillere</p>
                  </li>
                  <li className="flex items-start group">
                    <FaCheckCircle className="text-green-500 mt-1 mr-3 flex-shrink-0 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                    <p className="text-gray-600 transition-colors duration-300 group-hover:text-gray-800">Serveren har været aktiv i mindst 1 måned</p>
                  </li>
                  <li className="flex items-start group">
                    <FaCheckCircle className="text-green-500 mt-1 mr-3 flex-shrink-0 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                    <p className="text-gray-600 transition-colors duration-300 group-hover:text-gray-800">Mulighed for at reklamere for MCDevHub i server lobby eller lignende</p>
                  </li>
                  <li className="flex items-start group">
                    <FaCheckCircle className="text-green-500 mt-1 mr-3 flex-shrink-0 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                    <p className="text-gray-600 transition-colors duration-300 group-hover:text-gray-800">Vilje til at give feedback på plugins og dele erfaringer</p>
                  </li>
                </ul>
              </div>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl mb-8">
              <p className="text-gray-700 italic text-center">
                Opfylder du ikke helt kravene? Ansøg alligevel! Vi vurderer hver ansøgning individuelt.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Application Form */}
      <section id="application-form" ref={formRef} className="py-16 scroll-mt-24">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">Ansøg om partnerskab</h2>
            
            {submitted ? (
              <div className="bg-green-50 border border-green-200 rounded-xl p-8 text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <FaCheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">Tak for din ansøgning!</h3>
                <p className="text-gray-600 mb-6">
                  Vi har modtaget din ansøgning til partnerprogrammet. Vi gennemgår den så hurtigt som muligt og kontakter dig via den discord, du har angivet.
                </p>
                <p className="text-gray-600 mb-6">
                  Hvis du har spørgsmål i mellemtiden, er du velkommen til at kontakte os via Discord.
                </p>
                <Link 
                  href="https://discord.mcdevhub.dk" 
                  className="inline-block bg-indigo-600 text-white px-6 py-3 rounded-md font-medium hover:bg-indigo-700 transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Join vores Discord
                </Link>
              </div>
            ) : (
              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <form onSubmit={handleSubmit} className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Navn</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        placeholder="Dit fulde navn"
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        placeholder="<EMAIL>"
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="discordUsername" className="block text-sm font-medium text-gray-700 mb-1">Discord Navn</label>
                      <input
                        type="text"
                        id="discordUsername"
                        name="discordUsername"
                        value={formData.discordUsername}
                        onChange={handleChange}
                        required
                        placeholder="Discord navn"
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="platformType" className="block text-sm font-medium text-gray-700 mb-1">Platform Type</label>
                      <select
                        id="platformType"
                        name="platformType"
                        value={formData.platformType}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800"
                      >
                        <option value="youtube">YouTube Kanal</option>
                        <option value="twitch">Twitch Kanal</option>
                        <option value="server">Minecraft Server</option>
                        <option value="other">Andet</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="platformLink" className="block text-sm font-medium text-gray-700 mb-1">Link til din platform</label>
                    <input
                      type="text"
                      id="platformLink"
                      name="platformLink"
                      value={formData.platformLink}
                      onChange={handleChange}
                      required
                      placeholder="f.eks. https://youtube.com/channel/..."
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800"
                    />
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="followers" className="block text-sm font-medium text-gray-700 mb-1">Antal følgere/abonnenter eller daglige spillere</label>
                    <input
                      type="text"
                      id="followers"
                      name="followers"
                      value={formData.followers}
                      onChange={handleChange}
                      required
                      placeholder="Indtast antal følgere, abonnenter eller daglige spillere"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800"
                    />
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="serverInfo" className="block text-sm font-medium text-gray-700 mb-1">Information om din kanal/server</label>
                    <textarea
                      id="serverInfo"
                      name="serverInfo"
                      value={formData.serverInfo}
                      onChange={handleChange}
                      required
                      rows={3}
                      placeholder="Fortæl lidt om din kanal/server, dit indhold, og dit publikum"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800"
                    ></textarea>
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="pluginInterest" className="block text-sm font-medium text-gray-700 mb-1">Hvilke typer plugins er du interesseret i?</label>
                    <textarea
                      id="pluginInterest"
                      name="pluginInterest"
                      value={formData.pluginInterest}
                      onChange={handleChange}
                      required
                      rows={3}
                      placeholder="Beskriv hvilke typer plugins du er interesseret i at vise frem"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800"
                    ></textarea>
                  </div>
                  
                  <div className="mb-8">
                    <label htmlFor="promotionPlans" className="block text-sm font-medium text-gray-700 mb-1">Hvordan planlægger du at promovere vores plugins?</label>
                    <textarea
                      id="promotionPlans"
                      name="promotionPlans"
                      value={formData.promotionPlans}
                      onChange={handleChange}
                      required
                      rows={3}
                      placeholder="Beskriv hvordan du vil vise vores plugins frem på din platform"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800"
                    ></textarea>
                  </div>
                  
                  <div className="text-center">
                    <button 
                      type="submit" 
                      className="inline-block bg-blue-600 text-white px-8 py-4 rounded-lg font-bold hover:bg-blue-700 transition-colors"
                    >
                      Send Ansøgning
                    </button>
                  </div>
                </form>
              </div>
            )}
          </div>
        </div>
      </section>
      
      {/* FAQ Section */}
      <section className="py-24 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-20">
              <h2 className="text-4xl font-bold text-gray-900 mb-4 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Ofte stillede spørgsmål
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Få svar på de mest almindelige spørgsmål om vores partnerprogram
              </p>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {[
                {
                  question: "Hvor lang tid tager ansøgningen?",
                  answer: "Vi behandler ansøgninger inden for 1-2 arbejdsdage. Hvis du ikke har hørt fra os inden for 3 dage, er du velkommen til at <a href='https://discord.mcdevhub.dk' target='_blank' rel='noopener noreferrer' class='text-blue-600 hover:text-blue-700 font-medium underline'>kontakte os via Discord</a>.",
                  icon: "M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                },
                {
                  question: "Er der nogle omkostninger?",
                  answer: "Nej, partnerprogrammet er helt gratis. Vi tilbyder vores plugins gratis til godkendte partnere mod at du viser dem frem på din platform.",
                  icon: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                },
                {
                  question: "Hvor længe varer partnerskabet?",
                  answer: "Det initielle partnerskab varer i 2 uger. Herefter evaluerer vi samarbejdet og kan forlænge det, hvis begge parter er tilfredse med resultaterne.",
                  icon: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                },
                {
                  question: "Kan jeg vælge plugins?",
                  answer: "Ja, du kan angive dine præferencer, og vi vil forsøge at imødekomme dem. I nogle tilfælde kan vi også tilbyde special-udviklede funktioner til din platform.",
                  icon: "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                },
                {
                  question: "Hvad forventes der af mig?",
                  answer: "Vi forventer, at du aktivt viser vores plugins frem på din platform, giver ærlig feedback, og inkluderer links til vores hjemmeside i din beskrivelse eller på din server.",
                  icon: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                }
              ].map((faq, index) => (
                <div 
                  key={index}
                  className="group bg-white p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-blue-100 relative overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative z-10">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-blue-50 group-hover:bg-blue-100 rounded-xl flex items-center justify-center mr-4 transition-colors duration-300">
                        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={faq.icon} />
                        </svg>
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-800 transition-colors duration-300">
                        {faq.question}
                      </h3>
                    </div>
                    <p 
                      className="text-gray-600 group-hover:text-gray-700 leading-relaxed" 
                      dangerouslySetInnerHTML={{ __html: faq.answer }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-blue-900 to-indigo-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-[url('/pattern-grid.svg')] bg-[length:40px_40px]"></div>
        </div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight bg-gradient-to-r from-blue-200 to-indigo-200 bg-clip-text text-transparent">
              Er du klar til at tage din platform til nye højder?
            </h2>
            <p className="text-xl text-blue-100/80 max-w-2xl mx-auto mb-10">
              Bliv en del af vores eksklusive partnerprogram og få adgang til unikke værktøjer, der kan transformere din Minecraft-platform.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-6">
              <button 
                onClick={scrollToForm}
                className="group relative bg-white/10 backdrop-blur-sm border-2 border-white/20 text-white px-8 py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:bg-white/20 hover:shadow-2xl hover:shadow-blue-500/30 hover:-translate-y-1 cursor-pointer"
              >
                <span className="relative z-10 flex items-center justify-center cursor-pointer">
                  Start din ansøgning
                  <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5 group-hover:scale-110 cursor-pointer" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 cursor-pointer"></div>
              </button>
              <Link 
                href="/kontakt" 
                className="group relative bg-transparent border-2 border-white/20 text-white px-8 py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:bg-white/10 hover:shadow-2xl hover:shadow-white/30 hover:-translate-y-1 cursor-pointer"
              >
                <span className="relative z-10 flex items-center justify-center cursor-pointer">
                  Kontakt os
                  <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5 group-hover:scale-110 cursor-pointer" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </span>
                <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 cursor-pointer"></div>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}