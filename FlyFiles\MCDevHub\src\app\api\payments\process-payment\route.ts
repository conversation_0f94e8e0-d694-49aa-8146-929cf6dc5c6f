import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import <PERSON><PERSON> from 'stripe';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2025-04-30.basil' as any,
});

export async function POST(req: NextRequest) {
  try {
    // Check user authentication
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = session.user.id;
    
    // Parse request body
    const { reference, session_id } = await req.json();
    
    // Validate inputs
    if (!reference || !session_id) {
      return NextResponse.json(
        { error: 'Missing required fields', details: 'Reference and session_id are required' },
        { status: 400 }
      );
    }
    
    // Connect to database
    const { db } = await connectToDatabase();
    const depositsCollection = db.collection('deposits');
    const usersCollection = db.collection('users');
    const transactionsCollection = db.collection('transactions');
    
    // Find the deposit record
    const deposit = await depositsCollection.findOne({ 
      reference, 
      userId,
      status: 'pending' 
    });
    
    if (!deposit) {
      return NextResponse.json(
        { error: 'Deposit not found', details: 'Could not find a pending deposit with the provided reference' },
        { status: 404 }
      );
    }
    
    // Check if this deposit has already been processed
    const existingTransaction = await transactionsCollection.findOne({
      userId,
      reference,
      type: 'deposit'
    });
    
    if (existingTransaction) {
      return NextResponse.json(
        { message: 'Deposit already processed', details: 'This deposit has already been processed' },
        { status: 200 }
      );
    }
    
    // Verify the payment with Stripe
    const checkoutSession = await stripe.checkout.sessions.retrieve(session_id);
    
    if (checkoutSession.payment_status !== 'paid') {
      return NextResponse.json(
        { error: 'Payment not completed', details: 'The payment has not been completed successfully' },
        { status: 400 }
      );
    }
    
    if (checkoutSession.client_reference_id !== reference) {
      return NextResponse.json(
        { error: 'Reference mismatch', details: 'The session reference does not match the provided reference' },
        { status: 400 }
      );
    }
    
    // Get the amount from the deposit record
    const amount = deposit.amount;
    
    // Update deposit status
    await depositsCollection.updateOne(
      { _id: deposit._id },
      { $set: { status: 'completed', completedAt: new Date() } }
    );
    
    // Update user balance
    await usersCollection.updateOne(
      { auth_id: userId },
      { $inc: { balance: amount } }
    );
    
    // Add transaction record
    await transactionsCollection.insertOne({
      userId,
      type: 'deposit',
      amount,
      status: 'completed',
      description: 'Balance indsætning via Stripe',
      reference,
      paymentMethod: 'stripe',
      createdAt: new Date(),
    });
    
    return NextResponse.json({ 
      message: 'Betaling gennemført og balance opdateret',
      amount,
      reference 
    });
    
  } catch (error) {
    console.error('Process payment error:', error);
    return NextResponse.json(
      { error: 'Server error', details: 'Could not process payment' },
      { status: 500 }
    );
  }
} 