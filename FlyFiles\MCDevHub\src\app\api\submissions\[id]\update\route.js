import { NextResponse } from 'next/server';
import { MongoClient, ObjectId } from 'mongodb';
import { verify } from 'jsonwebtoken';

export const runtime = 'nodejs';

// MongoDB configuration
if (!process.env.MONGODB_URI) {
  throw new Error('Please add your Mongo URI to .env.local');
}

const uri = process.env.MONGODB_URI;
const options = {};

// Set up MongoDB client with proper connection pooling
let client;
let clientPromise;

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable to preserve
  // the connection across hot reloads
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri, options);
    global._mongoClientPromise = client.connect();
  }
  clientPromise = global._mongoClientPromise;
} else {
  // In production mode, create a new client for each request
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

/**
 * PATCH handler for updating a submission's status
 */
export async function PATCH(request, context) {
  try {
    // Extract id from params using the context
    const { params } = context;
    const id = params.id;
    
    // Get admin info from token
    const token = request.cookies.get('admin_token')?.value;

    if (!token) {
      return NextResponse.json({ 
        success: false, 
        message: 'Unauthorized' 
      }, { status: 401 });
    }

    // Verify token and get admin info
    const JWT_SECRET = process.env.JWT_SECRET;
    if (!JWT_SECRET) {
      throw new Error('JWT_SECRET is not defined');
    }
    
    const decodedToken = verify(token, JWT_SECRET);
    const adminUsername = decodedToken.username;

    // Get update data from request
    const updateData = await request.json();
    
    // Connect to the database
    const client = await clientPromise;
    const db = client.db("codehub");
    
    // Fetch admin info to get discord user ID
    const adminInfo = await db.collection("adminusers").findOne({ username: adminUsername });
    const discordUserId = adminInfo?.discordUserId || '';
    
    // Check if there are already notes before allowing update
    if (updateData.notes !== undefined) {
      const existingSubmission = await db.collection("form_submissions")
        .findOne({ _id: new ObjectId(id) });
      
      // If there are already notes and they're not empty, prevent overwriting
      if (existingSubmission && existingSubmission.notes && existingSubmission.notes.trim() !== '') {
        return NextResponse.json({ 
          success: false, 
          message: 'Andre har allerede tilføjet noter, kan ikke ændres' 
        }, { status: 400 });
      }
      
      // Append admin signature to notes with discord ID for linking
      // Format: "note content\n\n- {adminUsername}||{discordUserId}"
      // The ||{discordUserId} part will be parsed by the frontend to create links
      updateData.notes = `${updateData.notes.trim()}\n\n- ${adminUsername}||${discordUserId}`;
    }
    
    // Define what fields can be updated and prepare update object
    const allowedFields = ['isRead', 'notes'];
    const updateObject = {};
    
    // Add allowed fields to update object
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        updateObject[field] = updateData[field];
      }
    }
    
    // If marking as read, add read timestamp and admin info
    if (updateData.isRead === true) {
      updateObject.readBy = adminUsername;
      updateObject.readAt = new Date().toISOString();
    }
    
    // Update the submission
    const result = await db.collection("form_submissions")
      .updateOne(
        { _id: new ObjectId(id) },
        { $set: updateObject }
      );
    
    // Check if submission was found and updated
    if (result.matchedCount === 0) {
      return NextResponse.json({ 
        success: false, 
        message: 'Indsendelse ikke fundet' 
      }, { status: 404 });
    }
    
    // Return success
    return NextResponse.json({
      success: true,
      message: 'Indsendelse opdateret',
      updates: updateObject
    });
    
  } catch (error) {
    console.error('Error updating submission:', error);
    
    // Return error response
    return NextResponse.json({ 
      success: false, 
      message: 'Fejl ved opdatering af indsendelse',
      error: error instanceof Error ? error.message : 'Ukendt fejl'
    }, { status: 500 });
  }
} 