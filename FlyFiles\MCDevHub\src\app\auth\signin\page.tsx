'use client'

import { signIn, getProviders } from "next-auth/react"
import { useEffect, useState } from "react"
import { FaDiscord } from "react-icons/fa"

interface Provider {
  id: string
  name: string
  type: string
  signinUrl: string
  callbackUrl: string
}

export default function SignIn() {
  const [providers, setProviders] = useState<Record<string, Provider> | null>(null)

  useEffect(() => {
    const setUpProviders = async () => {
      const response = await getProviders()
      setProviders(response)
    }
    setUpProviders()
  }, [])

  if (!providers) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-2xl shadow-xl">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Log ind
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Log ind med din Discord konto for at fortsætte
          </p>
        </div>
        
        <div className="mt-8 space-y-4">
          {Object.values(providers).map((provider: Provider) => (
            <div key={provider.name}>
              <button
                onClick={() => signIn(provider.id, { callbackUrl: '/' })}
                className="w-full flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-[#5865F2] hover:bg-[#4752c4] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#5865F2] transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-[1.02]"
              >
                <FaDiscord className="mr-3 h-5 w-5" />
                Log ind med {provider.name}
              </button>
            </div>
          ))}
        </div>
        
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Ved at logge ind accepterer du vores vilkår og betingelser
          </p>
        </div>
      </div>
    </div>
  )
}
