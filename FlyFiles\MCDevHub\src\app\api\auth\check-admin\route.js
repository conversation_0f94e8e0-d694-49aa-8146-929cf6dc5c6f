import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';

export async function GET() {
  try {
    // Get the current user from NextAuth
    const session = await auth();

    if (!session?.user) {
      console.log('No user session found');
      return NextResponse.json({ isAdmin: false }, { status: 200 });
    }

    // Get the Discord ID from the user
    const discordUserId = session.user.id;

    console.log('Checking admin status for Discord ID:', discordUserId);

    if (!discordUserId) {
      console.log('No Discord ID found in user');
      return NextResponse.json({ isAdmin: false }, { status: 200 });
    }
    
    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // First, let's log all admin users to see what's in the database
    const allAdmins = await db.collection('adminusers').find({}).project({ username: 1, discordUserId: 1 }).toArray();
    console.log('All admin users in database:', allAdmins);
    
    // Try multiple ways to find the admin user

    // Method 1: Direct match
    let adminUser = await db.collection('adminusers').findOne({ discordUserId });
    if (adminUser) {
      console.log('Admin found with direct match');
    }
    
    // Method 2: String comparison
    if (!adminUser) {
      console.log('Admin not found with direct match, trying string comparison');
      adminUser = await db.collection('adminusers').findOne({ discordUserId: discordUserId.toString() });
      if (adminUser) {
        console.log('Admin found with string comparison');
      }
    }
    
    // Method 3: If the ID is a string in the database but a number in the auth, try to convert
    if (!adminUser) {
      console.log('Trying with numeric comparison');
      try {
        const numericId = parseFloat(discordUserId);
        if (!isNaN(numericId)) {
          adminUser = await db.collection('adminusers').findOne({ discordUserId: numericId });
          if (adminUser) {
            console.log('Admin found with numeric comparison');
          }
        }
      } catch (e) {
        console.log('Error in numeric comparison:', e);
      }
    }
    
    // Method 4: Case-insensitive regex as last resort
    if (!adminUser) {
      console.log('Admin not found with prior methods, trying case-insensitive regex');
      try {
        adminUser = await db.collection('adminusers').findOne({ 
          discordUserId: { $regex: new RegExp(`^${discordUserId}$`, 'i') } 
        });
        if (adminUser) {
          console.log('Admin found with regex comparison');
        }
      } catch (e) {
        console.log('Error in regex comparison:', e);
      }
    }
    
    // Method 5: Direct field match if the user ID might be in a different field
    if (!adminUser) {
      console.log('Trying to match directly on Discord field');
      adminUser = await db.collection('adminusers').findOne({
        $or: [
          { discord: discordUserId },
          { discordId: discordUserId },
          { discord_id: discordUserId },
          { discord_user_id: discordUserId }
        ]
      });
      if (adminUser) {
        console.log('Admin found with direct field match');
      }
    }
    
    console.log('Final admin user found status:', adminUser ? 'Yes' : 'No');
    if (adminUser) {
      console.log('Admin username:', adminUser.username);
    }
    
    // If we found a match, update the user record to ensure the correct discordUserId format
    if (adminUser && discordUserId && adminUser.discordUserId !== discordUserId) {
      console.log('Updating admin record to ensure consistent ID format');
      try {
        await db.collection('adminusers').updateOne(
          { _id: adminUser._id },
          { $set: { discordUserId: discordUserId } }
        );
      } catch (e) {
        console.log('Error updating admin record:', e);
      }
    }

    return NextResponse.json({
      isAdmin: !!adminUser,
      discordId: discordUserId,
      debug: {
        adminUserFound: !!adminUser,
        adminUsername: adminUser?.username || null,
        metadataSub: session.user.user_metadata?.sub || null,
        metadataProviderId: session.user.user_metadata?.provider_id || null
      }
    }, { status: 200 });
    
  } catch (error) {
    console.error('Error checking admin status:', error);
    return NextResponse.json({ 
      isAdmin: false, 
      error: 'Internal server error',
      errorMessage: error.message 
    }, { status: 500 });
  }
} 