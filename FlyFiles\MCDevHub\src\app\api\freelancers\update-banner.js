import { getSession } from 'next-auth/react';
import clientPromise from '@/lib/mongodb';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const session = await getSession({ req });
  if (!session || !session.user) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const { bannerUrl } = req.body;
  if (!bannerUrl) {
    return res.status(400).json({ error: 'Missing bannerUrl' });
  }

  const db = (await clientPromise).db();
  await db.collection('adminusers').updateOne(
    { discordUserId: session.user.discordUserId },
    { $set: { bannerImage: bannerUrl } }
  );

  return res.status(200).json({ success: true });
}
