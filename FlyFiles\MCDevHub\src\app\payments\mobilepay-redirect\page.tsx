'use client';

import React, { useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { FaMoneyBillWave, FaArrowLeft } from 'react-icons/fa';

function MobilePayContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const reference = searchParams.get('reference');

  // Redirect to profile after 10 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      router.push('/profile?tab=transactions&deposit_pending=true');
    }, 10000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50 pt-16">
      <div className="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden p-6">
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <FaMoneyBillWave className="text-blue-500 text-2xl" />
          </div>
        </div>
        
        <h1 className="text-xl font-bold text-center text-gray-900 mb-2">
          MobilePay Integration
        </h1>
        
        <div className="text-center mb-6">
          <p className="text-gray-600 mb-4">
            MobilePay integrationen er under udvikling. I en rigtig implementation ville du blive omdirigeret til MobilePay for at gennemføre din betaling.
          </p>
          
          <div className="p-4 bg-blue-50 rounded-lg mb-4 text-sm text-blue-700">
            <p className="font-medium">Reference</p>
            <p className="font-mono text-xs mt-1 break-all">{reference || 'Ingen reference'}</p>
          </div>
          
          <p className="text-gray-500 text-sm">
            Du vil blive omdirigeret til din profil om få sekunder...
          </p>
        </div>
        
        <div className="flex justify-center">
          <button
            onClick={() => router.push('/profile?tab=transactions')}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <FaArrowLeft className="mr-2" />
            Tilbage til profil
          </button>
        </div>
      </div>
    </div>
  );
}

export default function MobilePayRedirectPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <MobilePayContent />
    </Suspense>
  );
}