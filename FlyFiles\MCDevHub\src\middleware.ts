import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(req: NextRequest) {
  // Don't run middleware for static assets and certain paths
  if (
    req.nextUrl.pathname.startsWith('/_next') ||
    req.nextUrl.pathname.includes('/api/') ||
    req.nextUrl.pathname.startsWith('/static')
  ) {
    return NextResponse.next()
  }

  // Handle case-insensitive developer URLs
  const developerMatch = req.nextUrl.pathname.match(/^\/developers\/([^\/]+)$/);
  if (developerMatch) {
    const requestedUsername = developerMatch[1];

    // For now, skip the async fetch in middleware to avoid edge runtime issues
    // This functionality can be moved to the page component if needed
    console.log(`Developer URL accessed: ${requestedUsername}`);
  }

  // Special handling for logout requests
  const hasLogoutParam = req.nextUrl.searchParams.has('logout');

  if (hasLogoutParam) {
    console.log('Middleware: Detected logout request, redirecting to home');
    // Create a redirect response to remove the logout parameter
    const redirectUrl = new URL('/', req.url);
    return NextResponse.redirect(redirectUrl);
  }

  return NextResponse.next()
}

// Add matcher to specify routes that should run middleware
export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)'],
}