import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

export async function GET(request) {
  try {
    // Get the Discord ID from query parameters
    const { searchParams } = new URL(request.url);
    const discordId = searchParams.get('discordId');
    
    if (!discordId) {
      return NextResponse.json(
        { message: 'Discord ID er påkrævet' },
        { status: 400 }
      );
    }
    
    // Connect to database
    const { db } = await connectToDatabase();
    
    // Fetch all approved products by this Discord user ID
    const products = await db.collection('products')
      .find({ 
        discordUserId: discordId,
        status: 'approved' 
      })
      .sort({ createdAt: -1 })
      .toArray();
    
    return NextResponse.json(products);
    
  } catch (error) {
    console.error('Error fetching user products:', error);
    return NextResponse.json(
      { message: 'Der opstod en fejl ved hentning af brugerens produkter: ' + error.message },
      { status: 500 }
    );
  }
} 