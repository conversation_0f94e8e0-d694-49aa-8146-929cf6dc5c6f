import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { auth } from '@/lib/auth';

export const runtime = 'nodejs'; // Use Node.js runtime

export async function GET(request) {
  try {
    // Get the user's Discord ID from their NextAuth session
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Du er ikke logget ind' },
        { status: 401 }
      );
    }

    // Get Discord ID from session
    const discordUserId = session.user.id;
    
    if (!discordUserId) {
      return NextResponse.json(
        { success: false, message: 'Discord ID ikke fundet' },
        { status: 400 }
      );
    }
    
    // Connect to MongoDB
    const { db } = await connectToDatabase();
    
    // Look for an admin user with this Discord ID - try both string and number formats
    let adminUser = await db.collection('adminusers').findOne({ discordUserId });

    // If not found as string, try as number
    if (!adminUser) {
      adminUser = await db.collection('adminusers').findOne({
        discordUserId: parseInt(discordUserId)
      });
    }

    if (!adminUser) {
      return NextResponse.json(
        { success: false, message: 'Ingen admin bruger fundet med dette Discord ID' },
        { status: 404 }
      );
    }
    
    // Create safe user object (excluding sensitive data)
    const user = {
      username: adminUser.username,
      admintype: adminUser.admintype,
      description: adminUser.description || '',
      discordUserId: adminUser.discordUserId,
      email: adminUser.email || session.user.email, // Use MongoDB email if available, fallback to Discord email
      githubUsername: adminUser.githubUsername || '',
      youtubeUrl: adminUser.youtubeUrl || ''
    };
    
    // Return user info
    return NextResponse.json({
      success: true,
      user
    });
    
  } catch (error) {
    console.error('Error getting admin data by Discord ID:', error);
    
    // Return error response
    return NextResponse.json(
      { success: false, message: 'Der opstod en fejl ved hentning af admin data' },
      { status: 500 }
    );
  }
} 